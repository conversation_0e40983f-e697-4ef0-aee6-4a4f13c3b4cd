<template>
	<view class="main">
		<view class="mSearch-box">
			<mSearch
				class="mSearch-input-box"
				:mode="2"
				button="inside"
				:placeholder="'请输入你的问题描述或者代码'"
				@search="onSearch(false)"
				@confirm="onSearch(false)"
				@input="inputChange"
				v-model="parmes.keyword"
			></mSearch>
			<view :class="[{ colors: parmes.productList.length > 0 || parmes.type.length > 0 }, 'filter-box']" @click.stop="handleFilter">
				筛选
				<text class="iconfont iconshaixuan"></text>
				<view class="crice" v-if="parmes.productList.length > 0 || parmes.type.length > 0"></view>
			</view>
			<view class="mSearch-totast" v-if="totastList.length > 0 && issearch && parmes.keyword">
				<view
					class="mSearch-totast-item"
					@tap.stop="(issearch = false), (totastList = []), (parmes.keyword = item.name), onSearch()"
					v-for="(item, index) in totastList"
					:key="index"
				>
					{{ item.name }}
				</view>
			</view>
		</view>

		<!-- 故障列表 -->
		<view class="list-item" v-for="(item, index) in listData" :key="index" @tap.stop="toDeatails(item.id)">
			<view class="des-title">{{ item.title ? item.title : '本印猫' }}</view>
			<view class="des-class">
				<view class="des-class-title">知识库类型</view>
				<view class="des-card-list">
					<!-- <view class="des-card-card" v-for="(items,indexs) in item.type" :key="indexs"> -->
					<view class="des-card-card">
						{{ item.type.label }}
					</view>
				</view>
			</view>
			<view class="des-class des-boder">
				<view class="des-class-title">适用机型</view>
				<view class="des-card-list">
					<view class="des-card-card" v-for="(items, indexs) in item.models" :key="indexs">
						{{ items }}
					</view>
				</view>
			</view>
			<view class="des-totast" v-if="item.hasCodeExplain || item.hasText || item.hasVideo">
				<image class="des-img" v-if="item.hasCodeExplain" src="@/static/images/learn/icon1.png" mode=""></image>
				<image class="des-img" v-if="item.hasText" src="@/static/images/learn/icon2.png" mode=""></image>
				<image class="des-img" v-if="item.hasVideo" src="@/static/images/learn/icon3.png" mode=""></image>
			</view>
		</view>
		<view class="stutas" v-if="isStutas && listData.length > 0">{{ stutas }}</view>
		<!-- 使用帮助 -->
		<!-- 	<view class="help" @tap.stop="toHelp">
			使用
			<br />
			帮助
		</view> -->
		<!-- 无商品展示 -->
		<rf-empty class="no-listData" :info="'暂无知识库信息'" v-if="listData.length === 0 && !loading"></rf-empty>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
		<!-- 回到顶部 -->
		<rf-back-top :scrollTop="scrollTop"></rf-back-top>
	</view>
</template>

<script>
import { engineerPage, learnWords, getCurrentModel } from '@/api/order.js';
import { pageview } from '@/api/login';

import { shopInfo } from '@/api/index';
import mSearch from '@/components/rf-search/rf-search.vue';
export default {
	components: {
		mSearch
	},
	data() {
		return {
			menuInfo: this.$getMenu.menuInfo,
			issearch: false,
			loading: true,
			isFrist: true,
			totastList: [], //是否筛选
			isStutas: false, // 是否到底
			stutas: '',
			listData: [],
			// 总数
			total: 0,
			parmes: {
				pageNumber: 1, // 分页参数
				pageSize: 10, // 每页大小
				title: '', //热词/描述/错误代码
				productList: [], //	机型筛选列表
				type: [] //知识库类型
			},
			userInfo: {},
			scrollTop: ''
		};
	},
	async onShow() {
		this.initParms();
		if (uni.getStorageSync('learnKeyWords')) {
			this.parmes.keyword = uni.getStorageSync('learnKeyWords');
		}
		if (uni.getStorageSync('learnParmes')) {
			this.parmes.productList = uni.getStorageSync('learnParmes').productList;
			this.parmes.type = uni.getStorageSync('learnParmes').type;
		}
		this.initData();
	},
	created() {},
	// 下拉刷新
	onPullDownRefresh() {
		this.initParms();
		this.getLearnPage();
		uni.stopPullDownRefresh();
	},
	// 上拉加载
	onReachBottom() {
		this.stutas = '加载中';
		this.isStutas = true;
		this.parmes.pageNumber++;
		if ((this.parmes.pageNumber - 1) * this.parmes.pageSize >= this.total) {
			this.stutas = '—— 到底啦 ——';
			return;
		}
		this.getLearnPage();
	},
	// 页面滚动
	onPageScroll(e) {
		this.scrollTop = e.scrollTop;
	},
	methods: {
		onTabItemTap(args) {
			this.$http
				.post(`${pageview}`, {
					params: args.pagePath.split('?')[1],
					path: '/' + args.pagePath.split('?')[0],
					previousId: uni.getStorageSync('previousId')
				})
				.then((res) => {
					uni.setStorageSync('previousId', res.data);
				});
		},
		async initData() {
			this.getLearnPage();
		},
		// 搜索
		async onSearch() {
			this.loading = true;
			this.isFrist = true;
			this.issearch = false;
			this.parmes.pageNumber = 1;
			this.parmes.pageSize = 10;
			uni.setStorageSync('learnKeyWords', this.parmes.keyword);
			this.getLearnPage();
		},
		// 输入中
		async inputChange(e) {
			this.issearch = true;
			this.$http
				.get(learnWords, {
					name: this.parmes.keyword
				})
				.then((res) => {
					this.totastList = res.data;
				});
		},
		// 帮助
		async toHelp() {
			if (uni.getStorageSync('accessToken')) {
				let route = `/pages/help/learn`;
				this.$mRouter.push({
					route
				});
			} else {
				this.tologinFn();
			}
		},
		// 分页列表查询
		async getLearnPage() {
			await this.$http.post(`${engineerPage}`, this.parmes).then((res) => {
				this.loading = false;
				this.total = res.data.total;
				if (!uni.getStorageSync('accessToken')) {
					this.listData = res.data.rows;
					return;
				}
				if (this.isFrist) {
					this.listData = res.data.rows;
					this.isFrist = false;
				} else {
					this.listData = [...this.listData, ...res.data.rows];
					this.stutas = '上拉加载更多';
					this.listData.length < this.total ? (this.isStutas = true) : (this.isStutas = false);
				}
				// this.getCurrentModelFn();
			});
		},
		// 筛选
		async handleFilter() {
			if (uni.getStorageSync('accessToken')) {
				let route = `/pages/help/classify`;
				this.$mRouter.push({
					route
				});
			} else {
				this.tologinFn();
			}
		},
		// 初始化参数
		async initParms() {
			this.loading = true;
			this.isFrist = true;
			this.scrollTop = 0;
			this.stutas = '';
			this.parmes = {
				pageNumber: 1, // 分页参数
				pageSize: 10 // 每页大小
			};
		},
		// 去详情页
		toDeatails(id) {
			if (uni.getStorageSync('accessToken')) {
				uni.navigateTo({
					url: `/pages/help/detail?id=${id}`
				});
			} else {
				this.tologinFn();
			}
		},
		tologinFn() {
			this.$mHelper.toast('您还未登录');
			setTimeout(() => {
				uni.navigateTo({
					url: '/pages/user/logintype'
				});
			}, 800);
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	width: 100%;
	height: 100%;
	min-height: 100vh;
	padding: 0 24rpx;
	padding-top: 38px;
	padding-bottom: 27rpx;
	background-color: #f2f2f2;
	position: relative;

	.mSearch-box {
		width: 100%;
		padding: 10rpx 24rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		background-color: #f2f2f2;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 100;

		.mSearch-input-box {
			flex: 1;
		}

		.filter-box {
			width: 100rpx;
			height: 80rpx;
			margin-left: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #5c5757;
			position: relative;

			&.colors {
				color: #fd4e19;
			}

			.iconfont {
				font-size: 40rpx;
			}

			.crice {
				width: 12rpx;
				height: 12rpx;
				border-radius: 6rpx;
				background-color: red;
				position: absolute;
				top: 21rpx;
				right: 0rpx;
			}
		}

		.mSearch-totast {
			min-height: 30rpx;
			max-height: 150rpx;
			padding: 12rpx;
			box-sizing: border-box;
			background-color: #fff;
			width: 94%;
			position: absolute;
			top: 50px;
			left: 50%;
			transform: translate(-50%);
			border-radius: 12rpx;
			box-shadow: 5px 5px 5px #00000014, 5px -5px 5px #00000014, -5px 5px 5px #00000014, -5px -5px 5px #00000014;
			// overflow: hidden;
			overflow-y: scroll;
		}
	}

	.list-item {
		width: 100%;
		padding: 26rpx;
		box-sizing: border-box;
		margin-top: 27rpx;
		border-radius: 20rpx;
		background-color: #ffffff;
		display: flex;
		flex-direction: column;

		.des-title {
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #343434;
		}

		.des-class {
			display: flex;
			align-items: center;
			padding: 12rpx 0;
			box-sizing: border-box;

			.des-class-title {
				font-size: 27rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #9a9a9a;
				margin-right: 26rpx;
			}

			.des-card-list {
				flex: 1;
				height: 70rpx;
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				overflow: hidden;

				.des-card-card {
					height: 46rpx;
					padding: 0 16rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 27rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #535353;
					background: #f2f2f2;
					border-radius: 3rpx;
					margin: 10rpx;
				}
			}

			&.des-boder {
				border-top: 1rpx solid #f1f1f1;
			}
		}

		.des-totast {
			width: 100%;
			height: 32rpx;
			display: flex;
			align-items: center;
			justify-content: flex-end;

			.des-img {
				width: 34rpx;
				height: 32rpx;
				margin-left: 16rpx;
				filter: grayscale(100%);
			}
		}
	}

	.stutas {
		height: 80upx;
		line-height: 80upx;
		text-align: center;
		color: #ccc;
		font-size: 24upx;
		width: 100%;
	}

	.help {
		width: 80rpx;
		height: 80rpx;
		line-height: 30rpx;
		padding-top: 10rpx;
		background: linear-gradient(180deg, #f89030, #fd4e19);
		box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(203, 51, 0, 0.3);
		border-radius: 50%;
		font-size: 22rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #ffffff;
		text-align: center;
		position: fixed;
		bottom: 230rpx;
		right: 36rpx;
	}

	.no-listData {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}
}
</style>
