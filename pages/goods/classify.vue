<template>
	<view class="page" ref="page">
		<view class="show_main">
			<view class="filter-top"></view>
			<scroll-view class="uni-select__selector-scroll" scroll-y>
				<view class="scroll-list">
					<view class="list" v-for="(item, index) in infoData" :key="index">
						<view class="title">
							<view class="title_boder"></view>
							{{ item.name }}
						</view>
						<view class="classify">
							<view class="list-item" v-for="(list, index1) in item.listData" :key="index1"
								:class="list.isCheck ? 'active' : ''" @click.stop="handleCheck(list)">
								{{ list.name }}
							</view>
						</view>
					</view>
				</view>
			</scroll-view>

			<view class="footer-btn">
				<button class="del" @click.stop="handleCancel">
					<text>重置</text>
				</button>
				<button class="btn action" @click.stop="handleSave">
					<text class="action">确认</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		classifyTag,
		shopInfo
	} from "@/api";
	export default {
		data() {
			return {
				getMenu: this.$getMenu.menuInfo,
				currentIndex: 0,
				infoData: [],
				customerId: "",
				classifyId: "",
			};
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.loadData(this.classifyId);
			uni.stopPullDownRefresh();
		},

		async onLoad(params) {
			// this.$mHelper.loadVerify();
			const {
				id,
				name
			} = params;
			// if (
			//   this.infoData.length == 0 ||
			//   this.customerId != uni.getStorageSync("customerId")
			// ) {
			this.customerId = uni.getStorageSync("customerId");
			this.classifyId = id;
			this.loadData(this.classifyId);
			// }
			uni.setNavigationBarTitle({
				title: name,
			});
		},
		methods: {
			async loadData(id) {

				let res = await this.$http.get(`${classifyTag}`, {
					id,
				});
				this.infoData = [];
				let select = [];
				let select1 = [];
				if (
					uni.getStorageSync("classify") &&
					uni.getStorageSync("classify").length > 0
				) {
					uni.getStorageSync("classify").map((ele) => {
						console.log(ele);
						if (ele.values && !ele.tagId) {
							select = ele.values;
						}
						if (ele.values && ele.tagId) {
							select1.push(ele);
						}
					});
				}

				console.log(select);
				console.log(select1);
				if (res.data.models && res.data.models.length > 0) {
					this.infoData = res.data.models.map((item) => {
						return {
							name: item.brandName,
							listData: item.models.map((tag) => {
								return {
									name: tag.modelName,
									productId: tag.productId,
									isCheck: select.includes(tag.productId) ? true : false,
								};
							}),
						};
					});
				}
				let arr = res.data.tagList.map((item) => {
					console.log(item);
					let select2 = [];
					if (select1 && select1.length > 0) {
						select1.map((el) => {
							if (el.tagId == item.id) {
								select2 = el.values;
							}
						});
					}
					return {
						...item,
						listData: item.value.map((tag) => {
							return {
								name: tag,
								isCheck: select2.includes(tag) ? true : false,
							};
						}),
					};
				});
				this.infoData = this.infoData.concat(arr);

				console.log(this.infoData, "66666666666");
			},
			hide() {
				setTimeout(() => {
					this.$emit("hide");
				}, 100);
			},
			handleCheck(list) {
				console.log(list);
				list.isCheck = !list.isCheck;
			},
			handleCancel() {
				console.log("cancel");
				uni.removeStorageSync("classify");

				uni.switchTab({
					url: "/pages/home/<USER>",
				});
			},
			async handleSave() {
				let verify = await this.$http.post(this.$mHelper.verifyAccessToken);
				if (verify.data === 2) {
					this.$mHelper.toast("网络异常，请稍后再试...");
					this.$mHelper.relogin();
					return false;
				}
				let data = this.getTags();
				if (data.length > 0) {
					uni.setStorageSync("classify", data);
				} else {
					uni.removeStorageSync("classify");
				}
				uni.switchTab({
					url: "/pages/home/<USER>",
				});
			},
			closeA() {
				this.$emit("hide");
			},
			getTags() {
				let arr = [];
				this.infoData.map((item) => {
					item.listData.map((tag) => {
						if (tag.isCheck) {
							if (tag.productId) {
								arr.push({
									tagId: item.id,
									value: tag.productId,
								});
							} else {
								arr.push({
									tagId: item.id,
									value: tag.name,
								});
							}
						}
					});
				});
				let list = arr.reduce((result, item) => {
					let existingEntry = result.find((entry) => entry.tagId === item.tagId);

					if (existingEntry) {
						existingEntry.values.push(item.value);
					} else {
						result.push({
							tagId: item.tagId,
							values: [item.value],
						});
					}
					return result;
				}, []);
				return list;
			},
			clearTag() {
				this.infoData = [];
			},
		},
	};
</script>

<style scoped lang="scss">
	.show_popup {
		width: 100%;
		height: 100vh;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 100000;
		background-color: rgba(0, 0, 0, 0.5);
	}

	.show_main {
		width: 100%;
		padding: 0 22rpx;
		padding-bottom: 110rpx;
		box-sizing: border-box;
		background-color: #fff;
		float: right;

		.filter-top {
			width: 90%;
			background-color: #fff;
			margin: auto;
		}

		.uni-select__selector-scroll {
			width: 100%;

			.scroll-list {
				height: 100vh;
				overflow: hidden;
				overflow-y: auto;
				padding-bottom: 100rpx;
				box-sizing: border-box;
			}

			.list {
				position: relative;
				width: 100%;
				border-radius: 6rpx;
				margin-bottom: 26rpx;

				.title {
					width: 100%;
					font-size: 29rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #0f0f0f;
					line-height: 39rpx;
					text-align: left;
					display: flex;
					align-items: center;

					.title_boder {
						width: 6rpx;
						height: 20rpx;
						background: linear-gradient(to bottom,
								#e5452f 0%,
								#ee822f 100%) !important;
						margin-right: 12rpx;
						border-radius: 4rpx;
					}
				}

				.classify {
					display: flex;
					justify-content: space-between;
					align-items: center;
					flex-wrap: wrap;

					.list-item {
						box-sizing: border-box;
						width: 48%;
						height: 72rpx;
						line-height: 72rpx;
						text-align: center;

						background: #f6f6f6;
						border-radius: 36rpx;
						margin-top: 27rpx;

						&.active {
							color: #fff;
							background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
						}
					}
				}
			}
		}

		.footer-btn {
			width: 100%;
			height: 100rpx;
			padding: 0 22rpx;
			padding-bottom: 44rpx;
			box-sizing: border-box;
			position: fixed;
			right: 0;
			bottom: 0;
			z-index: 10;
			display: flex;
			align-items: center;
			background-color: #ffffff;

			.del {
				display: flex;
				align-items: center;
				width: 50%;
				height: 75rpx;
				color: #fff;
				background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
				border-radius: 37px 0px 0px 37px;
				text-align: center;
				justify-content: center;

				text {
					padding: 10rpx 30rpx;
					font-size: 31rpx;
					font-weight: bold;
				}
			}

			.btn {
				display: flex;
				align-items: center;
				width: 50%;
				height: 75rpx;
				color: #c0c0c0;
				border: 2rpx solid #c0c0c0;
				border-radius: 0px 37px 37px 0px;
				text-align: center;
				justify-content: center;

				text {
					font-size: 31rpx;
					font-weight: bold;
				}

				&.action {
					background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
					background-color: $base;
					color: #ffffff;
					border: 2rpx solid #ffffff;
					color: #fff;
					border: none;
				}
			}
		}
	}
</style>