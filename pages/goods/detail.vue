<template>
	<view class="page" @click="isMore = false" :class="isShow ? 'fixed' : ''">
		<view class="goods-head" :style="'background:rgba(255,255,255,' + PageScrollTop / 100 + ')'">
			<!-- 返回 -->
			<view class="back" @click="onBack">
				<view class="back-one" :class="{ action: PageScrollTop > 120 }">
					<text></text>
				</view>
			</view>
			<!-- tab切换 -->
			<!-- <view class="head-tab" v-if="PageScrollTop > 120">
        <view class="tab" :class="{ action: TabShow === 0 }" @click="onTab(0)">
          <text>商品</text>
          <text class="line"></text>
        </view>
        <view class="tab" :class="{ 'action': TabShow === 1 }" @click="onTab(1)">
          <text>评价</text>
          <text class="line"></text>
        </view>
        <view class="tab" :class="{ action: TabShow === 2 }" @click="onTab(2)">
          <text>详情</text>
          <text class="line"></text>
        </view>
      </view> -->
			<!-- 分享更多 -->
			<!-- <view class="share-more">
        <view class="share-more-one" :class="{ action: PageScrollTop > 120 }">
          <view class="list">
            <text class="iconfont icon-share"></text>
          </view>
          <view class="list" @click.stop="isMore = !isMore">
            <text class="iconfont icon-diandian"></text>
          </view>
        </view>
        <view class="mroe-list" v-show="isMore">
          <navigator class="list">
            <view class="icon">
              <text class="iconfont icon-xiaoxi"></text>
            </view>
            <view class="title">
              <text>消息</text>
            </view>
          </navigator>
          <navigator open-type="switchTab" url="/pages/home/<USER>" class="list">
            <view class="icon">
              <text class="iconfont icon-home"></text>
            </view>
            <view class="title">
              <text>首页</text>
            </view>
          </navigator>
          <navigator class="list">
            <view class="icon">
              <text class="iconfont icon-guanzhu"></text>
            </view>
            <view class="title">
              <text>我的关注</text>
            </view>
          </navigator>
          <navigator class="list">
            <view class="icon">
              <text class="iconfont icon-zuji"></text>
            </view>
            <view class="title">
              <text>浏览记录</text>
            </view>
          </navigator>
        </view>
      </view> -->
		</view>
		<!-- banner，标题 -->
		<view class="banner-title">
			<!-- banner -->
			<view class="banner">
				<swiper class="screen-swiper round-dot" indicator-dots="true" circular="true" autoplay="true" interval="5000" duration="500" indicator-active-color="#ffffff">
					<swiper-item v-for="(item, index) in details.picsUrl" :key="index" @click="showImageListFn(details.picsUrl, index)">
						<image :src="item.url" mode="aspectFill"></image>
						<!-- <video src="{{item.url}}" autoplay loop muted show-play-btn="{{false}}" controls="{{false
            }}" objectFit="cover" wx:if="{{item.type == 'video'}}"></video> -->
					</swiper-item>
				</swiper>
			</view>
			<!-- 价格 -->
			<!-- <view class="price-info" v-show="type == 0">
        <view class="price">
          <text class="min">￥</text>
          <text class="max">99</text>
          <text class="min">.00</text>
        </view>
        <view class="info">
          <view class="list" @click="onDepreciate">
            <text class="iconfont icon-jiangjia"></text>
            <text>降价通知</text>
          </view>
          <view class="list" @click="onAttention">
            <text class="iconfont" :class="AttentionShow === 0 ? 'icon-guanzhu-off' : 'icon-guanzhu-on action'"></text>
            <text>{{ AttentionShow === 0 ? '关注' : '已关注' }}</text>
          </view>
        </view>
      </view> -->
			<!-- 限时抢购 -->
			<!-- <view class="flash-price" v-show="type == 1">
        <view class="price-item">
          <view class="icon-item">
            <text class="iconfont icon-flash-sale"></text>
          </view>
          <view class="price">
            <view class="current-price">
              <text class="min">￥</text>
              <text class="max">99</text>
              <text class="min">.00</text>
            </view>
            <view class="original-price">
              <text>￥149.00</text>
            </view>
          </view>
          <view class="tag">
            <text class="iconfont icon-flash-naozhong"></text>
          </view>
        </view>
        <view class="time-item">
          <view class="title">
            <text>距结束还剩：</text>
          </view>
          <view class="time">
            <text class="num">02</text>
            <text class="dot">:</text>
            <text class="num">46</text>
            <text class="dot">:</text>
            <text class="num">52</text>
          </view>
        </view>
      </view> -->
			<!-- 标题 -->
			<!-- <view class="goods-title">
        <text>美连诚雪纺连衣裙 2020新款女夏裙子波点气质沙滩裙仙气时尚女装休闲衣服大码女装 白底红点 M</text>
      </view> -->
			<!-- 开通会员 -->
			<!-- <view class="dredge-vip">
        <view class="title">
          <text class="iconfont icon-vip"></text>
          <text>
            开通年卡会员预计估算优惠
            <text class="col">15.37</text>
            元
          </text>
        </view>
        <view class="dredge">
          <text>立即</text> 
          <text>开通</text>
        </view>
      </view> -->
		</view>

		<view class="goods-price" v-if="details.miniPrice || currentChoose.sku.saleUnitPrice">
			<view class="price">
				<text>￥</text>
				<text>{{ currentChoose.sku ? currentChoose.sku.saleUnitPrice : details.miniPrice + ' ' }}</text>
				<text v-if="!currentChoose.sku" style="font-size: 24rpx">起</text>
			</view>
			<view class="title">{{ details.name }}</view>
		</view>
		<!-- 优惠积分 -->
		<!-- <view class="goods-discounts">
      <view class="list">
        <view class="title">积分</view>
        <view class="content">
          <text>购买本商品可获得100积分</text>
        </view>
        <view class="more">
          <text class="iconfont icon-more"></text>
        </view>
      </view>
      <view class="list" @click="$refs['GoodsServe'].show()">
        <view class="title">服务</view>
        <view class="content">
          <view class="serve">
            <text class="iconfont icon-baozheng"></text>
            <text>退款保证</text>
          </view>
          <view class="serve">
            <text class="iconfont icon-baozheng"></text>
            <text>物流配送</text>
          </view>
        </view>
        <view class="more">
          <text class="iconfont icon-more"></text>
        </view>
      </view>
      <view class="list" @click="$refs['GoodsCoupon'].show()">
        <view class="title">领券</view>
        <view class="content">
          <view class="coupon-list">
            <view>满19减5</view>
          </view>
          <view class="coupon-list">
            <view>满19减5</view>
          </view>
        </view>
        <view class="more">
          <text class="iconfont icon-more"></text>
        </view>
      </view>
    </view> -->
		<!-- 属性规格 -->
		<view class="goods-discounts">
			<view class="list" v-if="details.adapterDevice && details.adapterDevice.length !== 0">
				<view class="title">适用</view>
				<view class="content content-list">
					<view class="content-jixin" v-for="(item, index) in details.adapterDevice" :key="index">
						{{ item }}
					</view>
				</view>
			</view>
			<view class="list" @click="$refs['GoodsAttr'].show(1)">
				<view class="title">已选</view>
				<view class="content">
					<text v-if="currentChoose.attr">{{ currentChoose.attr }}，{{ currentChoose.buyNum }}件</text>
					<text v-else>未选择</text>
				</view>
				<view class="more">
					<text class="iconfont icon-more">选规格</text>
					<u-icon name="arrow-right" color="#6f6e6c" size="14"></u-icon>
				</view>
			</view>
			<view class="list list-padding">
				<view class="title">送至</view>
				<view class="content content-center">
					<view class="serve">
						<!-- <text class="iconfont icon-dingwei"></text> -->
						<view class="text">{{ userInfo ? userInfo.fullAddress : '' }}</view>
					</view>
					<view class="serve-totast">注：如需更改收货地址请联系客服人员</view>
				</view>
				<!-- <view class="more">
          <text class="iconfont icon-more"></text>
        </view> -->
			</view>
			<view class="list">
				<view class="title" style="width: 12.5%">运费</view>
				<view class="content content-center">
					<!-- <text>{{ Number(details.shippingFee) ? details.shippingFee + ' 元' : '免运费' }}</text> -->
					<view class="serve-totast serve-totast-important">0元（当前仅支持到付，运输方式请联系客服）</view>
				</view>
				<!-- <view class="more"><text class="iconfont icon-more"></text></view> -->
			</view>
		</view>
		<!-- 评价 -->
		<!-- <view class="evaluate-data" ref="evaluate">
      <view class="title-more" @click="onEvaluate">
        <view class="title">
          <text>评价</text>
          <text class="num">999+</text>
        </view>
        <view class="more">
          <text class="iconfont icon-more"></text>
        </view>
      </view>
      <view class="evaluate-list">
        <view class="user-info">
          <view class="thumb">
            <image src="/static/img/user_pic.jpg" mode=""></image>
          </view>
          <view class="nickname-grade">
            <view class="nickname">
              <text>爱笑的汤姆</text>
            </view>
            <view class="grade">
              <text class="cuIcon-favorfill lg text-gray"></text>
            </view>
          </view>
        </view>
        <view class="content">
          <view class="character">
            <text class="two-omit">搭建啊激动了阿建档立卡点击就阿卡丽登记卡加端口几啊开了都金坷垃就恐龙当家哦架空</text>
          </view>
          <view class="attr">
            <text>蓝色</text>
          </view>
          <view class="thumb-list">
            <view class="list">
              <image src="/static/img/goods_banner_01.webp" mode=""></image>
            </view>
            <view class="list">
              <image src="/static/img/goods_banner_02.webp" mode=""></image>
            </view>
            <view class="list">
              <image src="/static/img/goods_banner_03.webp" mode=""></image>
            </view>
          </view>
        </view>
        <view class="look-all" @click="onEvaluate">
          <text>查看全部评价</text>
        </view>
      </view>
    </view> -->
		<!-- 商品介绍 -->
		<view class="products-introduction" ref="products">
			<view class="title">
				<text>商品介绍</text>
			</view>
			<view class="content" v-html="details.detailHtml"></view>
		</view>
		<!-- 底部 -->
		<view class="page-footer">
			<view class="footer-fn">
				<view class="list">
					<button class="contact" @click="bindcontact">
						<text class="iconfont icon-kefu" style="widt: 100%; display: block; height: 46%; margin-top: -9px"></text>
						<text>客服</text>
					</button>
				</view>
				<view class="list" @click="onToCart">
					<text class="iconfont icon-gouwuche"></text>
					<!-- <text class="iconfont icon-cart"></text> -->
					<text>购物车</text>
				</view>
			</view>
			<view class="footer-buy">
				<view class="cart-add" @click="$refs['GoodsAttr'].show(2)">
					<text>加入购物车</text>
				</view>
				<view class="buy-at" @click="$refs['GoodsAttr'].show(3)">
					<text>立即购买</text>
				</view>
			</view>
		</view>

		<!-- 服务弹窗 -->
		<goods-serve ref="GoodsServe"></goods-serve>
		<!-- 优惠券 -->
		<goods-coupon ref="GoodsCoupon"></goods-coupon>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
		<!-- 属性规格 -->
		<goods-attr
			ref="GoodsAttr"
			:allSaleAttr="allSaleAttr"
			:sku="sku"
			@choose="handleAttrChoose"
			@buyNow="buyNow"
			@addCart="addCart"
			:attrMap="attrMap"
			:skuMap="skuMap"
			:tempSku="tempSku"
		></goods-attr>

		<!-- 商品图片放大 -->
		<view class="bigImage" @tap.stop="isShow = false" v-if="isShow">
			<movable-area scale-area>
				<movable-view direction="all" @scale="onScale" scale="true" scale-min="1" scale-max="4" :scale-value="scale">
					<swiper class="screen-swiper round-dot" indicator-dots="true" circular="true" autoplay="true" interval="5000" duration="500" indicator-active-color="#ffffff">
						<swiper-item class="swiper-items" v-for="(item, index) in showImageList" :key="index">
							<image :src="item.url" mode="aspectFill"></image>
						</swiper-item>
					</swiper>
				</movable-view>
			</movable-area>
		</view>
	</view>
</template>

<script>
import GoodsServe from '@/components/goods/GoodsServe.vue';
import GoodsCoupon from '@/components/goods/GoodsCoupon.vue';
import GoodsAttr from '@/components/goods/GoodsAttr.vue';

import { goodsDetail, addCartApi } from '@/api/index';

const app = getApp();

export default {
	components: {
		GoodsServe,
		GoodsCoupon,
		GoodsAttr
	},
	data() {
		return {
			TabShow: 0,
			isMore: false,
			AttentionShow: 0,
			PageScrollTop: 0,
			type: 0,
			details: {},
			id: 0,
			userInfo: uni.getStorageSync('userInfo'),
			sku: [],
			allSaleAttr: {},
			currentChoose: {},
			skuMap: [],
			attrMap: {},
			tempSku: {},
			loading: false,
			isShow: false,
			showImageList: []
		};
	},
	async onLoad(params) {
		// this.$mHelper.loadVerify();
		const { id } = params;
		if (!id)
			uni.switchTab({
				url: '/pages/home/<USER>'
			});
		this.id = id;
		this.type = params.type || 0;
		await this.loadData();
	},
	onPageScroll(e) {
		this.PageScrollTop = e.scrollTop;
		uni
			.createSelectorQuery()
			.select('.products-introduction')
			.boundingClientRect((data) => {
				if (data.top < 80) {
					this.TabShow = 2;
				} else {
					this.TabShow = 0;
				}
			})
			.exec();
	},
	onShow() {
		this.userInfo = uni.getStorageSync('userInfo');
	},
	// 用户点击分享
	onShareAppMessage() {
		let shareParams = {
			title: `商品详情`,
			path: `/pages/goods/detail?id=${this.id}`
		};
		return shareParams;
	},

	methods: {
		showImageListFn(itemlist, index) {
			this.showImageList = itemlist;
			this.isShow = true;
		},
		// 客服
		bindcontact() {
			wx.openCustomerServiceChat({
				extInfo: {
					url: 'https://work.weixin.qq.com/kfid/kfca73cb4d1464a6a43'
				},
				corpId: 'ww8f007820b431a55e',
				showMessageCard: true,
				sendMessageTitle: '商品详情询问',
				sendMessagePath: `pages/goods/detail.html?id=${this.id}`,
				sendMessageImg: this.details.picsUrl[0].url,
				success(res) {}
			});
		},
		/**
		 * 返回
		 */
		onBack() {
			uni.switchTab({
				url: '/pages/home/<USER>'
			});
		},
		/**
		 * tab
		 */
		onTab(type) {
			this.TabShow = type;
			switch (type) {
				case 0:
					uni.pageScrollTo({
						scrollTop: 0,
						duration: 300
					});
					break;
				case 1:
					uni
						.createSelectorQuery()
						.select('.evaluate-data')
						.boundingClientRect((data) => {
							//data - 各种参数
							uni.pageScrollTo({
								scrollTop: this.PageScrollTop + data.top - 50,
								duration: 300
							});
						})
						.exec();
					break;
				case 2:
					uni
						.createSelectorQuery()
						.select('.products-introduction')
						.boundingClientRect((data) => {
							//data - 各种参数
							uni.pageScrollTo({
								scrollTop: this.PageScrollTop + data.top - 50,
								duration: 300
							});
						})
						.exec();
					break;
			}
		},
		/**
		 * 去购物车
		 */
		onToCart() {
			uni.switchTab({
				url: '/pages/cart/cart'
			});
		},
		/**
		 * 添加购物车
		 */
		addCart() {
			if (!uni.getStorageSync('customerId')) {
				this.$mRouter.push({
					route: '/pages/user/login'
				});
				return;
			}
			console.log(this.details.name);
			if (JSON.stringify(this.currentChoose) == '{}' || !this.currentChoose) {
				uni.showToast({
					title: '请选择商品',
					icon: 'error'
				});
				return;
			}
			this.loading = true;
			this.$http
				.post(addCartApi, {
					buyerPartnerId: uni.getStorageSync('customerId'),
					num: this.currentChoose.buyNum,
					saleSkuId: this.currentChoose.sku.id
				})
				.then((res) => {
					uni.showToast({
						title: '已加入购物车',
						icon: 'success'
					});
					this.$refs.GoodsAttr.hide();
				})
				.finally(() => {
					this.loading = false;
				});
		},
		buyNow() {
			if (JSON.stringify(this.currentChoose) == '{}' || !this.currentChoose) {
				uni.showToast({
					title: '请选择商品',
					icon: 'error'
				});
				return;
			}
			const params = [
				{
					saleSkuId: this.currentChoose.sku.id,
					buyNum: this.currentChoose.buyNum
				}
			];
			uni.navigateTo({
				url: '/pages/order/confirmOrder?tradeOrderSource=ITEM&saleSkuBuyParams=' + JSON.stringify(params),
				success: () => {
					console.log('跳转成功');
				},
				error: (err) => {
					console.log(err);
				}
			});
		},
		/**
		 * 降价通知点击
		 */
		onDepreciate() {
			uni.showToast({
				title: '降价通知提醒成功',
				icon: 'success'
			});
		},
		/**
		 * 关注点击
		 */
		onAttention() {
			if (this.AttentionShow === 0) {
				this.AttentionShow = 1;
				uni.showToast({
					title: '关注成功',
					icon: 'none'
				});
			} else {
				this.AttentionShow = 0;
				uni.showToast({
					title: '取消成功',
					icon: 'none'
				});
			}
		},
		/**
		 * 评价点击
		 */
		onEvaluate() {
			uni.navigateTo({
				url: '/pages/GoodsEvaluateList/GoodsEvaluateList'
			});
		},

		async loadData() {
			this.isLoading = true;
			await this.$http
				.get(goodsDetail + this.id)
				.then((res) => {
					if (res.code === 200 && res.data) {
						this.details = res.data;
						this.sku = this.details.sku;
						this.allSaleAttr = {
							...this.handleSkuAttr(this.details.sku)
						};
						// console.log(this.allSaleAttr);
						this.currentChoose = {
							sku: this.details.sku[0],
							attr: this.details.sku[0].saleAttrVals.map((item) => item.val).join('/'),
							buyNum: 1
						};
						this.init();
					}
				})
				.finally(() => {
					this.loading = false;
				});
		},
		handleSkuAttr(skuList) {
			const skuAttr = [];
			skuList.forEach((item) => {
				skuAttr.push(...item.saleAttrVals);
			});
			const result = [];
			skuAttr.forEach((item) => {
				if (result[item.name]) {
					result[item.name].indexOf(item.val) === -1 && result[item.name].push(item.val);
				} else {
					result[item.name] = [item.val];
				}
			});
			return result;
		},
		handleAttrChoose(val) {
			this.currentChoose = val;
		},
		getPrime(num) {
			const isPrime = (number) => {
				const target = number / 2;
				if (target === 2) return false;
				for (let index = 2; index < target; index++) {
					if (number % index === 0) return false;
				}
				return true;
			};
			const arr = [];
			for (let i = 2; arr.length < num; i++) {
				if (isPrime(i)) arr.push(i);
			}
			return arr;
		},
		init() {
			const allSaleAttr = [...Object.keys(this.allSaleAttr).map((item) => this.allSaleAttr[item])].flat();
			const primeList = this.getPrime(allSaleAttr.length);
			let index = 0;
			const attrMap = {};
			Object.keys(this.allSaleAttr).forEach((key) => {
				this.allSaleAttr[key].forEach((item) => {
					attrMap[key + '@' + item] = primeList[index];
					index++;
				});
			});
			this.attrMap = {
				...attrMap
			};
			const skuMap = [];
			const tempInfo = {
				availableNum: [this.sku[0].availableNum, this.sku[0].availableNum],
				saleUnitPrice: this.details.miniPrice,
				picsUrl: this.details.picsUrl[0]
			};
			this.sku.forEach((item) => {
				if (item.availableNum < tempInfo.availableNum[0]) tempInfo.availableNum[0] = item.availableNum;
				if (item.availableNum > tempInfo.availableNum[1]) tempInfo.availableNum[1] = item.availableNum;
				const target = item.saleAttrVals;
				let result = 1;
				target.forEach((item) => {
					result *= attrMap[item.name + '@' + item.val];
				});
				skuMap.push(result);
			});
			this.skuMap = skuMap;
			this.tempSku = tempInfo;
		}
	}
};
</script>

<style scoped lang="scss">
.page {
	position: absolute;
	width: 100%;
	// height: 100%;
	background: #f2f2f2;
	overflow-x: hidden;

	&.fixed {
		position: fixed;
	}

	&.auto {
		position: absolute;
	}

	// overflow-y: auto;
}

.goods-head {
	position: fixed;
	left: 0;
	top: 0;
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100rpx;
	background: rgba(255, 255, 255, 0);
	/* #ifdef APP-PLUS */
	height: calc(100rpx + var(--status-bar-height));
	/* #endif */
	/* #ifdef MP */
	height: 100rpx;

	/* #endif */
	.back {
		position: absolute;
		left: 0;
		top: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100rpx;
		height: 100%;
		/* #ifdef APP-PLUS */
		padding-top: 50rpx;
		/* #endif */
		/* #ifdef MP */
		padding-top: 100rpx;

		/* #endif */
		// 返回
		.back-one {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 50rpx;
			height: 50rpx;
			background-color: rgba(0, 0, 0, 0.3);
			border-radius: 100%;
			position: relative;

			text {
				display: flex;
				width: 20rpx;
				height: 20rpx;
				border-left: 2rpx solid #ffffff;
				border-bottom: 2rpx solid #ffffff;
				transform: rotate(45deg);
				position: absolute;
				left: 20rpx;
			}
		}

		.action {
			background-color: transparent;

			text {
				border-color: #555555;
			}
		}
	}

	// tab切换
	.head-tab {
		display: flex;
		align-items: center;
		height: 100%;
		/* #ifdef APP-PLUS */
		padding-top: 50rpx;
		/* #endif */
		/* #ifdef MP */
		padding-top: 100rpx;

		/* #endif */
		.tab {
			position: relative;
			margin: 0 20rpx;
			padding: 0 10rpx;

			text {
				color: #555555;
				font-size: 26rpx;
			}
		}

		.action {
			text {
				color: #212121;
				font-size: 28rpx;
			}

			.line {
				position: absolute;
				left: 0;
				bottom: -10rpx;
				width: 100%;
				height: 6rpx;
				background: linear-gradient(to right, #fe3b0f, rgba(255, 255, 255, 0.3));
			}
		}
	}

	// 分享更多
	.share-more {
		position: absolute;
		right: 0;
		top: 0;
		width: 140rpx;
		height: 100%;
		/* #ifdef APP-PLUS */
		padding-top: 50rpx;
		/* #endif */
		/* #ifdef MP */
		padding-top: 100rpx;

		/* #endif */
		.share-more-one {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-right: 20rpx;
			height: 100%;

			.list {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 50rpx;
				height: 50rpx;
				background-color: rgba(0, 0, 0, 0.3);
				border-radius: 100%;

				text {
					font-size: 28rpx;
					color: #ffffff;
				}
			}
		}

		.action {
			.list {
				background-color: transparent;

				text {
					color: #555555;
				}
			}
		}

		.mroe-list {
			position: fixed;
			right: 20rpx;
			top: 100rpx;
			/* #ifdef MP */
			top: 180rpx;
			/* #endif */
			width: 200rpx;
			background-color: rgba(255, 255, 255, 0.9);
			border-radius: 10rpx;

			.list {
				display: flex;
				align-items: center;
				width: 90%;
				height: 80rpx;
				margin: 0 auto;
				border-bottom: 2rpx solid #c8c7cc;
				padding: 0 4%;

				.icon {
					display: flex;
					align-items: center;
					width: 60rpx;

					text {
						font-size: 34rpx;
					}
				}

				.title {
					display: flex;
					align-items: center;

					text {
						font-size: 26rpx;
					}
				}
			}
		}
	}
}

/* banner 标题 */
.banner-title {
	background-color: #ffffff;
	padding-bottom: 20rpx;
}

/* banner */
.banner {
	width: 100%;
	height: 750rpx;

	.screen-swiper {
		width: 100%;
		height: 100%;
	}
}

/* 价格 */
.price-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 4%;
	height: 120rpx;

	.price {
		display: flex;
		align-items: center;

		.min {
			color: #fe3b0f;
			font-size: 28rpx;
			font-weight: bold;
		}

		.max {
			color: #fe3b0f;
			font-size: 48rpx;
			font-weight: bold;
		}
	}

	.info {
		display: flex;
		align-items: center;
		height: 100%;

		.list {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			padding: 0 20rpx;

			text {
				font-size: 24rpx;
				color: #555555;
			}

			.iconfont {
				font-size: 34rpx;
				margin-bottom: 10rpx;
				color: #555555;
			}

			.action {
				color: #fe3b0f;
			}
		}
	}
}

/* 限时抢购 */
.flash-price {
	display: flex;
	width: 100%;
	height: 100rpx;
	background-color: #ffffff;
	// border-radius: 20rpx;
	overflow: hidden;

	.price-item {
		position: relative;
		display: flex;
		width: 70%;
		height: 100%;
		background: linear-gradient(to left, #fe3b0f, #ff4e17);
		padding: 0 20rpx;
		overflow: hidden;

		.icon-item {
			display: flex;
			align-items: center;
			height: 100%;

			text {
				font-size: 42rpx;
				color: #ffffff;
			}
		}

		.price {
			display: flex;
			flex-direction: column;
			justify-content: center;
			margin-left: 20rpx;

			.current-price {
				display: flex;
				align-items: center;

				// height: 60rpx;
				text {
					color: #ffffff;
					font-weight: bold;
				}

				.min {
					font-size: 28rpx;
				}

				.max {
					font-size: 38rpx;
				}
			}

			.original-price {
				display: flex;
				align-items: center;

				text {
					font-size: 24rpx;
					color: #ffffff;
					opacity: 0.7;
					text-decoration: line-through;
				}
			}
		}

		.tag {
			position: absolute;
			right: -20rpx;
			bottom: -20rpx;
			transform: rotate(-45deg);

			text {
				font-size: 68rpx;
				color: rgba(0, 0, 0, 0.2);
			}
		}
	}

	.time-item {
		display: flex;
		flex-direction: column;
		justify-content: center;
		width: 30%;
		height: 100%;
		background-color: rgba(233, 59, 61, 0.5);

		// opacity: 0.5;
		.title {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;

			text {
				color: #ffffff;
				font-size: 24rpx;
			}
		}

		.time {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 50rpx;

			.num {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 40rpx;
				height: 40rpx;
				font-size: 24rpx;
				color: #ffffff;
				background-color: #fe3b0f;
				border-radius: 10rpx;
			}

			.dot {
				font-size: 24rpx;
				color: #fe3b0f;
				margin: 0 5rpx;
			}
		}
	}
}

/* 标题 */
.goods-title {
	padding: 0 4%;
	margin: 20rpx auto;

	text {
		font-size: 28rpx;
		color: #212121;
	}
}

/* 开通会员 */
.dredge-vip {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 90%;
	height: 80rpx;
	margin: 20rpx auto;
	background-color: #f5f5dc;
	border-radius: 20rpx;
	overflow: hidden;

	.title {
		display: flex;
		align-items: center;
		height: 100%;
		padding: 0 4%;

		text {
			font-size: 26rpx;
			color: #333333;

			.col {
				color: #fe3b0f;
				font-weight: bold;
			}
		}

		.iconfont {
			font-size: 34rpx;
			color: #333333;
			margin-right: 20rpx;
		}
	}

	.dredge {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 100rpx;
		height: 80rpx;
		background-color: #464c5b;

		text {
			font-size: 24rpx;
			color: #f5f5dc;
			text-align: center;
		}
	}
}

/* 优惠 */
.goods-discounts {
	padding: 0 4%;
	background-color: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
	margin: 22.67rpx;
	border-radius: 13rpx;

	.list-padding {
		padding: 10px 4% !important;
	}

	.list {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		padding: 0 4%;
		min-height: 100rpx;
		border-bottom: 2rpx solid #f6f6f6;

		.title {
			display: flex;
			align-items: center;
			width: 15%;
			height: 100%;
			font-size: 24rpx;
			color: #212121;
		}

		.content-center {
			flex-direction: column;
			align-items: start !important;
			width: 85% !important;
		}

		.content-list {
			display: flex;
			align-items: center;
			justify-content: flex-start;
			overflow: hidden;
			flex-wrap: wrap;
			text-overflow: ellipsis;
			padding-top: 14rpx;

			.content-jixin {
				font-size: 24rpx;

				padding: 0 10upx;
				background-color: #e8e8e8;
				border-radius: 10upx;
				color: #666;
				margin-right: 10rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 14rpx;
			}
		}

		.content {
			flex: 1;
			display: flex;
			align-items: center;
			width: 65%;
			height: 100%;

			> text {
				font-size: 24rpx;
				color: #555555;
			}

			.serve-totast-important {
				font-size: 24rpx !important;
				margin-top: 0 !important;
			}

			.serve-totast {
				color: #f5c744;
				font-size: 20rpx;
				margin-top: 8rpx;
			}

			.serve {
				display: flex;
				align-items: center;
				margin-right: 20rpx;

				.text {
					font-size: 24rpx;
					color: #555555;
				}

				.iconfont {
					font-size: 26rpx;
					color: #fe3b0f;
					margin-right: 10rpx;
				}
			}

			.coupon-list {
				position: relative;
				display: flex;
				align-items: center;
				// width: 100rpx;
				height: 30rpx;
				border: 2rpx solid #fe3b0f;
				border-radius: 6rpx;
				margin-right: 20rpx;

				view {
					display: inline-block;
					padding: 0 5rpx;
					color: #fe3b0f;
					font-size: 24rpx;
					transform: scale(0.8);
				}
			}

			.coupon-list:before {
				position: absolute;
				left: -10rpx;
				top: 50%;
				content: '';
				width: 12rpx;
				height: 12rpx;
				background-color: #fff;
				border-right: 2rpx solid #fe3b0f;
				border-radius: 100%;
				transform: translate(0, -50%);
			}

			.coupon-list:after {
				position: absolute;
				right: -10rpx;
				top: 50%;
				content: '';
				width: 12rpx;
				height: 12rpx;
				background-color: #fff;
				border-left: 2rpx solid #fe3b0f;
				border-radius: 100%;
				transform: translate(0, -50%);
			}
		}

		.more {
			width: 20%;
			display: flex;
			align-items: center;
			justify-content: flex-end;

			text {
				font-size: 24rpx;
				color: #6f6e6c;
			}
		}
	}
}

.goods-price {
	padding: 22.67rpx;
	background-color: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
	margin: 22.67rpx;
	border-radius: 13rpx;

	.price {
		font-size: 33rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #ff541e;
		line-height: 39rpx;

		text:first-child {
			font-size: 24rpx;
		}
	}

	.title {
		margin-top: 22.67rpx;
		font-size: 27rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #0c0c0c;
		line-height: 39rpx;
	}
}

/* 评价 */
.evaluate-data {
	padding: 0 4%;
	margin: 20rpx auto;
	background-color: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;

	.title-more {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		height: 100rpx;

		.title {
			display: flex;
			align-items: center;
			height: 100%;

			text {
				font-size: 28rpx;
				color: #212121;
				margin-right: 20rpx;
			}

			.num {
				font-size: 24rpx;
			}
		}

		.more {
			display: flex;
			align-items: center;

			text {
				font-size: 26rpx;
				color: #212121;
			}
		}
	}

	.evaluate-list {
		width: 100%;

		.user-info {
			display: flex;
			align-items: center;
			width: 100%;
			height: 80rpx;

			.thumb {
				width: 60rpx;
				height: 60rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: 100%;
				}
			}

			.nickname-grade {
				height: 60rpx;
				margin-left: 20rpx;

				.nickname {
					display: flex;
					align-items: center;

					text {
						font-size: 24rpx;
						color: #212121;
					}
				}

				.grade {
					display: flex;
					align-items: center;
					margin-top: 6rpx;

					text {
						font-size: 24rpx;
						color: #fe3b0f;
					}
				}
			}
		}

		.content {
			width: 100%;

			.character {
				display: flex;
				align-items: center;
				padding: 10rpx 0;

				text {
					font-size: 24rpx;
					color: #333333;
				}
			}

			.attr {
				display: flex;
				align-items: center;
				padding: 10rpx 0;

				text {
					font-size: 24rpx;
					color: #cccccc;
				}
			}

			.thumb-list {
				display: flex;
				width: 100%;
				height: 200rpx;
				margin: 10rpx 0;

				.list {
					width: 200rpx;
					height: 200rpx;
					margin-right: 3%;

					image {
						width: 100%;
						height: 100%;
					}
				}
			}
		}

		.look-all {
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 20rpx auto;

			text {
				padding: 10rpx 20rpx;
				font-size: 26rpx;
				color: #212121;
				border: 2rpx solid #f6f6f6;
				border-radius: 40rpx;
			}
		}
	}
}

/* 排行榜 */
.ranking-list {
	padding: 0 4%;
	margin: 20rpx auto;
	background-color: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;

	.ranking-title {
		display: flex;
		align-items: center;
		width: 100%;
		height: 80rpx;

		.title {
			font-size: 26rpx;
			color: #212121;
		}
	}

	.goods-list {
		display: flex;
		flex-wrap: wrap;
		width: 100%;

		.list {
			width: 32%;
			height: 360rpx;
			border-radius: 10rpx;
			overflow: hidden;
			margin-right: 2%;

			.thumb {
				width: 100%;
				height: 200rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.title {
				display: flex;
				align-items: center;
				width: 100%;
				height: 80rpx;

				text {
					font-size: 24rpx;
					color: #555555;
				}
			}

			.price {
				display: flex;
				align-items: center;
				width: 100%;
				height: 60rpx;

				text {
					color: #fe3b0f;
					font-size: 24rpx;
					font-weight: bold;
				}
			}
		}

		.list:nth-child(3n) {
			margin-right: 0;
		}
	}
}

/* 商品介绍 */
.products-introduction {
	padding: 0 4% 100rpx;

	.title {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 80rpx;

		text {
			font-size: 28rpx;
			color: #212121;
			margin: 0 20rpx;
		}
	}

	.title:before {
		content: '';
		width: 100rpx;
		height: 2rpx;
		background-color: #c0c0c0;
	}

	.title:after {
		content: '';
		width: 100rpx;
		height: 2rpx;
		background-color: #c0c0c0;
	}

	.content {
		text-align: center !important;
		width: 100%;

		image {
			width: 100%;
		}

		img {
			width: 100%;
		}
	}
}

/* 底部 */
.page-footer {
	position: fixed;
	left: 0;
	bottom: 0;
	display: flex;
	width: 100%;
	height: 140rpx;
	background-color: #ffffff;
	border-top: 2rpx solid #f6f6f6;
	padding: 10rpx 4% 24rpx 4%;

	.footer-fn {
		display: flex;
		align-items: center;
		width: 40%;
		height: 100%;

		.list {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 50%;
			height: 100%;

			text {
				font-size: 22rpx;
				color: #555555;
			}

			.iconfont {
				font-size: 34rpx;
				color: #212121;
			}
		}
	}

	.footer-buy {
		display: flex;
		align-items: center;
		// justify-content: space-between;
		width: 60%;
		height: 100%;

		.cart-add {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 50%;
			height: 70rpx;
			background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
			border-radius: 70rpx 0 0 70rpx;

			text {
				font-size: 30rpx;
				color: #ffffff;
			}
		}

		.buy-at {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 50%;
			height: 70rpx;
			background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
			border-radius: 0 70rpx 70rpx 0;

			text {
				font-size: 30rpx;
				color: #ffffff;
			}
		}
	}
}

button::after {
	border: none;
}

.bigImage {
	width: 100%;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	position: fixed;
	top: 0;
	left: 0;
}

movable-view {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100%;
}

movable-area {
	height: 100%;
	width: 100%;
	position: fixed;
	overflow: hidden;

	.screen-swiper {
		width: 100%;
	}
}

movable-view image {
	width: 100%;
}

.swiper-items {
	// display: flex;
	// align-items: center;
	// justify-content: center;
}
</style>
