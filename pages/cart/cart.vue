<template>
	<view class="page">
		<view class="head">
			<view class="edit" @click="editCart">
				<text>{{ isEdit ? "完成" : "编辑" }}</text>
			</view>
		</view>
		<!-- 购物车列表 -->
		<!-- <mescroll-body ref="mescrollRef" @down="downCallback" @up="upCallback" :down="downOption" :up="upOption" :top="0"> -->
		<view class="cart-list" v-if="goodsList.length > 0">
			<view class="list" v-for="good in goodsList" :key="good.id">
				<view class="check" @click="checkGood(good)">
					<text class="check-icon" :class="{
              'active iconfont icon-duihao': checkList.indexOf(good.id) !== -1,
            }"></text>
				</view>
				<view class="goods" @tap="jumpDetails(good.itemId)">
					<view class="thumb">
						<image :src="good.skuPicUrl[0].url" mode=""></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="two-omit">{{ good.itemName }}</text>
						</view>
						<view class="attribute">
							<view class="attr">
								<text>{{ good.saleAttrVals.map((item) => item.val) }}</text>
								<!-- <text class="more"></text> -->
							</view>
						</view>
						<view class="price-num">
							<view class="price">
								<text class="min">￥</text>
								<text class="max">{{ good.skuPrice }}</text>
							</view>
							<view class="num" @tap.stop>
								<text class="add" @tap.stop="changeBuyNumber(good.id, 'sub')">
									<text class="iconfont icon-subtract"></text>
								</text>
								<view class="number">
									<!-- <text>{{ good.cartSkuNum }}</text> -->
									<input v-model="good.cartSkuNum" type="number" maxlength="8"
										@change.stop="(e) => changeBuyNumber(e.detail.value)" />
								</view>
								<text class="add" @tap.stop="changeBuyNumber(good.id, 'add')">
									<text class="iconfont icon-add"></text>
								</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="goodsList.length === 0" style="padding-top: 200rpx">
			<rf-empty :info="'购物车竟然是空的'"></rf-empty>
		</view>
		<!-- 购物车失效商品列表 -->
		<!-- <view class="lose-efficacy-list">
      <view class="lose-efficacy-title">
        <view class="title">
          <text>失效商品1件</text>
        </view>
        <view class="empty">
          <text>清空失效商品</text>
        </view>
      </view>
      <view class="list" v-for="(item, index) in 2" :key="index">
        <view class="tag">
          <text>失效</text>
        </view>
        <view class="goods" @click="onSkip('goods')">
          <view class="pictrue">
            <image
              :src="'/static/img/goods_thumb_0' + (index + 1) + '.png'"
              mode=""
            ></image>
          </view>
          <view class="item">
            <view class="title">
              <text class="two-omit"
                >薇妮(Viney)时尚包包女包牛皮单肩包女休闲百搭斜挎包韩版小方包潮(水电费枪色)</text
              >
            </view>
            <view class="explain">
              <text>商品已不能购买，请联系客服进行沟通</text>
            </view>
          </view>
        </view>
      </view>
    </view> -->
		<!-- 为你推荐 -->
		<!-- <view class="recommend-info">
      <view class="recommend-title">
        <view class="title">
          <image src="/static/wntj_title.png" mode=""></image>
        </view>
      </view>
      <view class="goods-list">
        <view
          class="list"
          v-for="(item, index) in goodsList"
          @click="onSkip('goods')"
          :key="index"
        >
          <view class="pictrue">
            <image :src="item.img" mode="heightFix"></image>
          </view>
          <view class="title-tag">
            <view class="tag">
              <text v-if="item.is_goods === 1">特价</text>
              {{ item.name }}
            </view>
          </view>
          <view class="price-info">
            <view class="user-price">
              <text class="min">￥</text>
              <text class="max">{{ item.price }}</text>
            </view>
            <view class="vip-price">
              <image src="/static/vip_ico.png"></image>
              <text>￥{{ item.vip_price }}</text>
            </view>
          </view>
        </view>
      </view>
    </view> -->
		<!-- 结算 -->
		<view class="close-account">
			<view class="check-total">
				<view class="check" @click="allCheck">
					<text class="check-icon" :class="{
              'active iconfont icon-duihao':
                checkList.length !== 0 && checkList.length === goodsList.length,
            }"></text>
					<text class="all">全选</text>
				</view>
				<view class="total">
					<text>合计：</text>
					<text class="price">￥{{ totalPrice }}</text>
				</view>
			</view>
			<view class="account">
				<view class="btn-calculate" v-if="!isEdit" @click="settlement">
					<text>去结算({{ this.checkList.length }})</text>
				</view>
				<view class="btn-del" v-else>
					<!-- <text class="attention">移入关注</text> -->
					<text class="del" @click="deleteGoods">删除</text>
				</view>
			</view>
		</view>
		<!-- </mescroll-body> -->
		<rfLoading isFullScreen :active="isLoading"></rfLoading>
	</view>
</template>

<script>
	import {
		cartList,
		addNumberCart,
		decreaseNumCart,
		deleteCart,
		selectCart,
	} from "@/api/index";
	// 引入mescroll-mixins.js
	// import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		// mixins: [MescrollMixin], // 使用mixin
		components: {},
		data() {
			return {
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {
					use: false,
					toTop: {
						src: "",
					},
				},
				isEdit: false,
				goodsList: [],
				pagination: {
					pageNumber: 1,
					pageSize: 10,
					total: 0,
				},
				checkList: [],
				totalPrice: 0,
				isLoading: false,
			};
		},
		onShow() {
			this.refresh();


		},
		onReady() {},
		onReachBottom() {
			this.loadData();
		},
		onPullDownRefresh() {
			if (this.isLoading) return;
			uni.startPullDownRefresh({
				success: () => {
					this.loadData();
				},
			});
			uni.stopPullDownRefresh();
		},
		methods: {
			// 去详情
			jumpDetails(id) {
				let route = `/pages/goods/detail?id=${id}`;
				this.$mRouter.push({
					route,
				});
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.mescroll.endSuccess();
			},
			/*上拉加载的回调*/
			upCallback(page) {
				setTimeout(() => {
					this.mescroll.endByPage(10, 20);
				}, 2000);
			},
			/**
			 * 跳转点击
			 * @param {String} type 跳转类型
			 */
			onSkip(type) {
				switch (type) {
					case "classify":
						uni.navigateTo({
							url: "/pages/SearchGoodsList/SearchGoodsList",
						});
						break;
					case "goods":
						uni.navigateTo({
							url: "/pages/GoodsDetails/GoodsDetails",
							animationType: "zoom-fade-out",
							animationDuration: 200,
						});
						break;
				}
			},
			/**
			 * 结算
			 */
			settlement() {
				if (this.checkList.length === 0) return;
				const params = this.checkList.map((item) => {
					const target = this.goodsList.find((good) => good.id === item);
					console.log(target);
					return {
						saleSkuId: target.saleSkuId,
						buyNum: target.cartSkuNum
					};
				});
				uni.navigateTo({
					url: "/pages/order/confirmOrder?tradeOrderSource=CART&saleSkuBuyParams=" +
						JSON.stringify(params),
					success: () => {
						console.log("跳转成功");
					},
					error: (err) => {
						console.log(err);
					},
				});
			},
			loadData() {
				if (this.isLoading) return;
				if (
					this.goodsList.length !== 0 &&
					this.goodsList.length === this.pagination.total
				)
					return;
				this.isLoading = true;
				this.$http
					.get(cartList, {
						pageNumber: this.pagination.pageNumber,
						pageSize: this.pagination.pageSize,
					})
					.then((res) => {
						if (res.code === 200 && res.data) {
							this.goodsList = [...this.goodsList, ...res.data.rows];
							this.checkList = this.checkList.filter(
								(item) => !this.goodsList.find((good) => good.id === item)
							);
							this.pagination.total = +res.data.total;
							this.pagination.pageNumber++;
						}
					})
					.finally(() => {
						this.isLoading = false;
					});
			},
			async refresh() {
				this.pagination.pageNumber = 1;
				this.pagination.total = 0;
				this.goodsList = [];
				this.checkList = [];
				this.putSelectCart();
				this.loadData();
			},
			async changeBuyNumber(id, type) {
				let verify = await this.$http.post(this.$mHelper.verifyAccessToken);
				if (verify.data === 2) {
					this.$mHelper.toast("网络异常，请稍后再试...");
					this.$mHelper.relogin();
					return false;
				}
				this.isLoading = true;
				const editApi = type === "add" ? addNumberCart : decreaseNumCart;
				this.$http
					.post(editApi + "?id=" + id)
					.then((res) => {
						if (res.code === 200) {
							// this.refresh()
							const target = this.goodsList.find((item) => item.id === id);
							target.cartSkuNum += type === "add" ? 1 : -1;
							this.putSelectCart();
						} else {
							uni.showToast({
								title: res.message,
								icon: "error",
							});
						}
					})
					.finally(() => {
						this.isLoading = false;
					});
			},
			async checkGood(good) {
				let verify = await this.$http.post(this.$mHelper.verifyAccessToken);
				if (verify.data === 2) {
					this.$mHelper.toast("网络异常，请稍后再试...");
					this.$mHelper.relogin();
					return false;
				}
				const index = this.checkList.indexOf(good.id);
				if (index === -1) {
					this.checkList.push(good.id);
				} else {
					this.checkList.splice(index, 1);
				}
				this.checkList = [...this.checkList];
				this.putSelectCart();
			},
			getTotalPrice() {
				let result = 0;
				this.checkList.map((item) => {
					const target = this.goodsList.find((v) => v.id === item);
					result += target ? +target.skuPrice * +target.cartSkuNum : 0;
				});
				return Math.round(result * 100) / 100;
			},
			async allCheck() {
				let verify = await this.$http.post(this.$mHelper.verifyAccessToken);
				if (verify.data === 2) {
					this.$mHelper.toast("网络异常，请稍后再试...");
					this.$mHelper.relogin();
					return false;
				}
				if (this.checkList.length === this.goodsList.length) {
					this.checkList = [];
				} else {
					this.checkList = [...this.goodsList.map((good) => good.id)];
				}
				this.putSelectCart();
			},
			editCart() {
				if (this.isEdit) {}
				this.isEdit = !this.isEdit;
			},
			async deleteGoods() {
				let verify = await this.$http.post(this.$mHelper.verifyAccessToken);
				if (verify.data === 2) {
					this.$mHelper.toast("网络异常，请稍后再试...");
					this.$mHelper.relogin();
					return false;
				}
				uni.showModal({
					title: "删除",
					content: "是否删除该商品",
					success: (res) => {
						if (res.cancel) return;
						this.isLoading = true;
						this.$http
							.delete(deleteCart, this.checkList)
							.then((result) => {
								if (result.code === 200) {
									this.isLoading = false;
									this.checkList = [];
									this.refresh();
									this.putSelectCart();
								}
							})
							.finally(() => {
								this.isLoading = false;
							});
					},
				});
			},
			async putSelectCart() {
				await this.$http.post(selectCart, this.checkList).then((res) => {
					if (res.code === 200) {
						this.totalPrice = res.data.first;
					}
				});
			},
		},
	};
</script>

<style scoped lang="scss">
	.page {
		// padding-bottom: 200rpx;
		background: #f5f6f8;
		// min-height: 100vh;
		min-height: 100vh;
	}

	.head {
		position: fixed;
		left: 0;
		top: 0;
		z-index: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 80rpx;

		background-color: #ffffff;

		.title {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			font-size: 30rpx;
			color: #212121;
		}

		.edit {
			position: absolute;
			right: 20rpx;
			top: 50%;
			transform: translate(0, -50%);
			background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
			padding: 8rpx 20rpx;
			border-radius: 60px;

			text {
				color: #fff;
				font-size: 26rpx;
			}
		}
	}

	/* 购物车列表 */
	.cart-list {
		width: 100%;
		padding: 20rpx 0;
		margin-top: 100rpx;
		padding-bottom: 200rpx;

		.list {
			display: flex;
			height: 185rpx;
			margin: 0 23rpx 20rpx;
			background-color: #ffffff;
			box-shadow: 0 0 20rpx #f6f6f6;
			border-radius: 10rpx;

			.check {
				display: flex;
				align-items: center;
				width: 10%;
				height: 80%;
				margin-left: 20rpx;

				// background-color: #fff;

				text {
					font-size: 36rpx;
					color: #333333;
				}

				.icon-checked {
					color: #fe3b0f;
					// box-shadow: 0 0 10rpx  #fe3b0f;
				}
			}

			.goods {
				display: flex;
				align-items: center;
				width: 90%;
				height: 100%;

				.thumb {
					display: flex;
					// align-items: center;
					justify-content: center;
					width: 30%;
					height: 100%;
					margin-top: 20rpx;

					image {
						width: 160rpx;
						height: 160rpx;
						border-radius: 10rpx;
					}
				}

				.item {
					padding: 10rpx 0;
					width: 70%;
					height: 100%;

					.title {
						display: flex;
						align-items: center;
						width: 100%;
						height: auto;

						text {
							font-size: 26rpx;
							color: #212121;
						}
					}

					.attribute {
						display: flex;
						align-items: center;
						margin-top: 10rpx;

						.attr {
							display: flex;
							align-items: center;
							padding: 0 20rpx;
							height: 40rpx;
							background-color: #f6f6f6;
							border-radius: 10rpx;

							text {
								font-size: 24rpx;
								color: #333333;
							}

							.more {
								display: flex;
								width: 10rpx;
								height: 10rpx;
								border-left: 2rpx solid #333333;
								border-bottom: 2rpx solid #333333;
								transform: rotate(-45deg);
								margin-left: 10rpx;
							}
						}
					}

					.price-num {
						display: flex;
						align-items: center;
						justify-content: space-between;
						height: 80rpx;

						.price {
							display: flex;

							.min {
								color: #fe3b0f;
								font-size: 24rpx;
							}

							.max {
								font-size: 28rpx;
								color: #fe3b0f;
								font-weight: bold;
							}
						}

						.num {
							display: flex;
							height: 40rpx;
							margin-right: 20rpx;

							.add {
								display: flex;
								justify-content: center;
								align-items: center;
								width: 60rpx;
								height: 40rpx;
								background-color: #ffffff;

								text {
									color: #212121;
									font-size: 24rpx;
								}
							}

							.number {
								display: flex;
								justify-content: center;
								align-items: center;
								width: 80rpx;
								height: 40rpx;
								background-color: #f6f6f6;
								border-radius: 8rpx;
								text-align: center;

								text {
									font-size: 24rpx;
									color: #212121;
								}
							}
						}
					}
				}
			}
		}
	}

	.background {
		background: #f5f6f8;
	}

	/* 购物车失效商品列表 */
	.lose-efficacy-list {
		width: 100%;
		background-color: #ffffff;
		padding: 0 30rpx;
		margin-top: 30rpx;
		border-radius: 10rpx;
		overflow: hidden;

		.lose-efficacy-title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 80rpx;

			.title {
				display: flex;
				align-items: center;
				height: 100%;

				text {
					font-size: 28rpx;
					color: #222222;
				}
			}

			.empty {
				display: flex;
				align-items: center;
				height: 100%;

				text {
					font-size: 26rpx;
					color: #fe3b0f;
				}
			}
		}

		.list {
			display: flex;
			align-items: center;
			width: 100%;
			height: 185rpx;
			border-bottom: 1px solid #f6f6f6;

			.tag {
				display: flex;
				align-items: center;
				width: 10%;
				height: 100%;

				text {
					padding: 4rpx 10rpx;
					font-size: 24rpx;
					color: #ffffff;
					background-color: rgba(0, 0, 0, 0.3);
					border-radius: 20rpx;
				}
			}

			.goods {
				display: flex;
				align-items: center;
				width: 90%;
				height: 100%;
				background-color: #ffffff;
				border-radius: 10rpx;

				.pictrue {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 30%;
					height: 100%;

					image {
						width: 160rpx;
						height: 160rpx;
						border-radius: 10rpx;
					}
				}

				.item {
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					width: 70%;
					height: 160rpx;

					.title {
						width: 100%;

						text {
							font-size: 28rpx;
							color: #999999;
						}
					}

					.explain {
						display: flex;
						align-items: center;

						text {
							font-size: 24rpx;
							color: #222222;
						}
					}
				}
			}
		}
	}

	/* 为你推荐 */
	.recommend-info {
		width: 100%;
		background-color: #f2f2f2;
		cursor: pointer;

		.recommend-title {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 100rpx;

			.title {
				display: flex;
				align-items: center;

				image {
					width: 416rpx;
					height: 40rpx;
				}
			}
		}

		.goods-list {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			padding: 0 30rpx;

			.list {
				width: 49%;
				height: 540rpx;
				margin-bottom: 20rpx;
				background-color: #ffffff;
				border-radius: 10rpx;
				overflow: hidden;

				.pictrue {
					display: flex;
					justify-content: center;
					width: 100%;

					image {
						height: 350rpx;
					}
				}

				.title-tag {
					// display: flex;
					height: 100rpx;
					padding: 20rpx;

					.tag {
						float: left;
						margin-right: 10rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
						white-space: normal;
						font-size: 26rpx;
						line-height: 40rpx;

						text {
							font-size: 24rpx;
							color: #ffffff;
							padding: 4rpx 16rpx;
							background: linear-gradient(to right, #fe3b0f, #fc603a);
							border-radius: 6rpx;
							margin-right: 10rpx;
						}
					}
				}

				.price-info {
					display: flex;
					flex-wrap: wrap;
					align-items: center;
					justify-content: space-between;
					padding: 0 20rpx;
					height: 80rpx;

					.user-price {
						display: flex;
						align-items: center;

						text {
							color: #ff0000;
						}

						.min {
							font-size: 24rpx;
						}

						.max {
							font-size: 32rpx;
						}
					}

					.vip-price {
						display: flex;
						align-items: center;

						image {
							width: 26rpx;
							height: 26rpx;
							margin-right: 10rpx;
						}

						text {
							color: #fcb735;
							font-size: 24rpx;
						}
					}
				}
			}
		}
	}

	/* 结算 */
	.close-account {
		position: fixed;
		left: 0;
		bottom: 0;
		display: flex;
		justify-content: space-between;
		width: 100%;
		height: 100rpx;
		background-color: #ffffff;
		border-top: 2rpx solid #f6f6f6;

		.check-total {
			display: flex;
			align-items: center;
			width: 50%;
			height: 100%;

			.check {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 40%;
				height: 100%;

				text {
					font-size: 36rpx;
					color: #333333;
				}

				.icon-checked {
					color: #fe3b0f;
					// box-shadow: 0 0 10rpx  #fe3b0f;
				}

				.all {
					font-size: 24rpx;
					margin-left: 10rpx;
				}
			}

			.total {
				display: flex;
				align-items: center;
				width: 60%;
				height: 100%;

				text {
					font-size: 24rpx;
					color: #333333;
				}

				.price {
					font-weight: bold;
					color: #fe3b0f;
				}
			}
		}

		.account {
			display: flex;
			align-items: center;
			justify-content: flex-end;
			width: 46%;
			padding-right: 4%;

			.btn-calculate {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 160rpx;
				height: 60rpx;
				background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
				border-radius: 60rpx;

				text {
					color: #ffffff;
					font-size: 24rpx;
				}
			}

			.btn-del {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.attention {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 140rpx;
					height: 60rpx;
					border: 2rpx solid #eeeeee;
					border-radius: 60rpx;
					color: #333333;
					font-size: 24rpx;
					margin-right: 20rpx;
				}

				.del {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 100rpx;
					height: 60rpx;
					background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
					border-radius: 60rpx;
					color: #ffffff;
					font-size: 24rpx;
				}
			}
		}
	}

	.icon-subtract {
		position: relative;
		margin: 0 20rpx;

		&::after {
			content: "";
			display: block;
			width: 25rpx;
			height: 5rpx;
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			background-color: rgba($color: #000, $alpha: 0.8);
		}
	}

	.icon-add {
		position: relative;
		margin: 0 20rpx;

		&::after {
			content: "";
			display: block;
			width: 25rpx;
			height: 5rpx;
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			background-color: rgba($color: #000, $alpha: 0.8);
		}

		&::before {
			content: "";
			display: block;
			width: 5rpx;
			height: 25rpx;
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			background-color: rgba($color: #000, $alpha: 0.8);
		}
	}

	.check-icon {
		width: 35rpx;
		height: 35rpx;
		border: 1px solid #999999;
		border-radius: 50%;

		&.active {
			border: 1px solid #e5452f;
			color: #e5452f !important;
			line-height: 30rpx;
		}
	}
</style>