<template>
	<view class="page">
		<view class="back" @click="onBack">
			<view class="back-one" :class="{ action: PageScrollTop > 120 }">
				<text></text>
			</view>
		</view>
		<!-- 订单状态 -->
		<view class="order-status" v-if="goodsInfo.orderStatus == 'WAIT_PAY'">
			<view class="status">
				<text class="iconfont icon-zhuyi"></text>
				<text>待付款</text>
			</view>
			<view class="reason">
				<text>剩余{{ count }}</text>
			</view>
		</view>
		<view class="order-status2" v-if="goodsInfo.orderStatus == 'WAIT_DELIVER'">
			<view class="status">
				<text class="iconfont icon-zhuyi"></text>
				<text>待发货</text>
			</view>
			<view class="reason">
				<text>下单时间：{{ goodsInfo.createdAt }}</text>
			</view>
		</view>
		<view class="order-status2" v-if="goodsInfo.orderStatus == 'WAIT_RECEIVE'">
			<view class="status">
				<text class="iconfont icon-zhuyi"></text>
				<text>待收货</text>
			</view>
			<view class="reason">
				<text>下单时间：{{ goodsInfo.createdAt }}</text>
			</view>
		</view>
		<view class="order-status2" v-if="goodsInfo.orderStatus == 'SUCCESS'">
			<view class="status">
				<text class="iconfont icon-zhuyi"></text>
				<text>已完成</text>
			</view>
			<view class="reason">
				<text>下单时间：{{ goodsInfo.createdAt }}</text>
			</view>
		</view>
		<view class="order-status3" v-if="goodsInfo.orderStatus == 'CLOSED'">
			<view class="status">
				<text class="iconfont icon-zhuyi"></text>
				<text>已取消</text>
			</view>
			<view class="reason">
				<text>下单时间：{{ goodsInfo.createdAt }}</text>
			</view>
		</view>
		<!-- 收货地址 -->
		<view class="shipping-address">
			<view class="address">
				<text class="iconfont icon-dizhi"></text>
				<!-- <text>{{
          goodsInfo.consigneeFullAddress + goodsInfo.consigneeAddress
        }}</text> -->
				<text>{{
		  goodsInfo.consigneeFullAddress
		 }}</text>
			</view>
			<view class="name-phone">
				<text>{{ goodsInfo.consigneeName }}</text>
				<text>{{ goodsInfo.consigneePhone }}</text>
			</view>
		</view>

		<view class="goods-data">
			<view class="goods-title">
				<text>商品信息</text>
			</view>
			<view class="goods-list">
				<view class="list" v-for="(item, index) in goodsInfo.tradeOrderDetailList" :key="index">
					<view class="thumb">
						<image :src="item.saleSkuInfo.picUrl[0].url" mode=""></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="name one-omit">{{ item.itemName }}</text>
							<text class="attr"><text>{{
                  item.saleSkuInfo.saleAttrVals.map((item) => item.val)
                }}</text></text>
						</view>
						<view class="price-number">
							<view class="price">
								<text class="min">￥</text>
								<text class="max">{{ item.actualUnitPrice }}</text>
							</view>
							<view class="number">
								<text>x {{ item.itemNum }}</text>
							</view>
						</view>
						<!-- <view class="tag">
							<text>支持七天无理由退货</text>
						</view> -->
					</view>
				</view>
			</view>
		</view>
		<!-- 订单商品 -->
		<!-- <view class="order-goods">
      <view class="goods-list">
        <view class="list" v-for="(item, index) in goodsInfo.tradeOrderDetailList" :key="index">
          <view class="thumb">
            <image :src="'/static/img/goods_thumb_0' + (index + 1) + '.png'" mode=""></image>
          </view>
          <view class="item">
            <view class="title">
              <text class="one-omit">{{ item.itemName }}</text>
            </view>
            <view class="num-size">
              <text>颜色：黑色,XL,全国联保</text>
            </view>
            <view class="num-size">
              <text>数量：{{ item.itemNum }}</text>
            </view>
            <view class="price">
              <text>￥{{ item.payAmount }}</text>
            </view>
     
          </view>
        </view>
      </view>
      <button class="contact" open-type="contact" :show-message-card="true">
        <text class="iconfont icon-kefu"></text> 联系客服
      </button>
    </view> -->
		<!-- 订单信息 -->
		<!-- 订单明细 -->
		<view class="order-details">
			<view class="details-list">
				<view class="list">
					<view class="title">
						<text>商品总额</text>
					</view>
					<view class="price">
						<text>￥{{ goodsInfo.actualGoodsAmount }}</text>
					</view>
				</view>
				<view class="list">
					<view class="title">
						<text>运费</text>
					</view>
					<view class="price">
						<text>+￥{{ goodsInfo.shippingFee }}</text>
					</view>
				</view>
				<view class="list action">
					<view class="title">
						<text>订单总额：</text>
					</view>
					<view class="price">
						<text>￥{{ goodsInfo.actualAmount }}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="order-info">
			<view class="info-list">
				<view class="list">
					<view class="title">订单编号:</view>
					<view class="content">
						<text>{{ goodsInfo.orderNum }}</text>
						<!-- <text class="btn">复制</text> -->
					</view>
				</view>
				<view class="list">
					<view class="title">下单时间:</view>
					<view class="content">
						<text>{{ goodsInfo.orderTime }}</text>
					</view>
				</view>
				<view class="list">
					<view class="title">支付方式:</view>
					<view class="content">
						<text>在线支付</text>
					</view>
				</view>
				<view class="list">
					<view class="title">配送方式:</view>
					<view class="content">
						<text>普通快递</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 底部按钮 -->
		<view class="footer-btn" v-if="goodsInfo.orderStatus != 'CLOSED'">
			<view v-if="goodsInfo.orderStatus == 'WAIT_PAY'" class="del" @click.stop="onOrderCalcel">
				<text>取消订单</text>
			</view>
			<view v-if="goodsInfo.orderStatus !== 'WAIT_PAY'" class="del" @click.stop="onBack">
				<text>返回</text>
			</view>
			<view class="btn action" v-if="
          goodsInfo.orderStatus == 'WAIT_DELIVER' ||
          goodsInfo.orderStatus == 'SUCCESS'
        " @click.stop="setReturn">
				<text>申请售后</text>
			</view>
			<view class="btn action" v-if="goodsInfo.orderStatus == 'WAIT_PAY'">
				<text @click.stop="subPay">确认付款</text>
			</view>
			<view class="btn action" v-if="goodsInfo.orderStatus == 'WAIT_RECEIVE'">
				<text @click.stop="subReceive">确认收货</text>
			</view>
			<!-- <view class="btn action" v-if="goodsInfo.orderStatus == 'CLOSED'">
        <text @click.stop="subPay">再次购买</text>
      </view> -->
		</view>
	</view>
</template>

<script>
	import {
		orderdetail,
		orderReceived
	} from "@/api/order";
	export default {
		data() {
			return {
				goodsInfo: {},
				orderNum: "",
				count: "00分00秒",
				timer: null,
			};
		},
		async onShow() {
			await this.getInfo();
		},
		onHide() {
			if (this.timer) clearInterval(this.timer);
		},
		onLoad: function(options) {
			// this.$mHelper.loadVerify();
			this.orderNum = options.id;
			uni.$on("refresh", (data) => {
				this.getInfo();
			});
		},
		methods: {
			onBack() {
				uni.navigateBack();
			},
			async getInfo() {
				await this.$http.get(`${orderdetail}${this.orderNum}`).then((res) => {
					this.goodsInfo = res.data;
					if (this.goodsInfo.orderStatus === "WAIT_PAY") {
						this.countDown(+this.goodsInfo.payExpireSec);
					}
				});
			},
			onOrderCalcel() {
				uni.navigateTo({
					url: `/pages/order/cancelOrder?id=${this.goodsInfo.orderNum}`,
				});
			},
			setReturn() {
				//设置返回方式
				uni.showActionSheet({
					title: "选择退款方式",
					itemList: this.goodsInfo.orderStatus == "PAID" ? ["仅退款"] : ["仅退款", "退货退款"],
					success: (res) => {
						this.types = res.tapIndex == 0 ? "仅退款" : "退货退款";
						this.tapIndex = res.tapIndex;
						uni.navigateTo({
							url: `/pages/order/afterSale?type=${res.tapIndex}&orderNum=${this.goodsInfo.orderNum}`,
						});
					},
				});
			},
			/**
			 * 售后点击
			 */
			onApplyAftersales() {
				uni.navigateTo({
					url: "/pages/order/afterSaleType",
				});
			},
			async subReceive() {
				await this.$http.post(`${orderReceived}${this.orderNum}`).then(() => {
					uni.showToast({
						title: "收货成功",
						icon: "none",
						duration: 1000,
					});
					setTimeout(() => {
						uni.switchTab({
							url: "/pages/profile/profile",
						});
					}, 300);
				});
			},
			async subPay() {
				uni.redirectTo({
					url: `/pages/order/subPay?orderNum=${this.orderNum}&Pirce=${this.goodsInfo.actualAmount}&payExpireSec=${this.goodsInfo.payExpireSec}`,
				});
				// const _this = this
				// await this.$http
				//   .post(`${payPreview}${this.orderNum}`, { orderNum: this.orderNum })
				//   .then((res) => {
				//     console.log(res.data);
				//     uni.requestPayment({
				//       // timeStamp: obj.xxxx.timeStamp,  //后端返回的时间戳
				//       // nonceStr: obj.xxxx.nonceStr,   //后端返回的随机字符串
				//       // package: obj.xxxx.packageValue, //后端返回的prepay_id
				//       // signType: 'MD5', //后端签名算法,根据后端来,后端MD5这里即为MD5
				//       // paySign: obj.xxxx.paySign,  //后端返回的签名
				//       ...res.data,
				//       success(res) {
				//         console.log('用户支付扣款成功', res)

				//         setTimeout(function () {
				//           uni.showToast({
				//             title: "支付成功",
				//             icon: "loading",
				//             duration: 2000,
				//             mask: true
				//           })
				//         }, 1000);
				//         let route = `/pages/order/payResult`;
				//         _this.$mRouter.push({ route });
				//       },
				//       fail(res) {
				//         console.log('用户支付扣款失败', res)

				//         setTimeout(function () {
				//           uni.showToast({
				//             title: "支付失败",
				//             icon: "loading",
				//             duration: 2000,
				//             mask: true
				//           })
				//         }, 1000);
				//       }
				//     })
				//   });
			},
			countDown(time) {
				this.timer = setInterval(() => {
					if (time > 0) {
						const seconds = time % 60;
						const minutes = Math.floor(time / 60);
						this.count = `${minutes > 10 ? minutes : "0" + minutes}分${
            seconds > 10 ? seconds : "0" + seconds
          }秒`;
						time--;
						this.goodsInfo.payExpireSec--;
					} else {
						clearInterval(this.timer);
						this.timer = null;
					}
				}, 1000);
			},
		},
	};
</script>

<style scoped lang="scss">
	.page {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #f6f6f6;
	}

	/* 订单状态 */
	.order-status {
		padding: 50rpx;
		width: 100%;
		height: 250rpx;
		background: linear-gradient(to right, $base, $change-clor);

		.status {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 100rpx;

			text {
				font-size: 38rpx;
				// font-weight: bold;
				color: #ffffff;
			}

			.iconfont {
				margin-right: 20rpx;
			}
		}

		.reason {
			display: flex;
			// align-items: center;
			justify-content: center;
			width: 100%;
			height: 80rpx;

			text {
				font-size: 28rpx;
				color: #f6f6f6;
			}
		}
	}

	.order-status2 {
		padding: 50rpx;
		width: 100%;
		height: 250rpx;
		background: #fff;

		.status {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 100rpx;

			text {
				font-size: 38rpx;
				// font-weight: bold;
				color: #333;
			}

			.iconfont {
				margin-right: 20rpx;
			}
		}

		.reason {
			display: flex;
			// align-items: center;
			justify-content: center;
			width: 100%;
			height: 80rpx;

			text {
				font-size: 28rpx;
				color: #333;
			}
		}
	}

	.order-status3 {
		padding: 50rpx;
		width: 100%;
		height: 250rpx;
		background: #f6f6f6;

		.status {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 100rpx;

			text {
				font-size: 38rpx;
				// font-weight: bold;
				color: #333;
			}

			.iconfont {
				margin-right: 20rpx;
			}
		}

		.reason {
			display: flex;
			// align-items: center;
			justify-content: center;
			width: 100%;
			height: 80rpx;

			text {
				font-size: 28rpx;
				color: #333;
			}
		}
	}

	/* 收货地址 */
	.shipping-address {
		width: 100%;
		min-height: 200rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		margin: 20rpx auto 20rpx;
		padding-bottom: 22rpx;

		.name-phone {
			display: flex;
			align-items: center;
			padding: 0 22rpx;

			text {
				font-size: 26rpx;
				color: #323333;
				margin-right: 20rpx;
			}
		}

		.address {
			display: flex;
			padding: 4%;
			min-height: 100rpx;

			text {
				font-size: 28rpx;
				font-weight: bold;
				color: #222222;
			}

			.icon-dizhi {
				color: #1f1f1f;
				font-size: 40rpx;
				margin-right: 40rpx;
				font-weight: normal;
			}
		}
	}

	/* 订单商品 */
	.order-goods {
		width: 100%;
		background-color: #ffffff;
		border-radius: 20rpx;

		.goods-list {
			padding: 0 4%;

			.list {
				display: flex;
				align-items: center;
				width: 100%;
				min-height: 200rpx;

				.thumb {
					display: flex;
					width: 30%;
					height: 200rpx;
					margin-top: -20rpx;

					image {
						width: 160rpx;
						height: 160rpx;
						border-radius: 10rpx;
					}
				}

				.item {
					width: 70%;
					height: 100%;

					.title {
						display: flex;
						align-items: center;
						width: 100%;

						text {
							font-size: 26rpx;
							color: #222222;
						}

						.name {
							font-size: 28rpx;
							color: #222222;
							height: 60rpx;
						}
					}

					.num-size {
						display: flex;
						align-items: center;
						width: 100%;
						height: 60rpx;

						text {
							font-size: 26rpx;
							color: #959595;
							margin-right: 20rpx;
						}

						text:last-child {
							margin-right: 0;
						}
					}

					.price {
						display: flex;
						align-items: center;
						width: 100%;
						height: 60rpx;

						text {
							font-size: 28rpx;
							font-weight: bold;
							color: #222222;
						}
					}

					.order-btn {
						display: flex;
						align-items: center;
						justify-content: flex-end;
						width: 100%;
						height: 100rpx;

						.btn {
							padding: 10rpx 30rpx;
							color: #555555;
							font-size: 26rpx;
							border: 2rpx solid #eeeeee;
							border-radius: 100rpx;
						}
					}

					.order-btn {
						display: flex;
						align-items: center;
						justify-content: flex-end;
						width: 100%;
						height: 100rpx;

						.btn {
							padding: 10rpx 30rpx;
							color: #555555;
							font-size: 26rpx;
							border: 2rpx solid #eeeeee;
							border-radius: 100rpx;
						}
					}
				}
			}
		}

		.contact {
			width: 100%;
			line-height: 55px;
			background-color: #ffffff;
			font-size: 26rpx;
			border: none;
			height: 100rpx;

			text {
				font-size: 28rpx;
				color: #555555;
			}

			.iconfont {
				font-size: 34rpx;
				margin-right: 20rpx;
			}
		}
	}

	/* 订单信息 */
	.order-info {
		width: 100%;
		background-color: #ffffff;
		border-radius: 20rpx;
		margin: 20rpx auto;
		padding-bottom: 100rpx;

		.info-list {
			padding: 0 4%;

			.list {
				display: flex;
				align-items: center;
				width: 100%;
				height: 100rpx;
				border-bottom: 2rpx solid #f6f6f6;

				.title {
					font-size: 26rpx;
					color: #959595;
				}

				.content {
					display: flex;
					align-items: center;
					margin-left: 20rpx;

					text {
						font-size: 26rpx;
						font-weight: bold;
						color: #222222;
					}

					.btn {
						padding: 6rpx 20rpx;
						background-color: #eeeeee;
						color: #555555;
						font-size: 24rpx;
						border-radius: 50rpx;
						margin-left: 40rpx;
					}
				}
			}
		}
	}

	/* 订单明细 */
	.order-details {
		width: 100%;
		background-color: #ffffff;
		border-radius: 20rpx;
		margin: 20rpx auto;

		.details-list {
			padding: 0 4%;

			.list {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 100rpx;
				border-bottom: 2rpx solid #f6f6f6;

				.title {
					font-size: 26rpx;
					color: #959595;
				}

				.price {
					font-size: 26rpx;
					font-weight: bold;
				}
			}

			.action {
				display: flex;
				align-items: center;
				justify-content: flex-end;

				.price {
					font-size: 32rpx;
					font-weight: bold;
					color: $base;
				}
			}
		}
	}

	.footer-btn {
		position: fixed;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		padding: 0 10%;
		margin: auto;
		min-height: 60px;
		padding-bottom: 24rpx;
		background-color: #ffffff;
		border-top: 2rpx solid #eeeeee;

		.del {
			display: flex;
			align-items: center;
			width: 50%;
			height: 40px;
			color: #fff;
			background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
			border-radius: 56px 0px 0px 56px;
			text-align: center;
			justify-content: center;

			text {
				padding: 10rpx 30rpx;
				font-size: 24rpx;
				font-weight: bold;
			}
		}

		.btn {
			display: flex;
			align-items: center;
			width: 50%;
			height: 40px;
			color: #c0c0c0;
			border: 2rpx solid #c0c0c0;
			border-radius: 0px 56px 56px 0px;
			text-align: center;
			justify-content: center;

			text {
				padding: 10rpx 30rpx;
				font-size: 24rpx;
				font-weight: bold;
			}

			&.action {
				background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
				background-color: $base;
				color: #ffffff;
				border: 2rpx solid #ffffff;
				color: #fff;
				border: none;
			}
		}
	}

	.back {
		position: absolute;
		left: 0;
		top: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100rpx;
		padding-top: 100rpx;

		// 返回
		.back-one {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 50rpx;
			height: 50rpx;
			background-color: rgba(0, 0, 0, 0.3);
			border-radius: 100%;

			text {
				display: flex;
				width: 20rpx;
				height: 20rpx;
				margin-left: 8rpx;
				border-left: 2rpx solid #ffffff;
				border-bottom: 2rpx solid #ffffff;
				transform: rotate(45deg);
			}
		}

		.action {
			background-color: transparent;

			text {
				border-color: #555555;
			}
		}
	}

	/* 商品 */
	.goods-data {
		padding: 10rpx 4%;
		background-color: #ffffff;
		border-radius: 20rpx;
		margin: 20rpx auto 0 auto;

		.goods-title {
			display: flex;
			align-items: center;
			width: 100%;
			height: 80rpx;

			text {
				font-size: 26rpx;
				color: #222222;
			}
		}

		.goods-list {
			width: 100%;

			.list {
				display: flex;
				align-items: center;
				width: 100%;
				height: 200rpx;

				.thumb {
					display: flex;
					align-items: center;
					width: 30%;
					height: 100%;

					image {
						width: 160rpx;
						height: 160rpx;
						border-radius: 10rpx;
					}
				}

				.item {
					width: 70%;
					height: 160rpx;

					.title {
						display: flex;
						flex-direction: column;
						// justify-content: center;
						width: 100%;

						.name {
							font-size: 28rpx;
							color: #222222;
							height: 60rpx;
						}

						.attr {
							font-size: 24rpx;
							color: #c0c0c0;
						}
					}

					.price-number {
						display: flex;
						align-items: center;
						justify-content: space-between;
						width: 100%;
						height: 60rpx;

						.price {
							display: flex;
							align-items: center;

							text {
								color: $base;
								font-weight: bold;
							}

							.min {
								font-size: 26rpx;
							}

							.max {
								font-size: 32rpx;
							}
						}

						.number {
							display: flex;
							align-items: center;

							text {
								font-size: 26rpx;
								color: #222222;
							}
						}
					}

					.tag {
						display: flex;
						align-items: center;
						width: 100%;
						height: 40rpx;

						text {
							padding: 2rpx 12rpx;
							color: $base;
							border: 2rpx solid $base;
							border-radius: 40rpx;
							font-size: 24rpx;
						}
					}
				}
			}
		}

		.delivery {
			width: 100%;

			.list {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 80rpx;

				.title {
					font-size: 26rpx;
					color: #555555;
				}

				.content {
					display: flex;
					align-items: center;
					height: 40rpx;

					text {
						font-size: 26rpx;
						color: #222222;
					}

					.iconfont {
						// font-size: 24rpx;
						margin-top: 6rpx;
						margin-left: 10rpx;
					}

					.icon-check {
						font-size: 34rpx;
					}

					input {
						height: 80%;
						font-size: 26rpx;
						color: #222222;
						text-align: right;
					}
				}
			}
		}
	}
</style>