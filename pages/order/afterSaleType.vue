<template>
	<view class="page">
		<!-- 商品 -->
		<view class="goods-data">
			<view class="goods-list">
				<view class="list">
					<view class="thumb">
						<image src="/static/img/souji.webp" mode=""></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="two-omit">华为 HUAWEI 畅享9s 4GB+128GB 极光蓝</text>
						</view>
						<view class="price-num">
							<view class="price">
								<text>单价：</text>
								<text class="action">￥1999.00</text>
							</view>
							<view class="num">
								<text>数量：</text>
								<text class="action">1</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 售后类型选择 -->
		<view class="type-select">
			<view class="type-list">
				<view class="list" @click="onReturnType(0)">
					<view class="title">
						<image src="/static/sale_tk.png" mode=""></image>
						<text>退款</text>
					</view>
					<view class="content">
						<text>申请商品的退款</text>
						<text class="iconfont icon-more"></text>
					</view>
				</view>
				<view class="list" @click="onReturnType(1)">
					<view class="title">
						<image src="/static/sale_th.png" mode=""></image>
						<text>退货</text>
					</view>
					<view class="content">
						<text>申请商品的退货</text>
						<text class="iconfont icon-more"></text>
					</view>
				</view>
				<view class="list" @click="onReturnType(2)">
					<view class="title">
						<image src="/static/sale_hh.png" mode=""></image>
						<text>换货</text>
					</view>
					<view class="content">
						<text>申请商品的换货</text>
						<text class="iconfont icon-more"></text>
					</view>
				</view>
			</view>
		</view>
		<!-- 联系客服 -->
		<view class="contact-service">
			<view class="btn" @click="bindcontact">
				<text class="iconfont icon-kefu"></text>
				<text>联系客服</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {

		};
	},
	methods: {
		// 客服
		bindcontact (){
			wx.openCustomerServiceChat({
			  extInfo: {url: 'https://work.weixin.qq.com/kfid/kfca73cb4d1464a6a43'},
			  corpId: 'ww8f007820b431a55e',
			  success(res) {}
			})
		},
		/**
		 * 退款类型点击
		 */
		onReturnType(type) {
			uni.navigateTo({
				url: '/pages/ReturnDetails/ReturnDetails?type=' + type,
			})
		}
	}
}
</script>

<style scoped lang="scss">
.page {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: #f6f6f6;
}

/* 商品 */
.goods-data {
	width: 100%;
	background-color: #FFFFFF;
	border-radius: 0 0 20rpx 20rpx;

	.goods-list {
		padding: 0 4%;

		.list {
			display: flex;
			align-items: center;
			width: 100%;
			height: 200rpx;

			.thumb {
				display: flex;
				align-items: center;
				width: 30%;
				height: 100%;

				image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 10rpx;
				}
			}

			.item {
				width: 70%;
				height: 100%;

				.title {
					display: flex;
					align-items: center;
					width: 100%;
					height: 100rpx;

					text {
						font-size: 26rpx;
						color: #c0c0c0;
					}
				}

				.price-num {
					display: flex;
					align-items: center;
					width: 100%;
					height: 80rpx;

					.price {
						display: flex;
						align-items: center;

						text {
							font-size: 24rpx;
							color: #C0C0C0;
						}

						.action {
							color: #222222;
							font-weight: bold;
						}
					}

					.num {
						display: flex;
						align-items: center;
						margin-left: 20rpx;

						text {
							font-size: 24rpx;
							color: #C0C0C0;
						}

						.action {
							color: #222222;
							font-weight: bold;
						}
					}
				}
			}
		}
	}
}

/* 售后类型选择 */
.type-select {
	width: 94%;
	margin: 20rpx auto;
	background-color: #FFFFFF;
	border-radius: 20rpx;
	overflow: hidden;

	.type-list {
		padding: 0 4%;

		.list {
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 100rpx;

			.title {
				display: flex;
				align-items: center;

				image {
					width: 40rpx;
					height: 40rpx;
				}

				text {
					font-size: 32rpx;
					font-weight: bold;
					color: #222222;
					margin-left: 20rpx;
				}
			}

			.content {
				display: flex;
				align-items: center;

				text {
					font-size: 26rpx;
					color: #959595;
					margin-left: 10rpx;
				}
			}
		}
	}
}

/* 联系客服 */
.contact-service {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 94%;
	height: 100rpx;
	background-color: #FFFFFF;
	margin: 20rpx auto;
	border-radius: 20rpx;

	.btn {
		display: flex;
		align-items: center;

		text {
			font-size: 30rpx;
			font-weight: bold;
		}

		.iconfont {
			margin-right: 20rpx;
		}
	}
}</style>
