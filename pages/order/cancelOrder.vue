<template>
	<view class="cencal_order">
		<view class="remark">
			<textarea :maxlength="maxRemarkLength" v-model="closeReason" placeholder="请在此处输入您的退单理由"
				placeholder-class="textarea_p"></textarea>
			<text class="tips">{{ closeReason.length }}/{{ maxRemarkLength }}</text>
		</view>

		<!-- <view class="tag_box">
      <view v-for="(item, index) in remarkList" :key="index" class="tag_list" @tap="setCurrent(index)">
        <view
          :style="'color:' + (item.current == true ? '#fff' : '') + ';background:' + (item.current == true ? colors : '') + ';border:' + (item.current == true ? 'none' : '')">
          {{ item.name }}
        </view>
      </view>
    </view> -->
		<view class="tips">申请提交后，平台将进行审核，请耐心等待</view>
		<view class="footer-btn">
			<view class="del" @click.stop="cancel">
				<text>取消</text>
			</view>
			<view class="btn action">
				<text @click.stop="sub">确认提交</text>
			</view>
		</view>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
	import {
		orderClose
	} from '@/api/order';
	export default {
		data() {
			return {
				closeReason: '',
				orderId: '',
				remarkList: [{
					name: '图案不好看'
				}, {
					name: '性价比太低'
				}, {
					name: '态度不好'
				}, {
					name: '价格不合理'
				}, {
					name: '做工不行'
				}, {
					name: '物流时间长'
				}, {
					name: '价格优惠低'
				}, {
					name: '其他原因'
				}],
				data: "",
				maxRemarkLength: 80,
				loading: false
			};
		},

		components: {},
		props: {},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			// this.$mHelper.loadVerify();
			this.orderId = options.id;
		},
		methods: {
			cancel() {
				uni.navigateBack();
				uni.$emit('refresh', {
					refresh: true
				})
			},
			sub() {
				let obj = {
					closeReason: this.closeReason,
					tradeOrderNum: this.orderId
				}
				this.loading = true
				this.$http.post(`${orderClose}`, obj)
					.then((res) => {
						if (res.code === 200) {
							uni.showToast({
								title: '订单取消成功',
								icon: 'success'
							})
							uni.$emit('refresh', {
								refresh: true
							})
							uni.navigateBack();
							// this.$mRouter.reLaunch({ route: "/pages/profile/profile" });
						} else {
							throw new Error(res.msg);
						}
					})
					.catch((err) => {
						uni.showToast({
							title: err.message,
							icon: 'error'
						})
					})
					.finally(() => {
						this.loading = false;
					});
			}
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},

		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},

		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},

	};
</script>
<style lang="scss" scoped>
	page {
		background-color: #F5F5FA;
	}

	.cencal_order {
		padding: 20upx 4%;
		background-color: #fff;
	}

	.remark {
		background-color: #F5F5F5;
		border-radius: 10upx;
		height: 60vw;
		padding: 20upx;
		margin-top: 20upx;

		textarea {
			font-size: 26upx;
			color: #797979;
		}

		.tips {
			color: #cccc;
		}
	}

	.textarea_p {
		font-size: 24upx;
		color: #797979;
	}

	.tag_box {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		margin-top: 40upx;
	}

	.tag_box::after {
		content: '';
		width: 30%;
	}

	.tag_list {
		max-width: 24%;
		min-width: 24%;
		text-align: center;
		margin-bottom: 30upx;
	}

	.tag_list view {
		height: 60upx;
		line-height: 60upx;
		border-radius: 30upx;
		border: 1upx solid #ddd;
		font-size: 22upx;
		color: #303030;
	}

	.btns {
		width: 100%;
		height: 80upx;
		line-height: 80upx;
		font-size: 30upx;
		color: #FFFFFF;
		text-align: center;
		margin-top: 100upx;
		border-radius: 20upx;
		background: linear-gradient(90deg, #E5452F 0%, #EE822F 100%);
		position: fixed;
		bottom: 30rpx;
		left: 10%;
		width: 80%;

	}

	.tips {
		font-size: 24upx;
		color: #999999;
		height: 40upx;
		line-height: 40upx;
		margin-top: 100upx;
		text-align: center;
	}

	.footer-btn {
		position: fixed;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		padding: 0 10%;
		margin: auto;
		height: 84px;
		background-color: #FFFFFF;
		border-top: 2rpx solid #EEEEEE;

		.del {
			display: flex;
			align-items: center;
			width: 50%;
			height: 40px;
			color: #fff;
			background: linear-gradient(90deg, #F5C744 0%, #EE822F 100%);
			border-radius: 56px 0px 0px 56px;
			text-align: center;
			justify-content: center;

			text {
				padding: 10rpx 30rpx;
				font-size: 24rpx;
				font-weight: bold;
			}
		}

		.btn {
			display: flex;
			align-items: center;
			width: 50%;
			height: 40px;
			color: #C0C0C0;
			border: 2rpx solid #C0C0C0;
			border-radius: 0px 56px 56px 0px;
			text-align: center;
			justify-content: center;

			text {
				padding: 10rpx 30rpx;
				font-size: 24rpx;
				font-weight: bold;
			}

			&.action {
				background: linear-gradient(90deg, #E5452F 0%, #EE822F 100%);
				background-color: $base;
				color: #FFFFFF;
				border: 2rpx solid #FFFFFF;
				color: #fff;
				border: none;
			}
		}
	}
</style>