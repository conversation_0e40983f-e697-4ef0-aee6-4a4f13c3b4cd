<template>
	<view class="page">
		<view class="price-count-down">
			<view class="price">
				<text class="min">￥</text>
				<text class="max">{{ Pirce }}</text>
			</view>
			<view class="count-down">
				<view class="title">支付剩余时间</view>
				<view class="count">
					<text class="time">{{ hour }}</text>
					<text class="dot">:</text>
					<text class="time">{{ min }}</text>
					<text class="dot">:</text>
					<text class="time">{{ sec }}</text>
				</view>
			</view>
		</view>
		<!-- 支付方式列表 -->
		<view class="pay-way">
			<view class="pay-list">
				<view class="list" v-for="(item, index) in PayList" @click="onPayWay(item, index)" :key="index">
					<view class="pay-type">
						<image :src="item.icon" mode=""></image>
						<text>{{ item.name }}</text>
					</view>
					<view class="check">
						<text class="iconfont" :class="PayWay === index ? 'icon-checked action' : 'icon-check'"></text>
					</view>
				</view>
			</view>
		</view>
		<view class="pay-submit">
			<view class="submit" @click="onSubmit">{{ PayPirce }}{{ Pirce }}</view>
		</view>
	</view>
</template>

<script>
	import {
		orderdetail,
		payPreview
	} from "@/api/order";
	export default {
		data() {
			return {
				orderNum: '',
				PayList: [{
						icon: '/static/images/home/<USER>',
						name: '微信支付',
					}
					// , {
					// 	icon: '/static/images/home/<USER>',
					// 	name: '支付宝支付',
					// }, {
					// 	icon: '/static/images/home/<USER>',
					// 	name: '银行卡支付',
					// },
				],
				PayWay: 0,
				PayPirce: `微信支付`,
				Pirce: 0,
				CountDown: 1000,
				day: 0,
				hour: 0,
				min: 0,
				sec: 0,
			};
		},
		onLoad(params) {
			this.CountDownData();
			this.orderNum = params.orderNum
			this.Pirce = params.Pirce
			this.CountDown = params.payExpireSec
			console.log(this.CountDown);
		},
		methods: {
			/**
			 * 支付方式切换点击
			 */
			onPayWay(item, index) {
				this.PayWay = index;
				this.PayPirce = `${item.name}`
			},
			/**
			 * 倒计时
			 */
			CountDownData() {
				console.log(this.CountDown);
				setTimeout(() => {
					this.CountDown--;
					this.day = parseInt(this.CountDown / (24 * 60 * 60))
					this.hour = parseInt(this.CountDown / (60 * 60) % 24);
					this.min = parseInt(this.CountDown / 60 % 60);
					this.sec = parseInt(this.CountDown % 60);
					console.log(this.CountDown);
					console.log(this.hour, this.min, this.sec);
					if (this.CountDown <= 0) {
						return
					}
					this.CountDownData();
				}, 1000)
			},
			/**
			 * 支付点击
			 */
			async onSubmit() {
				let verify = await this.$http.post(this.$mHelper.verifyAccessToken);
				if (verify.data === 2) {
					this.$mHelper.toast("网络异常，请稍后再试...");
					this.$mHelper.relogin();
					return false;
				}
				const _this = this
				await this.$http
					.post(`${payPreview}${this.orderNum}`, {
						orderNum: this.orderNum
					})
					.then((res) => {
						console.log(res.data);
						uni.requestPayment({
							// timeStamp: obj.xxxx.timeStamp,  //后端返回的时间戳
							// nonceStr: obj.xxxx.nonceStr,   //后端返回的随机字符串
							// package: obj.xxxx.packageValue, //后端返回的prepay_id
							// signType: 'MD5', //后端签名算法,根据后端来,后端MD5这里即为MD5
							// paySign: obj.xxxx.paySign,  //后端返回的签名
							...res.data,
							success(res) {
								console.log('用户支付扣款成功', res)

								setTimeout(function() {
									uni.showToast({
										title: "支付成功",
										icon: "loading",
										duration: 2000,
										mask: true
									})
									_this.$mRouter.push({
										route: `/pages/order/payResult?type=1&orderNum=${_this.orderNum}&Pirce=${_this.Pirce}`
									});

								}, 500);
							},
							fail(res) {
								console.log('用户支付扣款失败', res)

								setTimeout(function() {
									uni.showToast({
										title: "支付失败",
										icon: "loading",
										duration: 2000,
										mask: true
									})
									_this.$mRouter.push({
										route: `/pages/order/payResult?type=2&orderNum=${_this.orderNum}&Pirce=${_this.Pirce}`
									});

								}, 500);
							}
						})
					});


				// uni.redirectTo({
				// 	url: '/pages/PayResult/PayResult',
				// })
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: #f6f6f6;
	}

	/* 金额倒计时 */
	.price-count-down {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 200rpx;
		background-color: #FFFFFF;

		.price {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 80rpx;

			text {
				color: $base;
				font-weight: bold;
			}

			.min {
				font-size: 32rpx;
			}

			.max {
				font-size: 52rpx;
			}
		}

		.count-down {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 60rpx;

			.title {
				font-size: 24rpx;
				color: #222222;
			}

			.count {
				display: flex;
				align-items: center;
				margin-left: 20rpx;

				.time {
					padding: 4rpx 4rpx;
					background-color: #EEEEEE;
					font-size: 24rpx;
					color: #222222;
					border-radius: 2rpx;
				}

				.dot {
					margin: 0 10rpx;
					font-size: 24rpx;
					color: #222222;
				}
			}
		}
	}

	/* 支付方式 */
	.pay-way {
		width: 100%;
		background-color: #FFFFFF;
		margin-top: 220rpx;

		.pay-list {
			padding: 0 4%;

			.list {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 100rpx;
				border-bottom: 2rpx solid #f6f6f6;

				.pay-type {
					display: flex;
					align-items: center;

					image {
						width: 40rpx;
						height: 40rpx;
					}

					text {
						font-size: 28rpx;
						color: #222222;
						margin-left: 20rpx;
					}
				}

				.check {
					display: flex;
					align-items: center;

					text {
						font-size: 42rpx;
						color: #C0C0C0;
					}

					.action {
						color: $base;
					}
				}
			}
		}
	}

	/* 支付提交 */
	.pay-submit {
		position: absolute;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 122rpx;
		background-color: #FFFFFF;
		padding-bottom: 24rpx;

		.submit {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 90%;
			height: 70%;
			background-color: $base;
			color: #FFFFFF;
			border-radius: 100rpx;
			font-size: 28rpx;
		}
	}
</style>