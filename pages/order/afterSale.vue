<template>
	<view class="page">
		<!-- 商品 -->
		<view class="goods-data">
			<view class="goods-title">
				<text>商品信息</text>
			</view>
			<view class="goods-list">
				<view class="list" v-for="(item, index) in goodsInfo.tradeOrderDetailList" :key="index">
					<view class="thumb">
						<image :src="item.saleSkuInfo.picUrl[0].url" mode=""></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="name one-omit">{{ item.itemName }}</text>
							<text class="attr"><text>{{
								item.saleSkuInfo.saleAttrVals.map((item) => item.val)
							}}</text></text>
						</view>
						<view class="price-number">
							<view class="price">
								<text class="min">￥</text>
								<text class="max">{{ item.payAmount }}</text>
							</view>
							<view class="number">
								<text>x {{ item.itemNum }}</text>
							</view>
						</view>
						<!-- <view class="tag">
							<text>支持七天无理由退货</text>
						</view> -->
					</view>
				</view>
			</view>
		</view>
		<!-- <view class="goods-data">
			<view class="goods-list">
				<view class="list">
					<view class="thumb">
						<image src="/static/img/souji.webp" mode=""></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="two-omit">华为 HUAWEI 畅享9s 4GB+128GB 极光蓝</text>
						</view>
						<view class="price-num">
							<view class="price">
								<text>单价：</text>
								<text class="action">￥1999.00</text>
							</view>
							<view class="num">
								<text>数量：</text>
								<text class="action">1</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view> -->
		<!-- 申请数据 -->
		<view class="apply-data">
			<view class="apply-list">
				<!-- <view class="list" @click="isApplyCause = true">
					<view class="title">
						<text>申请原因</text>
					</view>
					<view class="more">
						<text>请选择申请原因</text>
						<text class="iconfont icon-more1"></text>
					</view>
				</view> -->
				<view class="list">
					<view class="title">
						<text>申请金额</text>
					</view>
					<view class="price">
						<text>￥{{ goodsInfo.actualAmount }}</text>
					</view>

				</view>
				<view class="list describe">
					<textarea v-model="reason" placeholder="请具体描述申请原因" />
				</view>

				<p class="youhui" style="border-bottom: none;">
					<text class="text1">上传凭证</text>
					<!-- <text class="text3">(最多3张)</text> -->
				</p>
				<view class="img_box">
					<FileUpload @submit="handleSubmit">
					</FileUpload>
				</view>
				<p class="tips">
					提交服务单后，售后专员可能与您电话沟通，请保持手机畅通
				</p>
				<button style=" margin-top:20rpx" class="contact" @click="bindcontact">
					<text class="iconfont icon-kefu"></text> 联系客服
				</button>
				<!-- 退货的返回方式以及收件地址 -->
				<view class="address" v-if="type == 1">
					<view class="address_type">
						<text class="type_text">返回方式</text>
						<view class="type_right">
							<text>自行寄回</text>
						</view>
					</view>
					<!-- <view class="rule">
						<view class="rule_left">如因个人原因退/换货，将在换新商品签收时收取8元运费</view>
						<view class="rule_right">
							运费规则
						</view>
					</view> -->

				</view>

				<!-- 收货地址 只有在换货和维修时才显示 -->
				<!-- <view class="address">
					<p class="m_a_title">收货地址<text>(该地址是商家回寄给您的地址)</text></p>
					<view class="user_address" @click="setLocation(1)" style="border-bottom: none;">
						<view>
							<view class="u_a_top">
								<text class="iconfont icon-dizhi"></text>
								<text class="name">{{ Toaddress.name }}</text>
								<text>{{ Toaddress.phone }}</text>
							</view>
							<view class="u_a_bottom">
								地址: {{ Toaddress.address }}{{ Toaddress.moreAddres }}
							</view>
						</view>
						<image src="/static/images/home/<USER>" mode=""></image>
					</view>
				</view> -->
				<!-- 联系人信息 -->
				<!-- <view class="contacts">
					<view class="user_cell">
						<text>前往寄回</text>
					</view>
					<view class="user_cell">
						填写单号
					</view>
				</view> -->


			</view>

		</view>
		<!-- 提交 -->
		<view class="submit-btn">
			<view class="btn" @click="subReverse">提交</view>
		</view>
		<!-- 申请原因弹窗 -->
		<view class="apply-cause-win cu-modal bottom-modal" :class="{ 'show': isApplyCause }" @click="isApplyCause = false">
			<view class="cu-dialog">
				<view class="title">申请原因</view>
				<view class="cause-list">
					<view class="list">
						<view class="cause">质量问题</view>
						<view class="check">
							<text class="iconfont icon-check"></text>
						</view>
					</view>
					<view class="list">
						<view class="cause">拍错/多拍/不想要</view>
						<view class="check">
							<text class="iconfont icon-check"></text>
						</view>
					</view>
					<view class="list">
						<view class="cause">协商一致退款</view>
						<view class="check">
							<text class="iconfont icon-check"></text>
						</view>
					</view>
					<view class="list">
						<view class="cause">缺货</view>
						<view class="check">
							<text class="iconfont icon-check"></text>
						</view>
					</view>
					<view class="list">
						<view class="cause">未按约定时间发货</view>
						<view class="check">
							<text class="iconfont icon-check"></text>
						</view>
					</view>
					<view class="list">
						<view class="cause">其他</view>
						<view class="check">
							<text class="iconfont icon-check"></text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		orderdetail,
		orderReverse
	} from "@/api/order";
	import FileUpload from "@/components/file-upload/index.vue";

	export default {
		data() {
			return {
				orderNum: '',
				type: 0,
				reason: '',
				isApplyCause: false,
				imgUrl: [],
				goodsInfo: {},

				Toaddress: {
					name: '反转',
					phone: '17712333156',
					address: '安徽省合肥市庐阳区',
					moreAddres: '逍遥津',
					address_id: 1
				},
				form: {
					"reason": "",
					"reverseType": "2701", //售后类型(2700数据字典):2702退货退款单/2701退款单
					"seqId": "", //交易订单编号
					"staffId": '', // 客户员工id
					"tradeOrderId": '', //交易订单ID
				}
			};
		},
		components: {
			FileUpload,
		},
		onLoad: function(options) {
			// this.$mHelper.loadVerify();
			this.orderNum = options.orderNum;
			this.type = options.type;
			this.getInfo();

		},
		methods: {
			// 客服
			bindcontact() {
				wx.openCustomerServiceChat({
					extInfo: {
						url: 'https://work.weixin.qq.com/kfid/kfca73cb4d1464a6a43'
					},
					corpId: 'ww8f007820b431a55e',
					success(res) {}
				})
			},
			async getInfo() {
				await this.$http
					.get(`${orderdetail}${this.orderNum}`)
					.then((res) => {
						this.goodsInfo = res.data;
					});
			},
			handleSubmit(data) {
				this.imgUrl = []
				data.map((ele) => [
					this.imgUrl.push(ele.url)
				])
				console.log(data);
			},
			subReverse() {
				this.form = {
					reason: this.reason,
					reverseType: this.type == 1 ? "2702" : "2701", //售后类型(2700数据字典):2702退货退款单/2701退款单
					seqId: this.goodsInfo.orderNum, //交易订单编号
					staffId: uni.getStorageSync("userInfo").staffId, // 客户员工id
					tradeOrderId: this.goodsInfo.id, //交易订单ID
					evidenceFiles: this.imgUrl.join()
				}
				this.$http
					.post(`${orderReverse}`, this.form)
					.then((res) => {
						uni.showToast({
							title: '提交成功',
							icon: 'none',
							duration: 1000
						})
						uni.$emit('refresh', {
							refresh: true
						})
						setTimeout(() => {
							uni.redirectTo({
								url: '/pages/order/afterSaleOrder'
							});
						}, 300)
					});



			}

		}
	}
</script>

<style scoped lang="scss">
	.img_box {
		overflow: hidden;
		padding: 20upx 0;

		.addImg {
			width: 184upx;
			height: 184upx;
			background: #f2f2f2;
			border-radius: 20upx;
			overflow: hidden;
			transition: all 0.3s;

			image {
				width: 51upx;
				height: 42upx;
				display: block;
				margin: 0 auto;
				margin-top: 45upx;
			}

			p {
				font-size: 24upx;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: rgba(255, 94, 102, 1);
				text-align: center;
				margin-top: 20upx;
			}

			&:active {
				transform: scaleX(0.96);
			}
		}
	}

	.img_list {
		width: 184upx;
		height: 184upx;
		border-radius: 20upx;
		float: left;
		margin-right: 30upx;
		position: relative;

		image,
		video {
			width: 100%;
			height: 100%;
			display: block;
		}

		video {
			position: relative;
			border-radius: 5upx;
			overflow: hidden;
			overflow: visible !important;

			.covers {
				//遮挡层
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				left: 0;
				z-index: 9990;
			}

			.imgs {
				width: 72upx;
				height: 72upx;
				position: absolute;
				top: 50%;
				left: 50%;
				z-index: 9999;
				transform: translate(-50%, -50%);
			}

			.video_close {
				width: 40upx;
				height: 40upx;
				position: absolute;
				display: block;
				top: -6upx;
				left: -6upx;
				border-radius: 50%;
				z-index: 9999;
			}
		}

		.close {
			width: 40upx;
			height: 40upx;
			position: absolute;
			box-sizing: border-box;
			top: -6upx;
			left: -6upx;
			border-radius: 50%;
		}
	}

	.youhui {
		height: 40upx;
		line-height: 40upx;
		padding: 20upx 0;
		border-bottom: 1upx solid #f2f2f2;
		box-sizing: content-box;

		.text1 {
			font-size: 28upx;
			color: #333333;
		}

		.text2 {
			float: right;
			color: #999999;
			font-size: 28upx;
			margin-right: 5upx;
			margin-top: 2upx;
		}

		.text3 {
			font-size: 24upx;
			color: #999999;
			margin-left: 10upx;
		}

		image {
			float: right;
			width: 12upx;
			height: 22upx;
			margin-top: 12upx;
			margin-left: 10upx;
		}
	}

	.page {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #f6f6f6;
	}

	/* 商品 */
	.goods-data {
		padding: 10rpx 4%;
		background-color: #ffffff;
		border-radius: 20rpx;
		margin: 20rpx auto 0 auto;

		.goods-title {
			display: flex;
			align-items: center;
			width: 100%;
			height: 80rpx;

			text {
				font-size: 26rpx;
				color: #222222;
			}
		}

		.goods-list {
			width: 100%;

			.list {
				display: flex;
				align-items: center;
				width: 100%;
				height: 200rpx;

				.thumb {
					display: flex;
					align-items: center;
					width: 30%;
					height: 100%;

					image {
						width: 160rpx;
						height: 160rpx;
						border-radius: 10rpx;
					}
				}

				.item {
					width: 70%;
					height: 160rpx;

					.title {
						display: flex;
						flex-direction: column;
						// justify-content: center;
						width: 100%;

						.name {
							font-size: 28rpx;
							color: #222222;
							height: 60rpx;
						}

						.attr {
							font-size: 24rpx;
							color: #c0c0c0;
						}
					}

					.price-number {
						display: flex;
						align-items: center;
						justify-content: space-between;
						width: 100%;
						height: 60rpx;

						.price {
							display: flex;
							align-items: center;

							text {
								color: $base;
								font-weight: bold;
							}

							.min {
								font-size: 26rpx;
							}

							.max {
								font-size: 32rpx;
							}
						}

						.number {
							display: flex;
							align-items: center;

							text {
								font-size: 26rpx;
								color: #222222;
							}
						}
					}

					.tag {
						display: flex;
						align-items: center;
						width: 100%;
						height: 40rpx;

						text {
							padding: 2rpx 12rpx;
							color: $base;
							border: 2rpx solid $base;
							border-radius: 40rpx;
							font-size: 24rpx;
						}
					}
				}
			}
		}

		.delivery {
			width: 100%;

			.list {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 80rpx;

				.title {
					font-size: 26rpx;
					color: #555555;
				}

				.content {
					display: flex;
					align-items: center;
					height: 40rpx;

					text {
						font-size: 26rpx;
						color: #222222;
					}

					.iconfont {
						// font-size: 24rpx;
						margin-top: 6rpx;
						margin-left: 10rpx;
					}

					.icon-check {
						font-size: 34rpx;
					}

					input {
						height: 80%;
						font-size: 26rpx;
						color: #222222;
						text-align: right;
					}
				}
			}
		}
	}

	/* 申请数据 */
	.apply-data {
		width: 100%;
		margin: 20rpx auto;
		background-color: #FFFFFF;
		border-radius: 20rpx;
		overflow: hidden;
		padding-bottom: 100rpx;

		.apply-list {
			padding: 0 4%;

			.list {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 100rpx;
				border-bottom: 2rpx solid #f6f6f6;

				.title {
					display: flex;
					align-items: center;

					text {
						font-size: 26rpx;
						color: #959595;
					}
				}

				.more {
					display: flex;
					align-items: center;

					text {
						font-size: 26rpx;
						font-weight: bold;
						color: #222222;
					}

					.iconfont {
						margin-left: 20rpx;
					}

					input {
						font-size: 26rpx;
						color: #222222;
						text-align: right;
					}
				}
			}

			.describe {
				padding: 20rpx;
				height: 200rpx;
				background-color: #f6f6f6;
				margin-bottom: 20rpx;

				textarea {
					width: 100%;
					height: 100%;
					font-size: 26rpx;
					background-color: #f6f6f6;
				}
			}

			.voucher {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				width: 100%;
				height: 200rpx;

				.thumb {
					position: relative;
					width: 25%;
					height: 160rpx;

					image {
						width: 160rpx;
						height: 160rpx;
					}

					text {
						position: absolute;
						right: 0rpx;
						top: -20rpx;
						font-size: 38rpx;
						background-color: #FFFFFF;
						border-radius: 100%;
					}
				}
			}
		}
	}

	/* 提交 */
	.submit-btn {
		position: fixed;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 122rpx;
		padding-bottom: 22rpx;

		.btn {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 90%;
			height: 70rpx;
			font-size: 28rpx;
			color: #FFFFFF;
			background: linear-gradient($base, $change-clor);
			border-radius: 100rpx;
		}
	}

	/* 申请原因弹窗 */
	.apply-cause-win {
		.cu-dialog {
			width: 100%;
			height: 60%;
			border-radius: 20rpx 20rpx 0 0 !important;

			.title {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100rpx;
				font-size: 28rpx;
				font-weight: bold;
			}

			.cause-list {
				padding: 0 4%;
				height: 90%;
				overflow: auto;

				.list {
					display: flex;
					align-items: center;
					justify-content: space-between;
					width: 100%;
					height: 100rpx;

					.cause {
						display: flex;
						align-items: center;
						font-size: 28rpx;
						color: #222222;
					}

					.check {
						display: flex;
						align-items: center;

						text {
							font-size: 34rpx;
							color: #959595;
						}
					}
				}
			}
		}
	}

	.address {
		margin-top: 30upx;
		box-shadow: 0upx 0upx 10upx #ddd;
		padding: 20upx;
		border-radius: 20upx;

		.address_type {
			height: 80upx;
			line-height: 80upx;
			display: flex;
			justify-content: space-between;

			.type_text {
				font-size: 28upx;
				color: #8D8D8D;
			}

			.type_right {
				font-size: 28upx;
				color: #333;
				display: flex;
				align-items: center;

				.more {
					width: 40upx;
					height: 40upx;
					display: block;
					margin-left: 20upx;
				}
			}
		}

		.rule {
			background-color: #FCF9E5;
			padding: 20upx;
			font-size: 24upx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.rule_left {
				width: 70%;
				color: #8D8D8D;
			}

			.rule_right {
				color: #E1251B;
			}
		}

		.user_address {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-bottom: 20upx;
			margin-top: 30upx;
			border-bottom: 1upx solid #EEEEEE;

			.u_a_top {
				height: 60upx;
				line-height: 60upx;
				font-size: 28upx;
				color: #000000;
				font-weight: bold;
				display: flex;
				align-content: center;

				.iconfont {
					font-weight: 500;
					margin-right: 20upx;
					font-size: 38upx;
					color: #666666;
				}

				.name {
					margin-right: 10upx;
					font-size: 30upx;
				}
			}

			.u_a_bottom {
				font-size: 24upx;
				color: #8D8D8D;
				margin-left: 55upx;
				margin-top: 10upx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			image {
				width: 40upx;
				height: 40upx;
				display: block;
			}

			&:active {
				opacity: .8;
			}
		}

		.times {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30upx 0;

			.rili {
				width: 35upx;
				height: 35upx;
				padding: 5upx;
				box-sizing: content-box;
			}

			.times_title {
				width: 84%;
				text-align: left;
				color: #000000;
			}

			.more {
				height: 40upx;
				width: 40upx;
				display: block;
			}

		}

		.m_a_title {
			font-size: 28upx;
			color: #8D8D8D;
			height: 80upx;
			line-height: 80upx;

			text {
				margin-left: 20upx;
				font-size: 22upx;
			}
		}
	}

	.contacts {
		margin-top: 30upx;
		box-shadow: 0upx 0upx 10upx #ddd;
		padding: 20upx;
		border-radius: 20upx;

		.user_cell {
			height: 80upx;
			line-height: 80upx;
			display: flex;
			align-items: center;

			image {
				width: 40upx;
				height: 40upx;
				display: block;
			}

			text {
				font-size: 28upx;
				color: #000000;
				font-weight: bold;
				margin-left: 20upx;
			}
		}
	}

	.tips {
		font-size: 24upx;
		color: #999999;
		height: 40upx;
		line-height: 40upx;
		margin-top: 40upx;
		text-align: center;
	}

	.btns {
		width: 100%;
		height: 80upx;
		line-height: 80upx;
		font-size: 30upx;
		color: #FFFFFF;
		text-align: center;
		margin: 20upx 0;
		border-radius: 8upx;

		&:active {
			opacity: .8;
		}
	}

	.contact {
		height: 100rpx;
		width: 100%;
		background-color: #FFFFFF;
		font-size: 26rpx;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;

		text {
			font-size: 28rpx;
			color: #555555;
		}

		.iconfont {
			font-size: 34rpx;
			margin-right: 20rpx;
		}
	}



	.price {
		text {
			font-size: 28rpx;
			font-weight: bold;
			color: #EE822F;
		}
	}
</style>