<template>
	<view class="page">
		<!-- 订单tab -->
		<!-- <view class="order-tab">
      <view
        class="tab"
        :class="{ action: OrderType == 0 }"
        @click="onOrderTab(0)"
      >
        <text>待审核</text>
        <text class="line"></text>
      </view>
      <view
        class="tab"
        :class="{ action: OrderType == 1 }"
        @click="onOrderTab(1)"
      >
        <text>售后中</text>
        <text class="line"></text>
      </view>
      <view
        class="tab"
        :class="{ action: OrderType == 2 }"
        @click="onOrderTab(2)"
      >
        <text>已完成</text>
        <text class="line"></text>
      </view>
      <view
        class="tab"
        :class="{ action: OrderType == 3 }"
        @click="onOrderTab(3)"
      >
        <text>已撤销</text>
        <text class="line"></text>
      </view>
      <view
        class="tab"
        :class="{ action: OrderType == 4 }"
        @click="onOrderTab(4)"
      >
        <text>已驳回</text>
        <text class="line"></text>
      </view>
    </view> -->
		<!-- 订单搜索 -->
		<view class="order-search">
			<!-- <view class="search">
        <text class="iconfont icon-fadajing"></text>
        <input type="text" placeholder="商品名称/商品编号/订单号/序列号" />
      </view>
      <view class="search">搜索</view> -->
			<view class="search-box">
				<mSearch class="mSearch-input-box" :mode="2" button="inside" :placeholder="'订单编号'" @search="onSearch(false)"
					@confirm="onSearch(false)" v-model="keyword"></mSearch>
			</view>

			<!-- <view class="filtrate" @click="isFiltrate = true">
        <text>筛选</text>
      </view> -->
		</view>
		<!-- 订单列表 -->
		<view class="order-list">
			<view class="list" v-for="(item, index) in afterSaleList" :key="index">
				<view class="order-number" v-if="item.tradeOrderDetailList.length > 0">
					<view class="number">
						<text>订单编号：{{ item.seqId }}</text>
					</view>
					<view class="type">
						<image src="/static/sale_th.png" mode=""></image>
						<text>{{ item.reverseType }}</text>
					</view>
				</view>
				<view class="goods-list" v-if="item.tradeOrderDetailList.length > 0">
					<view class="list" @click="handleListClick(item)">
						<view class="list-item" v-for="list in item.tradeOrderDetailList" :key="list.id">
							<view class="thumb">
								<image :src="list.saleSkuInfo.picUrl[0].url" mode=""></image>
							</view>
							<view class="item">
								<view class="title">
									<text class="two-omit">{{ list.itemName }}</text>
								</view>
								<view class="num">
									<text>数量：{{ list.itemNum }}</text>
									<text class="price">￥{{ list.payAmount || 0 }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="order-status" v-if="item.tradeOrderDetailList.length > 0">
					<text>{{
            item.reverseRecordlList[item.reverseRecordlList.length - 1].title
          }}</text>
					<!-- <text>{{item.returnStatus}}</text> -->
					<text>{{
            item.reverseRecordlList[item.reverseRecordlList.length - 1].content
          }}</text>
				</view>
				<!-- <view class="order-btn" v-if="OrderType === 0">
          <text class="action">申请售后</text>
        </view> -->
			</view>
		</view>
		<!-- 筛选弹窗 -->
		<view class="filtrate-win" @click="isFiltrate = false">
			<view class="cu-modal drawer-modal justify-end" :class="{ show: isFiltrate }">
				<view class="cu-dialog basis-lg">
					<view class="order-time">
						<view class="title">
							<text>下单时间</text>
						</view>
						<view class="time-list">
							<view class="list action">
								<text>全部</text>
							</view>
							<view class="list">
								<text>一个月内</text>
							</view>
							<view class="list">
								<text>一个月至三个月</text>
							</view>
							<view class="list">
								<text>三个月六个月</text>
							</view>
							<view class="list">
								<text>六个月至一年</text>
							</view>
							<view class="list">
								<text>一年以上</text>
							</view>
						</view>
					</view>
					<view class="footer-btn">
						<view class="btn" @click="isFiltrate = false">
							<text>重置</text>
						</view>
						<view class="btn action" @click="isFiltrate = false">
							<text>确定</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<rfLoading isFullScreen :active="isLoading"></rfLoading>
	</view>
</template>

<script>
	import mSearch from "@/components/rf-search/rf-search.vue";
	import {
		afterSaleApi
	} from "@/api";
	export default {
		components: {
			mSearch,
		},
		data() {
			return {
				OrderType: 0,
				isFiltrate: false,
				keyword: "",
				queryParams: {
					page: 1,
					size: 10,
					reverseType: "", //售后类型
					auditStatus: "",
					seqIdOrPhone: "",
				},
				total: 0,
				// 售后订单列表
				afterSaleList: [],
				isLoading: false
			};
		},
		onLoad() {
			// this.$mHelper.loadVerify();
			this.getAfterSaleList();
		},
		onReachBottom() {
			this.getOrderData();
		},
		onPullDownRefresh() {
			if (this.isLoading) return
			uni.startPullDownRefresh({
				success: () => {
					this.resetParams()
					this.getOrderData()
				},
			});
			uni.stopPullDownRefresh();
		},
		methods: {
			/**
			 * 订单tab切换状态
			 * @param {Number} type
			 */
			onOrderTab(type) {
				this.OrderType = type;
			},
			/**
			 * 搜索点击
			 */
			onSearch() {
				// 执行搜索
				console.log(this.keyword);
				this.getAfterSaleList();
			},
			// 过滤状态
			onFilterState(type) {
				let allType = {
					2801: "待审核",
					2802: "已驳回",
					2803: "售后中",
					2804: "已完成",
					2805: "已撤销",
				};
				if (type) {
					let status = allType[type];
				}
				console.log(status);
				return status;
			},
			// 获取售后列表数据
			async getAfterSaleList() {
				if (this.isLoading) return
				if (this.afterSaleList.length > 0 && this.afterSaleList.length >= this.total) return
				this.isLoading = true;
				let params = {
					pageNumber: this.queryParams.page,
					pageSize: this.queryParams.size,
					reverseType: this.queryParams.reverseType,
					auditStatus: this.queryParams.auditStatus,
					seqIdOrPhone: this.keyword,
				};
				let res = await this.$http.get(`${afterSaleApi}`, params);
				this.afterSaleList = res.data.rows;
				this.total = +res.data.total;
				this.isLoading = false
			},
			// 列表点击
			handleListClick(data) {
				// console.log("list");
				// uni.setStorageSync("afterSaleList", data);
				uni.navigateTo({
					url: `/pages/order/afterSaleOrderDetail?id=${data.id}`,
				});
			},
		},
	};
</script>

<style scoped lang="scss">
	page {
		width: 100vw;
		height: 100vh;
		background-color: #f6f6f6;
	}

	.page {
		position: absolute;
		left: 0;
		top: 0;
		width: 100vw;
		height: 100vh;
		background-color: #f6f6f6;
		padding: 20rpx 4%;
	}

	/* 订单tab */
	.order-tab {
		position: fixed;
		left: 0;
		top: 0;
		/* #ifdef H5 */
		top: 88rpx;
		/* #endif */
		z-index: 10;
		display: flex;
		align-items: center;
		width: 100%;
		height: 100rpx;
		background-color: #ffffff;

		.tab {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 205%;
			height: 80%;

			text {
				font-size: 26rpx;
				color: #959595;
			}
		}

		.action {
			text {
				color: #222222;
			}

			.line {
				position: absolute;
				left: 50%;
				bottom: 0;
				width: 60rpx;
				height: 6rpx;
				background: linear-gradient(to right, $base, #f6f6f6);
				transform: translate(-50%, 0);
			}
		}
	}

	/* 售后申请 */
	.order-search {
		display: flex;
		align-items: center;
		height: 80rpx;
		background-color: #f2f2f2;
		margin-top: 20rpx;

		/* .search {
    display: flex;
    align-items: center;
    width: 90%;
    height: 60rpx;
    background-color: #f6f6f6;
    border-radius: 70rpx;
    padding: 0 20rpx;

    text {
      font-size: 32rpx;
      color: #555555;
    }

    input {
      width: 90%;
      height: 100%;
      font-size: 26rpx;
      color: #555555;
      margin-left: 3%;
    }
  } */

		.filtrate {
			display: flex;
			align-items: center;
			justify-content: flex-end;
			width: 10%;
			height: 100%;

			text {
				font-size: 26rpx;
				color: #959595;
			}
		}
	}

	/* 订单列表 */
	.order-list {
		width: 100%;
		margin: 20rpx auto;
		background-color: #f6f6f6;


		.list {
			padding: 0 0 4% 0;
			background: #fff;
			margin-bottom: 20rpx;

			.order-number {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 60rpx;
				padding: 10rpx 20rpx;

				.number {
					display: flex;
					align-items: center;

					text {
						font-size: 26rpx;
						color: #959595;
					}
				}

				.type {
					display: flex;
					align-items: center;

					image {
						width: 34rpx;
						height: 34rpx;
					}

					text {
						font-size: 26rpx;
						font-weight: bold;
						color: #222222;
						margin-left: 10rpx;
					}
				}
			}

			.goods-list {
				padding: 5rpx 4%;
				background-color: #ffffff;
				border-radius: 20rpx;
				margin-bottom: 0;
				width: 100%;

				.list {
					/* display: flex;
        align-items: center; */
					width: 100%;

					.list-item {
						display: flex;
						align-items: center;
						width: 100%;
						height: 200rpx;
					}

					.thumb {
						display: flex;
						align-items: center;
						width: 30%;
						height: 100%;

						image {
							width: 160rpx;
							height: 160rpx;
						}
					}

					.item {
						width: 70%;
						height: 100%;

						.title {
							display: flex;
							align-items: center;
							width: 100%;
							height: 120rpx;

							text {
								color: #222222;
								font-size: 26rpx;
							}
						}

						.num {
							display: flex;
							width: 100%;
							justify-content: space-between;
							align-items: center;

							text {
								color: #959595;
								font-size: 26rpx;
							}

							.price {
								color: #333;
								font-size: 28rpx;
							}
						}
					}
				}
			}

			.order-status {
				display: flex;
				align-items: center;
				padding: 0 4%;
				min-height: 70rpx;
				background-color: #f6f6f6;
				border-radius: 20rpx;
				width: 90%;
				margin: auto;

				text {
					font-size: 28rpx;
					color: #959595;
				}

				text:nth-child(1) {
					color: #222222;
					margin-right: 20rpx;
				}

				text:nth-child(2) {
					padding: 15rpx 0;
					flex: 1;
				}
			}

			.order-btn {
				display: flex;
				align-items: center;
				justify-content: flex-end;
				width: 100%;
				height: 100rpx;
				background-color: #ffffff;

				text {
					padding: 10rpx 30rpx;
					font-size: 26rpx;
					color: #959595;
					border: 2rpx solid #eeeeee;
					border-radius: 100rpx;
				}

				.action {
					border: 2rpx solid $base;
					color: $base;
				}
			}
		}
	}

	/* 筛选弹窗 */
	.filtrate-win {
		.cu-dialog {
			top: 0;
			height: calc(100vh);
			flex-basis: 90% !important;
			background-color: #ffffff;

			// 下单时间
			.order-time {
				padding: 0 4%;

				.title {
					display: flex;
					align-items: center;
					width: 100%;
					height: 100rpx;

					text {
						font-size: 28rpx;
						color: #222222;
					}
				}

				.time-list {
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
					width: 100%;

					.list {
						display: flex;
						align-items: center;
						justify-content: center;
						width: 48%;
						height: 60rpx;
						background-color: #f6f6f6;
						border: 2rpx solid #ffffff;
						border-radius: 60rpx;
						margin-bottom: 20rpx;

						text {
							font-size: 26rpx;
							color: #222222;
						}
					}

					.action {
						background-color: $rgba-02;
						border: 2rpx solid $base;

						text {
							color: $base;
						}
					}
				}
			}

			// 按钮
			.footer-btn {
				position: absolute;
				left: 0;
				bottom: 0;
				display: flex;
				align-items: center;
				width: 100%;
				height: 100rpx;
				border-top: 2rpx solid #f6f6f6;

				.btn {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 50%;
					height: 100%;

					text {
						font-size: 28rpx;
						color: #555555;
					}
				}

				.action {
					background: linear-gradient(to right, $base, $change-clor);

					text {
						color: #ffffff;
					}
				}
			}
		}
	}

	.search-box {
		width: 100%;
		/* background-color: rgb(242, 242, 242); */
		/* padding: 15upx 2.5%; */
		display: flex;
		justify-content: space-between;

		.mSearch-input-box {
			width: 100%;
		}

		.input-box>input {
			width: 100%;
			height: 60upx;
			font-size: 32upx;
			border: 0;
			border-radius: 60upx;
			-webkit-appearance: none;
			-moz-appearance: none;
			appearance: none;
			padding: 0 3%;
			margin: 0;
			background-color: #ffffff;
		}
	}
</style>