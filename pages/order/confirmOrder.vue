<template>
	<view class="page">
		<!-- 地址 -->
		<!-- <view class="address-data">
      <view class="address-list" @click="onSkip('address')">
        <view class="list">
          <text>黑龙江哈尔滨市道里区城区</text>
        </view>
        <view class="list">
          <text class="address">爱建路1333号</text>
        </view>
        <view class="list">
          <text>张三</text>
          <text>178****8888</text>
        </view>
        <view class="list">
          <text class="tips">(如果快递不方便接收，您可以选择暂时寄存服务)</text>
        </view>
      </view>
      <view class="bar">

      </view>
    </view> -->
		<view class="shipping-address">
			<view class="address">
				<text class="iconfont icon-dizhi"></text>
				<text>{{ userInfo.fullAddress }}</text>
			</view>
			<view class="name-phone">
				<text>{{ userInfo.staffName }}</text>
				<text>{{ userInfo.mobile }}</text>
			</view>
		</view>
		<!-- 商品 -->
		<view class="goods-data">
			<view class="goods-title">
				<text>商品信息</text>
			</view>
			<view class="goods-list">
				<view class="list" v-for="(item, index) in goodsInfo.tradeOrderDetailList" :key="index">
					<view class="thumb">
						<image :src="item.saleSkuInfo.picUrl[0].url" mode=""></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="name one-omit">{{ item.itemName }}</text>
							<text class="attr"><text>{{
                  item.saleSkuInfo.saleAttrVals.map((item) => item.val)
                }}</text></text>
						</view>
						<view class="price-number">
							<view class="price">
								<text class="min">￥</text>
								<text class="max">{{ item.actualUnitPrice }}</text>
							</view>
							<view class="number">
								<text>x {{ item.itemNum }}</text>
							</view>
						</view>
						<!-- <view class="tag">
							<text>支持七天无理由退货</text>
						</view> -->
					</view>
				</view>
			</view>
			<!-- <view class="delivery">
        <div class="list">
          <view class="title">配送</view>
          <view class="content">
            <text>快递运输</text>
            <text class="iconfont icon-more"></text>
          </view>
        </div>
        <div class="list">
          <view class="title">运费险</view>
          <view class="content">
            <text>￥10.00</text>
            <text class="iconfont icon-check"></text>
          </view>
        </div>
        <div class="list">
          <view class="title">留言</view>
          <view class="content">
            <input type="text" placeholder="选填,建议先和商家沟通确认">
          </view>
        </div>
      </view> -->
		</view>
		<view>
			<!-- <text>联系客服</text> -->
			<button class="contact" @click="bindcontact">
				<text class="iconfont icon-kefu"></text> 联系客服
			</button>
		</view>
		<!-- 优惠 -->
		<!-- <view class="discounts-data">
      <view class="discounts">
        <div class="list" @click="$refs['UseCoupon'].show()">
          <view class="title">优惠券</view>
          <view class="content">
            <text>无可用</text>
            <text class="iconfont icon-more"></text>
          </view>
        </div>
        <div class="list">
          <view class="title">积分</view>
          <view class="content">
            <text>共300，满1000可用</text>
            <text class="iconfont icon-more"></text>
          </view>
        </div>
      </view>
    </view> -->
		<!-- 订单金额 -->
		<view class="order-price">
			<view class="price-list">
				<view class="list">
					<view class="title">
						<text>商品总额</text>
					</view>
					<view class="price" v-if="goodsInfo.actualGoodsAmount">
						<text class="highlight">￥{{ goodsInfo.actualGoodsAmount }}</text>
					</view>
				</view>
				<!-- <view class="list">
          <view class="title">
            <text>会员折扣</text>
          </view>
          <view class="price">
            <text>-￥19.00</text>
          </view>
        </view> -->
				<view class="list">
					<view class="title">
						<text>运费</text>
					</view>
					<view class="price" v-if="goodsInfo.shippingFee">
						<text class="highlight">+￥{{ goodsInfo.shippingFee }}</text>
					</view>
				</view>
				<div class="list">
					<view class="title"><text>配送</text></view>
					<view class="content">
						<text>快递运输</text>
						<text class="iconfont icon-more"></text>
					</view>
				</div>
				<view class="list action">
					<view class="title">
						<text>应付金额</text>
					</view>
					<view class="price" v-if="goodsInfo.actualAmount">
						<text class="highlight">￥{{ goodsInfo.actualAmount }}</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 地址提示 -->
		<view class="address-tips" :style="scrollTop >= 100 ? '' : 'display:none'">
			<text> {{ userInfo.fullAddress }}</text>
		</view>
		<!-- 底部合计提交 -->
		<!-- <view class="footer-submit">
      <view class="price">
        <text class="min">￥</text>
        <text class="max">299</text>
        <text class="min">.00</text>
      </view>
      <view class="submit" @click="onSubmit">
        <text>提交订单</text>
      </view>
    </view> -->
		<view class="footer-btn" v-if="goodsInfo">
			<view class="del" @click.stop="onOrderCalcel">
				<text>取消订单</text>
			</view>
			<view class="btn action" @click.stop="subOrder">
				<text>确认订单</text>
			</view>
		</view>
		<!-- 优惠券 -->
		<use-coupon ref="UseCoupon"></use-coupon>
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
	import {
		orderCreate,
		orderPreview
	} from "@/api/order";
	import UseCoupon from "@/components/order/useCoupon.vue";
	export default {
		components: {
			// 优惠券
			UseCoupon,
		},
		data() {
			return {
				loading: false,
				scrollTop: 0,
				saleSkuBuyParams: {},
				userInfo: uni.getStorageSync("userInfo"),
				goodsInfo: {},
			};
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		onLoad(params) {
			// 购物车传过来的参数
			// this.$mHelper.loadVerify();
			let {
				saleSkuBuyParams,
				tradeOrderSource
			} = params;
			this.$nextTick(() => {
				this.tradeOrderSource = tradeOrderSource;
				this.saleSkuBuyParams = JSON.parse(saleSkuBuyParams);
				this.getInfo();
			})

		},
		methods: {
			// 客服
			bindcontact() {
				wx.openCustomerServiceChat({
					extInfo: {
						url: 'https://work.weixin.qq.com/kfid/kfca73cb4d1464a6a43'
					},
					corpId: 'ww8f007820b431a55e',
					success(res) {}
				})
			},
			onOrderCalcel() {
				uni.navigateBack();
			},
			async getInfo() {
				await this.$http
					.post(`${orderPreview}`, {
						// buyerId: uni.getStorageSync("userInfo").staffId,
						buyerRemark: "",
						saleSkuBuyParams: this.saleSkuBuyParams,
					})
					.then((res) => {
						this.goodsInfo = res.data;
					});
			},
			/**
			 * 确认订单
			 */
			async subOrder() {
				let verify = await this.$http.post(this.$mHelper.verifyAccessToken);
				if (verify.data === 2) {
					this.$mHelper.toast("网络异常，请稍后再试...");
					this.$mHelper.relogin();
					return false;
				}
				const _this = this;
				this.loading = true;
				await this.$http
					.post(`${orderCreate}`, {
						// buyerId: uni.getStorageSync("userInfo").staffId,
						id: this.goodsInfo.id,
						buyerRemark: "",
						saleSkuBuyParams: this.saleSkuBuyParams,
						tradeOrderSource: this.tradeOrderSource,
					})
					.then((res) => {
						console.log(res);
						_this.loading = false;
						uni.redirectTo({
							url: `/pages/order/subPay?orderNum=${res.data.orderNum}&Pirce=${res.data.actualAmount}&payExpireSec=${res.data.payExpireSec}`,
						});
					});
			},
			/**
			 * 跳转点击
			 * @param {String} type 跳转类型
			 */
			onSkip(type) {
				switch (type) {
					case "address":
						uni.navigateTo({
							url: "/pages/AddressList/AddressList",
						});
						break;
				}
			},
		},
	};
</script>

<style lang="scss">
	.page {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		// height: 100%;
		background-color: #f6f6f6;
		padding-bottom: 180rpx;
	}

	/* 收货地址 */
	.shipping-address {
		width: 100%;
		min-height: 200rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		margin: 20rpx auto 20rpx;

		.name-phone {
			display: flex;
			align-items: center;
			padding: 0 14%;

			text {
				font-size: 26rpx;
				color: #323333;
				margin-right: 20rpx;
			}
		}

		.address {
			display: flex;
			padding: 4%;
			min-height: 100rpx;

			text {
				font-size: 28rpx;
				font-weight: bold;
				color: #222222;
			}

			.icon-dizhi {
				color: #1f1f1f;
				font-size: 40rpx;
				margin-right: 40rpx;
				font-weight: normal;
			}
		}
	}

	/* 地址 */
	.address-data {
		position: relative;
		padding: 10rpx 4%;
		background-color: #ffffff;
		border-radius: 0 0 20rpx 20rpx;
		overflow: hidden;

		.bar {
			position: absolute;
			left: 0;
			bottom: 0;
			width: 100%;
			height: 10rpx;
			background-color: #cccccc;
			background-image: linear-gradient(45deg,
					rgba(255, 255, 255, 0.15) 25%,
					transparent 25%,
					transparent 50%,
					rgba(255, 255, 255, 0.15) 50%,
					rgba(255, 255, 255, 0.15) 75%,
					transparent 75%,
					transparent);
			background-size: 72rpx 72rpx;
		}

		.address-list {
			width: 100%;

			.list {
				display: flex;
				align-items: center;
				width: 100%;
				height: 60rpx;

				text {
					font-size: 24rpx;
					color: #555555;
					margin-right: 10rpx;
				}

				.address {
					font-size: 32rpx;
					color: #222222;
				}

				.tips {
					color: $base;
				}
			}
		}
	}

	/* 商品 */
	.goods-data {
		padding: 10rpx 4%;
		background-color: #ffffff;
		border-radius: 20rpx;
		margin: 20rpx auto 0 auto;

		.goods-title {
			display: flex;
			align-items: center;
			width: 100%;
			height: 80rpx;

			text {
				font-size: 26rpx;
				color: #222222;
			}
		}

		.goods-list {
			width: 100%;

			.list {
				display: flex;
				align-items: center;
				width: 100%;
				height: 200rpx;

				.thumb {
					display: flex;
					align-items: center;
					width: 30%;
					height: 100%;

					image {
						width: 160rpx;
						height: 160rpx;
						border-radius: 10rpx;
					}
				}

				.item {
					width: 70%;
					height: 160rpx;

					.title {
						display: flex;
						flex-direction: column;
						// justify-content: center;
						width: 100%;

						.name {
							font-size: 28rpx;
							color: #222222;
							height: 60rpx;
						}

						.attr {
							font-size: 24rpx;
							color: #c0c0c0;
						}
					}

					.price-number {
						display: flex;
						align-items: center;
						justify-content: space-between;
						width: 100%;
						height: 60rpx;

						.price {
							display: flex;
							align-items: center;

							text {
								color: $base;
								font-weight: bold;
							}

							.min {
								font-size: 26rpx;
							}

							.max {
								font-size: 32rpx;
							}
						}

						.number {
							display: flex;
							align-items: center;

							text {
								font-size: 26rpx;
								color: #222222;
							}
						}
					}

					.tag {
						display: flex;
						align-items: center;
						width: 100%;
						height: 40rpx;

						text {
							padding: 2rpx 12rpx;
							color: $base;
							border: 2rpx solid $base;
							border-radius: 40rpx;
							font-size: 24rpx;
						}
					}
				}
			}
		}

		.delivery {
			width: 100%;

			.list {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 80rpx;

				.title {
					font-size: 26rpx;
					color: #555555;
				}

				.content {
					display: flex;
					align-items: center;
					height: 40rpx;

					text {
						font-size: 26rpx;
						color: #222222;
					}

					.iconfont {
						// font-size: 24rpx;
						margin-top: 6rpx;
						margin-left: 10rpx;
					}

					.icon-check {
						font-size: 34rpx;
					}

					input {
						height: 80%;
						font-size: 26rpx;
						color: #222222;
						text-align: right;
					}
				}
			}
		}
	}

	/* 优惠 */
	.discounts-data {
		width: 100%;
		margin: 20rpx auto;
		background-color: #ffffff;
		border-radius: 20rpx;

		.discounts {
			padding: 0 4%;

			.list {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 80rpx;

				.title {
					font-size: 26rpx;
					color: #555555;
				}

				.content {
					display: flex;
					align-items: center;
					height: 40rpx;

					text {
						font-size: 26rpx;
						color: #222222;
					}

					.iconfont {
						// font-size: 24rpx;
						margin-top: 6rpx;
						margin-left: 10rpx;
					}

					.icon-check {
						font-size: 34rpx;
					}

					input {
						height: 80%;
						font-size: 26rpx;
						color: #222222;
						text-align: right;
					}
				}
			}
		}
	}

	/**
 * 地址提示
 */
	.address-tips {
		position: fixed;
		left: 0;
		bottom: 100rpx;
		z-index: 100;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 60rpx;
		padding: 0 4%;
		background-color: #fef2ce;

		text {
			font-size: 26rpx;
			color: #fbbd08;
		}
	}

	/* 订单金额 */
	.order-price {
		width: 100%;
		margin: 20rpx auto;
		background-color: #ffffff;
		border-radius: 20rpx;

		.price-list {
			padding: 0 4%;

			.list {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 80rpx;

				.title {
					display: flex;
					align-items: center;

					text {
						font-size: 26rpx;
						color: #555555;
					}
				}

				.price {
					display: flex;
					align-items: center;

					text {
						font-size: 26rpx;
						font-weight: bold;
						color: #222222;
					}

					.highlight {
						color: $base;
					}
				}
			}

			.action {
				display: flex;
				align-items: center;
				justify-content: flex-end;

				.price {
					font-size: 32rpx;
					font-weight: bold;
					color: $base;
				}
			}
		}
	}

	/* 顶部合计提交 */
	.footer-submit {
		position: fixed;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		height: 100rpx;
		background-color: #ffffff;
		padding: 0 4%;

		.price {
			display: flex;
			align-items: flex-end;

			text {
				font-weight: bold;
				color: $base;
			}

			.min {
				font-size: 32rpx;
			}

			.max {
				font-size: 48rpx;
			}
		}

		.submit {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 200rpx;
			height: 70rpx;
			background-color: $base;
			border-radius: 70rpx;

			text {
				font-size: 26rpx;
				color: #ffffff;
			}
		}
	}

	.contact {
		width: 100%;
		height: 100rpx;
		background-color: #ffffff;
		font-size: 26rpx;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;

		text {
			font-size: 28rpx;
			color: #555555;
		}

		.iconfont {
			font-size: 34rpx;
			margin-right: 20rpx;
		}
	}

	.footer-btn {
		position: fixed;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		padding: 0 10%;
		margin: auto;
		height: 84px;
		// padding-bottom: 24rpx;
		background-color: #ffffff;
		border-top: 2rpx solid #eeeeee;

		.del {
			display: flex;
			align-items: center;
			width: 50%;
			height: 40px;
			color: #fff;
			background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
			border-radius: 56px 0px 0px 56px;
			text-align: center;
			justify-content: center;

			text {
				padding: 10rpx 30rpx;
				font-size: 28rpx;
				font-weight: bold;
			}
		}

		.btn {
			display: flex;
			align-items: center;
			width: 50%;
			height: 40px;
			color: #c0c0c0;
			border: 2rpx solid #c0c0c0;
			border-radius: 0px 56px 56px 0px;
			text-align: center;
			justify-content: center;

			text {
				padding: 10rpx 30rpx;
				font-size: 28rpx;
				font-weight: bold;
			}

			&.action {
				background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
				background-color: $base;
				color: #ffffff;
				border: 2rpx solid #ffffff;
				color: #fff;
				border: none;
			}
		}
	}
</style>