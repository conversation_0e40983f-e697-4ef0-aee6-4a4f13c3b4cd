<template>
	<view class="step">
		<view class="step_box">
			<!-- 左侧进度线 -->
			<view class="step_left"> </view>
			<view class="step_right">
				<view class="right_content" v-for="(item, index) in stepData" :key="index">
					<!-- 进度名称 -->
					<block>
						<p class="title" :style="{ color: item.type == 1 ? '#202020' : '#999' }">
							{{ item.title }}
						</p>
					</block>
					<!-- <block v-if="item.isNow == 1">
            <p
              class="title"
              :style="{ color: item.type == 1 ? colors : '#999' }"
            >
              {{ item.title }}
            </p>
          </block> -->
					<!-- 进度时间 -->
					<p class="times">{{ item.createdAt }}</p>
					<!-- 进度详情备注 -->
					<p class="result" v-if="item.content && item.content !== ''">
						<text style="color: #202020">{{ item.content }}</text>
					</p>
					<!-- 右侧的进度点 -->
					<p class="status" :style="{
            background: item.isNow == 1 ? colors : '#ccc',
            borderColor: item.isNow == 1 ? colors : '#ccc',
          }"></p>
				</view>
			</view>
		</view>
		<view class="info-box">
			<view class="title"> 退款信息 </view>
			<view class="goods-list"
				v-if="afterSaleList.tradeOrderDetailList && afterSaleList.tradeOrderDetailList.length > 0">
				<view class="list">
					<view class="list-item" v-for="list in afterSaleList.tradeOrderDetailList" :key="list.id">
						<view class="thumb">
							<image :src="list.saleSkuInfo.picUrl[0].url" mode=""></image>
						</view>
						<view class="item">
							<view class="title">
								<text class="two-omit">{{ list.itemName }}</text>
							</view>
							<view class="num">
								<text>数量：{{ list.itemNum }}</text>
								<text class="price">￥{{ list.payAmount || 0 }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 申请数据 -->
		<view class="apply-data" v-if="afterSaleList.auditStatus == 2806">
			<view class="apply-list">
				<!-- <view class="list" @click="isApplyCause = true">
					<view class="title">
						<text>申请原因</text>
					</view>
					<view class="more">
						<text>请选择申请原因</text>
						<text class="iconfont icon-more1"></text>
					</view>
				</view> -->
				<!-- 退货的返回方式以及收件地址 -->
				<view class="address">
					<view class="address_type">
						<text class="type_text">返回方式</text>
						<view class="type_right">
							<text>自行寄回</text>
						</view>
					</view>
					<!-- <view class="rule">
						<view class="rule_left">如因个人原因退/换货，将在换新商品签收时收取8元运费</view>
						<view class="rule_right">
							运费规则
						</view>
					</view> -->

				</view>
				<view class="list">
					<view class="title">
						<text>物流单号</text>
					</view>
					<view class="more">
						<input type="text" v-model="form.logisticsCode" placeholder="请输入物流单号" />
					</view>

				</view>
				<view class="list">
					<view class="title">
						<text>物流公司</text>
					</view>
					<view class="more">
						<input type="text" v-model="form.logisticsCompany" placeholder="请输入物流公司" />
					</view>

				</view>
				<view class="list">
					<view class="title">
						<text>联系电话</text>
					</view>
					<view class="more">
						<input type="number" v-model="form.telephone" placeholder="请输入联系电话" />
					</view>

				</view>
				<view style="padding-top:20rpx">
					<p style="font-size: 26rpx;color: #959595;">
						<text class="text">上传凭证</text>
						<!-- <text class="text3">(最多3张)</text> -->
					</p>
					<view class="img_box">
						<FileUpload @submit="handleSubmit">
						</FileUpload>
					</view>
				</view>

				<p class="tips">
					请联系客服获取退货地址，并填写退货单号
				</p>
				<button style=" margin-top:20rpx" class="contact" @click="bindcontact">
					<text class="iconfont icon-kefu"></text> 联系客服
				</button>


			</view>

		</view>
		<!-- 提交 -->
		<view class="submit-btn" v-if="afterSaleList.auditStatus == 2806">
			<view class="btn" @click="subReverse">提交</view>
		</view>
		<view class="submit-btn" v-if="afterSaleList.auditStatus == 2801">
			<view class="btn" @click="subRevoke">撤销申请</view>
		</view>

	</view>
</template>

<script>
	import {
		fillInReturnOrder,
		ordergetOne,
		orderRevoke
	} from "@/api/order";
	import FileUpload from "@/components/file-upload/index.vue";
	export default {
		components: {
			FileUpload,
		},
		data() {
			return {
				id: null,
				isShow: true,
				colors: "#f00",
				stepData: [],
				afterSaleList: {},
				imgUrl: [],
				form: {
					logisticsCode: "",
					logisticsCompany: "",
					telephone: "",
				}
			};
		},
		onLoad(params) {
			// this.$mHelper.loadVerify();
			this.id = params.id;
			this.init()
		},
		methods: {
			async init() {
				let res = await this.$http.get(`${ordergetOne}`, {
					id: this.id
				});
				this.afterSaleList = res.data;
				// let list = uni.getStorageSync("afterSaleList");
				if (this.afterSaleList.reverseRecordlList.length > 0) {
					this.stepData = this.afterSaleList.reverseRecordlList;
				}
			},
			// 客服
			bindcontact() {
				wx.openCustomerServiceChat({
					extInfo: {
						url: 'https://work.weixin.qq.com/kfid/kfca73cb4d1464a6a43'
					},
					corpId: 'ww8f007820b431a55e',
					success(res) {}
				})
			},
			handleSubmit(data) {
				this.imgUrl = []
				data.map((ele) => [
					this.imgUrl.push(ele.url)
				])
				console.log(data);
			},
			subRevoke() {
				this.$http.put(`${orderRevoke}`, {
					reverseOrderId: this.afterSaleList.reverseOrderId
				}).then(() => {
					this.$mHelper.toast('撤销成功');
					this.init()
				})
			},
			subReverse() {
				console.log(this.imgUrl);
				this.form = {
					...this.form,
					"reverseOrderId": this.afterSaleList.reverseOrderId, //售后单id
					evidenceFiles: this.imgUrl.join()
				}
				this.$http
					.post(`${fillInReturnOrder}`, this.form)
					.then((res) => {
						uni.showToast({
							title: '提交成功',
							icon: 'none',
							duration: 1000
						})
						setTimeout(() => {
							uni.redirectTo({
								url: '/pages/order/afterSaleOrder'
							});
						}, 300)
					});



			}
		},
	};
</script>

<style lang="scss" scoped>
	.step {
		padding: 40upx 20upx;
		margin-bottom: 20upx;
	}

	.step_box {
		margin: 0 20upx;
		margin-left: 20upx;
		display: flex;

		.step_left {
			width: 2upx;
			display: block;
			background-color: #dddddd;
			overflow: hidden;
		}

		.step_right {
			margin-left: 20upx;
			margin-top: -10upx;

			.right_content {
				position: relative;
				margin-bottom: 30upx;

				.title {
					font-size: 28upx;
					font-family: Source Han Sans CN;
					font-weight: 500;
					color: #333333;
				}

				.times {
					font-size: 22upx;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #999999;
					line-height: 36upx;
				}

				.status {
					width: 12upx;
					height: 12upx;
					border-radius: 50%;
					border: 2upx solid #ccc;
					position: absolute;
					top: 10upx;
					background-color: #ccc;
					left: -28upx;
				}

				.result {
					padding: 10upx 15upx;
					background-color: #f6f6f6;
					font-size: 22upx;
					margin-top: 10upx;
					border: 1upx dashed #ddd;
				}

				&:last-of-type {
					margin-bottom: 0;

					.status {
						top: 14upx;
					}

					&::before {
						content: "";
						width: 6upx;
						height: 100%;
						background-color: #ffffff;
						position: absolute;
						top: 22upx;
						left: -24upx;
					}
				}
			}
		}
	}

	.info-box {
		margin-top: 30rpx;

		.title {
			font-size: 26rpx;
			font-weight: bold;
		}

		.goods-list {
			width: 100%;

			.list {
				/* display: flex;
        align-items: center; */
				width: 100%;

				.list-item {
					display: flex;
					align-items: center;
					width: 100%;
					height: 200rpx;
				}

				.thumb {
					display: flex;
					align-items: center;
					width: 30%;
					height: 100%;

					image {
						width: 160rpx;
						height: 160rpx;
					}
				}

				.item {
					width: 70%;
					height: 100%;

					.title {
						display: flex;
						align-items: center;
						width: 100%;
						height: 120rpx;

						text {
							color: #222222;
							font-size: 26rpx;
						}
					}

					.num {
						display: flex;
						width: 100%;
						justify-content: space-between;
						align-items: center;

						text {
							color: #959595;
							font-size: 26rpx;
						}

						.price {
							color: #333;
							font-size: 28rpx;
						}
					}
				}
			}
		}
	}

	.apply-data {
		width: 100%;
		margin: 20rpx auto;
		background-color: #FFFFFF;
		border-radius: 20rpx;
		overflow: hidden;
		padding-bottom: 100rpx;

		.apply-list {
			padding: 0 4%;

			.list {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 100rpx;
				border-bottom: 2rpx solid #f6f6f6;

				.title {
					display: flex;
					align-items: center;

					text {
						font-size: 26rpx;
						color: #959595;
					}
				}

				.more {
					display: flex;
					align-items: center;

					text {
						font-size: 26rpx;
						font-weight: bold;
						color: #222222;
					}

					.iconfont {
						margin-left: 20rpx;
					}

					input {
						font-size: 26rpx;
						color: #222222;
						text-align: right;
					}
				}
			}

			.describe {
				padding: 20rpx;
				height: 200rpx;
				background-color: #f6f6f6;
				margin-bottom: 20rpx;

				textarea {
					width: 100%;
					height: 100%;
					font-size: 26rpx;
					background-color: #f6f6f6;
				}
			}

			.voucher {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				width: 100%;
				height: 200rpx;

				.thumb {
					position: relative;
					width: 25%;
					height: 160rpx;

					image {
						width: 160rpx;
						height: 160rpx;
					}

					text {
						position: absolute;
						right: 0rpx;
						top: -20rpx;
						font-size: 38rpx;
						background-color: #FFFFFF;
						border-radius: 100%;
					}
				}
			}
		}
	}

	.contact {

		width: 100%;
		line-height: 55px;
		background-color: #FFFFFF;
		font-size: 26rpx;
		border: none;
		height: 100rpx;

		text {
			font-size: 28rpx;
			color: #555555;
		}

		.iconfont {
			font-size: 34rpx;
			margin-right: 20rpx;
		}
	}

	.tips {
		font-size: 24upx;
		color: #999999;
		height: 40upx;
		line-height: 40upx;
		margin-top: 40upx;
		text-align: center;
	}

	/* 提交 */
	.submit-btn {
		position: fixed;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 122rpx;
		padding-bottom: 22rpx;

		.btn {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 90%;
			height: 70rpx;
			font-size: 28rpx;
			color: #FFFFFF;
			background: linear-gradient($base, $change-clor);
			border-radius: 100rpx;
		}
	}

	.address {
		margin-top: 30upx;
		box-shadow: 0upx 0upx 10upx #ddd;
		padding: 20upx;
		border-radius: 20upx;

		.address_type {
			height: 80upx;
			line-height: 80upx;
			display: flex;
			justify-content: space-between;

			.type_text {
				font-size: 28upx;
				color: #8D8D8D;
			}

			.type_right {
				font-size: 28upx;
				color: #333;
				display: flex;
				align-items: center;

				.more {
					width: 40upx;
					height: 40upx;
					display: block;
					margin-left: 20upx;
				}
			}
		}

		.rule {
			background-color: #FCF9E5;
			padding: 20upx;
			font-size: 24upx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.rule_left {
				width: 70%;
				color: #8D8D8D;
			}

			.rule_right {
				color: #E1251B;
			}
		}

		.user_address {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-bottom: 20upx;
			margin-top: 30upx;
			border-bottom: 1upx solid #EEEEEE;

			.u_a_top {
				height: 60upx;
				line-height: 60upx;
				font-size: 28upx;
				color: #000000;
				font-weight: bold;
				display: flex;
				align-content: center;

				.iconfont {
					font-weight: 500;
					margin-right: 20upx;
					font-size: 38upx;
					color: #666666;
				}

				.name {
					margin-right: 10upx;
					font-size: 30upx;
				}
			}

			.u_a_bottom {
				font-size: 24upx;
				color: #8D8D8D;
				margin-left: 55upx;
				margin-top: 10upx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			image {
				width: 40upx;
				height: 40upx;
				display: block;
			}

			&:active {
				opacity: .8;
			}
		}

		.times {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30upx 0;

			.rili {
				width: 35upx;
				height: 35upx;
				padding: 5upx;
				box-sizing: content-box;
			}

			.times_title {
				width: 84%;
				text-align: left;
				color: #000000;
			}

			.more {
				height: 40upx;
				width: 40upx;
				display: block;
			}

		}

		.m_a_title {
			font-size: 28upx;
			color: #8D8D8D;
			height: 80upx;
			line-height: 80upx;

			text {
				margin-left: 20upx;
				font-size: 22upx;
			}
		}
	}
</style>