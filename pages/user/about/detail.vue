<!--
 * @Description: 
 * @Autor: shh
 * @Date: 2023-05-10 12:52:23
 * @LastEditors: shh
 * @LastEditTime: 2023-11-14 10:18:26
-->
<template>
  <view class="about">
    <view class="shop-info about-content" v-if="title === '本印猫介绍'">
      <view class="about-content">
        <Introduce lazy-load></Introduce>
      </view>
    </view>
    <view class="shop-info" v-if="title === '注册协议'">
      <view class="about-content">
        <Parser lazy-load></Parser>
      </view>
    </view>
    <view class="shop-info" v-if="title === '隐私协议'">
      <view class="about-content">
        <Privacy lazy-load></Privacy>
      </view>
    </view>

    <!--加载动画-->
    <rfLoading isFullScreen :active="loading"></rfLoading>
  </view>
</template>

<script>
/**
 * @des 关于商城详情
 *
 * @<NAME_EMAIL>
 * @date 2019-12-09 10:13
 * @copyright 2019
 */
import Parser from "../components/rf-parser/index";
import Introduce from "../components/rf-parser/introduce";
import Privacy from "../components/rf-parser/privacy";

export default {
  data() {
    return {
      detail: {},
      title: null,
      loading: true,
    };
  },
  components: {
    Parser,
    Introduce,
    Privacy,
  },
  onLoad(options) {
    this.initData(options);
  },
  methods: {
    // 数据初始化
    initData(options) {
      this.title = options.title;
      uni.setNavigationBarTitle({
        title: options.title,
      });
      this.loading = false;
    },
  },
};
</script>
<style lang="scss">
page {
  background-color: #fff;
  position: relative;

  .about {
    .shop-info {
      .about-content {
        padding: $spacing-lg;
      }

      text-align: center;

      image {
        margin-top: 100upx;
        width: 240upx;
        height: 240upx;
        border-radius: 50%;
      }
    }
  }
}</style>
