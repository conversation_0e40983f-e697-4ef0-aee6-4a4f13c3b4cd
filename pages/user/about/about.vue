<template>
  <view class="about" v-if="!loading">
    <view
      class="list-cell b-b"
      v-for="item in navList"
      :key="item.title"
      @tap="navTo(`/pages/user/about/detail?title=${item.title}`)"
      hover-class="cell-hover"
      :hover-stay-time="50"
    >
      <text class="cell-tit">{{ item.title }}</text>
      <text class="cell-more iconfont iconyou"></text>
    </view>

    <view class="vs">
      <view>Copyright © 2023-2026</view>
      <view>成都本印科技有限公司 版权所有</view>
      <view>版本号：{{ version }}</view>
    </view>
  </view>
</template>
<script>
/**
 * @des 关于商城
 *
 * @<NAME_EMAIL>
 * @date 2019-12-09 10:13
 * @copyright 2019
 */

export default {
  components: {},
  data() {
    return {
      version: "",
      aboutInfo: {},
      navList: [
        { title: "本印猫介绍" },
        // {
        //   title: "版权信息",
        // },
        // { title: "证照信息" },
        { title: "注册协议" },
        { title: "隐私协议" },
      ],
      appName: this.$mSettingConfig.appName,
      loading: false,
      hostUrl: this.$mConfig.hostUrl,
      merchantId: "",
    };
  },
  onLoad() {
    this.initData();
    const accountInfo = wx.getAccountInfoSync();
    this.version = accountInfo.miniProgram.version;
  },
  methods: {
    // 初始化数据
    initData() {
      uni.setNavigationBarTitle({ title: `关于${this.appName}` });
      const userInfo = uni.getStorageSync("userInfo") || {};
      this.merchantId = userInfo.merchant_id;
      if (this.merchantId === "0") {
        this.navList.shift();
        this.loading = false;
      } else {
        // this.getConfigList();
      }
    },
    // 获取商城信息
    async getConfigList() {
      await this.$http
        .get(`${merchantView}`, {
          params: {
            id: this.merchantId,
            field: "web_qrcode",
          },
        })
        .then((r) => {
          this.loading = false;
          this.aboutInfo = r;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 统一跳转接口
    navTo(route) {
      this.$mRouter.push({ route });
    },
  },
};
</script>
<style lang="scss">
page {
  position: relative;
  background-color: #f5f5f5;
}

/*关于商城*/
.about {
  padding: 20upx 0;

  .history-section {
    padding: 30upx 0 0;
    margin-top: 20upx;
    background: #fff;
    border-radius: 10upx;

    .sec-header {
      display: flex;
      align-items: center;
      font-size: $font-base;
      color: $font-color-dark;
      line-height: 40upx;
      margin-left: 30upx;

      .iconfont {
        font-size: 44upx;
        color: #5eba8f;
        margin-right: 16upx;
        line-height: 40upx;
      }
    }

    .qrcode-wrapper {
      padding: $spacing-lg 0;
      text-align: center;

      .qrcode-img-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .qrcode {
        height: 280upx;
      }

      .qrcode-img {
        width: 300upx;
        height: 300upx;

        image {
          width: 300upx;
          height: 300upx;
        }
      }

      .info {
        display: block;
      }
    }
  }
}

.vs {
  width: 100%;
  text-align: center;
  color: #8a8c93;
  position: fixed;
  bottom: 30px;
}
</style>
