<template>
  <view class="login-type">
    <view class="logo">
      <image
        class="img"
        src="@/static/images/logo.png"
        mode="heightFix"
      ></image>
      <!-- <rf-image :preview="false" src="@/static/images/logo.png"></rf-image> -->
    </view>
    <textarea
      type="input"
      v-model="log"
      :maxlength="500"
      v-show="false"
    ></textarea>

    <!--协议popup-->
    <rf-protocol-popup
      ref="mapState"
      @popupState="popupState"
      protocolPath="/pages/user/about/detail?title=注册协议"
      policyPath="/pages/user/about/detail?title=隐私协议"
      v-if="isRfProtocolPopupShow"
    ></rf-protocol-popup>
    <!-- 底部协议 -->
    <view class="footer-protocol">
      <text
        @tap="handleRfProtocolPopupShow"
        class="iconfont"
        :class="
          appAgreementDefaultSelect
            ? `text-${themeColor.name} iconradiobox`
            : 'iconradioboxblank'
        "
      ></text>
      <text class="content">如您同意</text>
      <!-- 协议地址 -->
      <text :class="'text-' + themeColor.name" @tap="handleRfProtocolPopupShow"
        >《{{ appName }} 协议》</text
      >
      <text class="content">请点击同意并继续</text>
    </view>
    <!-- 底部协议 -->
    <!--		v-if="isAuthLoginShow && !closeThirdPartyLogin"-->
    <view class="footer">
      <view v-show="step == 1">
        <!-- <view class="d-header">
          <text>手机号一键登录</text>
        </view> -->
        <view class="login-type-list">
          <button
            :disabled="btnLoading"
            :loading="btnLoading"
            class="login-type-btn"
            @tap="toAuthLogin"
          >
            手机号一键登录
            <!-- <image class="image" :src="wechat"></image> -->
          </button>
        </view>
      </view>
      <view class="wrapper" v-show="step == 2">
        <view class="d-header">
          <text>获取手机号</text>
        </view>
        <view style="height: 84rpx; position: relative">
          <button
            v-if="!params.isBind"
            class="confirm-btn"
            open-type="getPhoneNumber"
            @getphonenumber="onGetPhoneNumber"
          >
            一键登录
          </button>
          <button v-if="params.isBind" class="confirm-btn" @tap="weightLogin">
            一键登录
          </button>
          <button
            v-if="!appAgreementDefaultSelect"
            class="confirm-btn"
            style="position: absolute; top: 0"
            @tap="agree"
          >
            一键登录
          </button>
        </view>
      </view>
    </view>
    <rfLoading isFullScreen :active="loading"></rfLoading>
  </view>
</template>
<script>
/**
 * @des 登录类型
 *
 * @<NAME_EMAIL>
 * @date 2020-01-13 12:02
 * @copyright 2019
 */
import { getOpenId, wechatH5Login, mpWechatLogin } from "@/api/login";
import rfProtocolPopup from "./components/rf-protocol-popup";
import { shopInfo } from "@/api/index";
export default {
  components: { rfProtocolPopup },
  data() {
    return {
      step: 2,
      log: "",
      params: { code: null, isBind: false, isNew: false },
      btnLoading: false,
      loading: false,
      isRfProtocolPopupShow: false, // 控制协议popup显示
      appAgreementDefaultSelect: uni.getStorageSync("notFirstTimeLogin"), //this.$mSettingConfig.appAgreementDefaultSelect, // 是否允许点击登录按钮
      isAuthLoginShow: false,
      isIosAuthLoginShow: false,
      logo: this.$mSettingConfig.appLogo,
      appName: this.$mSettingConfig.appName,
      promoCodeParams: {},
      styleLoginType: this.$mSettingConfig.styleLoginType,
      wechat: this.$mAssetsPath.wechat,
      apple: this.$mAssetsPath.apple,
      closeThirdPartyLogin: this.$mSettingConfig.closeThirdPartyLogin,
    };
  },
  onShow() {
    this.btnLoading = false;
    if (uni.getStorageSync("accessToken")) {
      this.$mRouter.reLaunch({ route: "/pages/home/<USER>" });
    }
    this.toAuthLogin();
  },
  onLoad(options) {
    //	this.$mStore.commit('logout');

    // 用户多次点击授权登录会生成多个code 去最后一个code
    this.code =
      options.code &&
      options.code.split(",")[options.code.split(",").length - 1];
    // 如果不是第一次进来 就不需要强制阅读协议

    // if (!uni.getStorageSync("notFirstTimeLogin")) {
    //   if (!this.appAgreementDefaultSelect) {
    //     this.appAgreementDefaultSelect = false;
    //   }
    // } else {
    //   this.appAgreementDefaultSelect = true;
    // }
    const backUrl = uni.getStorageSync("backToPage");
    if (backUrl.indexOf("promo_code") !== -1) {
      this.promoCodeParams.promo_code =
        JSON.parse(backUrl)["query"]["promo_code"];
    }
    const _this = this;
    // if (this.code) {
    //   this.btnLoading = true;
    //   this.$http
    //     .get(wechatH5Login, {
    //       code: this.code,
    //       ..._this.promoCodeParams,
    //     })
    //     .then(async (r) => {
    //       this.btnLoading = false;
    //       if (!r.login) {
    //         this.user_info = r.user_info.original;
    //         uni.showModal({
    //           content: "您尚未绑定账号，是否跳转登录页面？",
    //           success: (confirmRes) => {
    //             if (confirmRes.confirm) {
    //               uni.setStorageSync(
    //                 "wechatUserInfo",
    //                 JSON.stringify(_this.user_info)
    //               );
    //               _this.$mRouter.push({ route: "/pages/public/login" });
    //             } else {
    //               this.btnLoading = false;
    //             }
    //           },
    //         });
    //       } else {
    //         await this.$mStore.commit("login", r.user_info);
    //         const backToPage = uni.getStorageSync("backToPage");
    //         if (backToPage) {
    //           if (
    //             backToPage.indexOf("/pages/index/profile") !== -1 ||
    //             backToPage.indexOf("/pages/cart/cart") !== -1 ||
    //             backToPage.indexOf("/pages/home/<USER>") !== -1 ||
    //             backToPage.indexOf("/pages/notify/notify") !== -1 ||
    //             backToPage.indexOf("/pages/category/category") !== -1
    //           ) {
    //             this.$mRouter.reLaunch(JSON.parse(backToPage));
    //           } else {
    //             this.$mRouter.redirectTo(JSON.parse(backToPage));
    //           }
    //           uni.removeStorageSync("backToPage");
    //           uni.removeStorageSync("wechatUserInfo");
    //         } else {
    //           this.$mRouter.reLaunch({ route: "/pages/index/profile" });
    //         }
    //       }
    //     })
    //     .catch(() => {
    //       this.btnLoading = false;
    //     });
    // }
    console.log("进入loginType页面的onload方法...");
    this.params = { code: null, isBind: false, isNew: false };
  },
  methods: {
    // 通用跳转
    navTo(route) {
      if (!this.appAgreementDefaultSelect) {
        this.$mHelper.toast("请勾选并阅读协议，才能进行下一步哦", 1.5 * 1000);
        return;
      }
      this.$mRouter.redirectTo({ route });
    },
    // 显示协议popup
    handleRfProtocolPopupShow() {
      this.isRfProtocolPopupShow = true;
    },
    // 监听是否同意协议
    popupState(e) {
      if (e) {
        this.appAgreementDefaultSelect = true;
        uni.setStorageSync("notFirstTimeLogin", true);
        this.isRfProtocolPopupShow = false;
      } else {
        this.appAgreementDefaultSelect = false;
        this.isRfProtocolPopupShow = false;
      }
    },

    // 授权登录
    toAuthLogin() {
      const _this = this;
      //uni获取登录信息
      // uni.getUserProfile({
      //   desc: "完善用户信息",
      //   success: function (infoRes) {
      uni.login({
        provider: "weixin",
        success: function (loginRes) {
          console.log(loginRes);
          _this.params.code = loginRes.code;
          _this.mpWxLogin(_this.params);
        },
        fail: function (e) {
          console.log("获取登录信息错误：" + JSON.stringify(e));
          _this.btnLoading = false;
          _this.$mHelper.log("暂不支持小程序登录");
        },
        //     });
        //   },
        //   fail: function (e) {
        //     console.log("获取用户信息失败:" + JSON.stringify(e));
        //     _this.btnLoading = false;
        //   },
      });
    },
    async mpWxLogin(params) {
      //获取openid
      await this.$http
        .get(getOpenId + "?code=" + params.code)
        .then(async (res) => {
          if (res) {
            this.params.isBind = res.data.isBind;
            this.params.isNew = res.data.isNew;
            if (res.data.isBind) {
              this.params.token = res.data.token;
            } else {
              this.params.token = null;
            }
            console.log(this.params);
          } else {
            this.$mHelper.toast("获取个人登录信息失败！");
          }
        });
    },
    agree() {
      this.btnLoading = true;
      if (!this.appAgreementDefaultSelect) {
        this.$mHelper.toast("请阅读并同意协议", 1.5 * 1000);
        this.btnLoading = false;
        return;
      }
    },
    weightLogin() {
      this.$mStore.commit("login", this.params);
      this.$mRouter.reLaunch({ route: "/pages/profile/profile" });
    },
    async onGetPhoneNumber(e) {
      const _this = this;
      //"绑定手机号
      if (e.detail.errMsg === "getPhoneNumber:fail user deny") {
        //用户决绝授权
        this.$mHelper.toast("登录后，会有全部功能的操作权限！");
      } else {
        console.log(e.detail);
        let params = {
          phoneCode: e.detail.code,
        };
        //允许授权
        if (!this.params.isBind) {
          uni.login({
            provider: "weixin",
            success: function (loginRes) {
              console.log(loginRes.code);
              _this.params.code = loginRes.code;
            },
            fail: function (e) {
              _this.$mHelper.log("暂不支持小程序登录");
            },
          });
        }
        setTimeout(() => {
          this.loading = true;
          let obj = { phoneCode: params.phoneCode, authCode: this.params.code };
          this.$http.post(mpWechatLogin, obj).then(async (res) => {
            this.btnLoading = false;
            if (res) {
              this.loading = false;
              this.$mStore.commit("login", res.data);
              if (!res.data.isNew) {
                const pages = getCurrentPages();
                // this.$mRouter.reLaunch({ route: "/pages/profile/profile" });
                uni.navigateBack({
                  detail: pages.length - 2,
                });
              } else {
                uni.setStorageSync("customerId", res.data.customerId);
                this.$mRouter.reLaunch({ route: "/pages/user/sign" });
              }
            } else {
              this.btnLoading = false;
              this.loading = false;
              this.$mHelper.toast("登录失败！");
            }
          });
        }, 500);
      }
    },
  },
};
</script>
<style lang="scss">
page {
  background: #fff;
  height: calc(100% - 88upx);
}

.login-type {
  padding-top: 80upx;

  .logo {
    text-align: center;
    margin-bottom: 80upx;

    image {
      width: 180upx;
      height: 180upx;
      border-radius: 28upx;
    }
  }

  .confirm-btn {
    width: 84%;
    margin: 0 7% 50upx;
    height: 84upx;
    line-height: 84upx;
    font-size: $font-lg;
    border: 1upx solid;
  }

  .confirm-btn-bg {
    background: none;
  }

  .footer {
    width: 100%;
    text-align: center;
    position: fixed;
    font-size: $font-sm + 2upx;

    .protocol {
      color: $base-color;
      margin: 0 10upx;
    }

    .d-header {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 50px;
      height: 80upx;
      margin-bottom: $spacing-base;
      font-size: $font-base + 2upx;
      color: $font-color-base;
      position: relative;

      text {
        padding: 0 $spacing-lg;
        background: #fff;
        position: relative;
        z-index: 1;
      }

      &:after {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translateX(-50%);
        width: 300upx;
        height: 0;
        content: "";
        border-bottom: 1px solid #ccc;
      }
    }

    .login-type-list {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .login-type-btn {
        line-height: 80rpx;
        border: none;
        text-align: center;
        background: none;
        padding: 0 20rpx;
        margin: 0;
        color: #fff;
        background: #f60;

        .image {
          width: 100upx;
          height: 100upx;
        }

        &:after {
          border: none;
        }
      }

      .iconfont {
        font-size: 50upx;
        color: $font-color-base;
      }
    }
  }
}

.confirm-btn {
  width: 630upx;
  height: 76upx;
  line-height: 76upx;
  border-radius: 50px;
  margin-top: 70upx;
  background: #f37b1d;
  color: #fff;
  font-size: $font-lg;

  &:after {
    border-radius: 100px;
  }
}

.footer-protocol {
  .iconfont {
    font-size: 20px;
  }
}
</style>
