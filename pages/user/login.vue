<template>
	<view class="login">
		<view class="container">
			<!--顶部返回按钮-->
			<text class="back-btn iconfont iconzuo" @tap="navBack"></text>
			<view class="logo">
				<image class="img" src="@/static/images/logo.png" mode="heightFix"></image>
				<!-- <rf-image :preview="false" src="@/static/images/logo.png"></rf-image> -->
			</view>
			<!-- 设置白色背景防止软键盘把下部绝对定位元素顶上来盖住输入框等 -->
			<view class="wrapper">
				<view class="left-top-sign">LOGIN</view>
				<view class="welcome">欢迎回来！</view>
				<view class="input-content">
					<view class="input-item">
						<text class="tit">账号</text>
						<input type="text" name="code" v-model="loginParams.code" placeholder="请输入账号" maxlength="20" @blur="blurMobileChange" />
					</view>
					<view class="input-item">
						<text class="tit">密码</text>
						<input name="password" type="password" v-model="loginParams.password" placeholder="请输入密码" maxlength="20" />
					</view>
					<view class="input-item">
						<text class="tit">验证码</text>
						<view style="width: 100%">
							<input name="captcha" type="text" v-model="loginParams.captcha" placeholder="请输入验证码" maxlength="20" style="width: 60%; float: left" />
							<image class="codema" mode="heightFix" :src="captchaImg" @click="getcaptcha"></image>
						</view>
					</view>
					<view class="remember" @tap="rememberUsername">
						<text class="iconfont" :class="agreenRememberUsername ? `text-${themeColor.name} iconradiobox` : 'iconradioboxblank'"></text>
						<text class="content">记住我</text>
					</view>
					<view class="footer-protocol">
						<text @tap="handleRfProtocolPopupShow" class="iconfont" :class="appAgreementDefaultSelect ? `text-${themeColor.name} iconradiobox` : 'iconradioboxblank'"></text>
						<text class="content">如您同意</text>
						<!-- 协议地址 -->
						<text :class="'text-' + themeColor.name" @tap="handleRfProtocolPopupShow">《{{ appName }} 协议》</text>
						<text class="content">请点击同意并继续</text>
					</view>
					<!--协议popup-->
					<rf-protocol-popup
						ref="mapState"
						@popupState="popupState"
						protocolPath="/pages/user/about/detail?title=注册协议"
						policyPath="/pages/user/about/detail?title=隐私协议"
						v-if="isRfProtocolPopupShow"
					></rf-protocol-popup>
					<button class="confirm-btn" :class="'bg-' + themeColor.name" :disabled="btnLoading" :loading="btnLoading" @tap="toLogin">登录</button>
				</view>

				<!-- <view class="forget-section" @tap="navTo('/pages/public/password')">
          忘记密码?
        </view> -->
			</view>
			<!-- <view class="register-section">
        还没有账号?
        <text @tap="navTo('/pages/public/register')">马上注册</text>
        或者
        <text @tap="toHome">返回主页</text>
      </view> -->
		</view>
		<!--协议popup-->
		<rf-protocol-popup
			ref="mapState"
			@popupState="popupState"
			protocolPath="/pages/user/about/detail?title=注册协议"
			policyPath="/pages/user/about/detail?title=隐私协议"
			v-if="isRfProtocolPopupShow"
		></rf-protocol-popup>
	</view>
</template>
<script>
import { getOpenId, mpWechatLogin, logout, captcha, key, resources } from '@/api/login';
import { encodeFun, treeToArray } from '@/utils/index';
import rfProtocolPopup from './components/rf-protocol-popup';
export default {
	components: {
		rfProtocolPopup
	},
	data() {
		return {
			appAgreementDefaultSelect: false, // 是否允许点击登录按钮
			agreenRememberUsername: true,
			isRfProtocolPopupShow: false, // 控制协议popup显示
			captchaImg: '',
			loginParams: {
				code: '',
				captcha: '',
				password: '',
				passwordToken: '',
				key: '',
				captchaToken: '',
				authCode: ''
			},
			registerParams: {
				mobile: '',
				password: '',
				password_repetition: '',
				recommendCode: '',
				nickname: '',
				code: ''
			},
			btnLoading: false,
			reqBody: {},
			codeSeconds: 0, // 验证码发送时间间隔
			loginByPass: true,
			smsCodeBtnDisabled: true,
			userInfo: null,
			loginBg: this.$mAssetsPath.loginBg,
			loginPic: this.$mAssetsPath.loginPic,
			appName: this.$mSettingConfig.appName,
			styleLoginType: this.$mSettingConfig.styleLoginType,
			closeRegisterPromoCode: this.$mSettingConfig.closeRegisterPromoCode,
			tabCurrentIndex: 0,
			typeList: [
				{
					text: '登录'
				},
				{
					text: '注册'
				}
			]
		};
	},
	onShow() {
		// if (this.$mStore.getters.hasLogin) {
		//   this.$mRouter.reLaunch({ route: "/pages/home/<USER>" });
		// }
	},
	onLoad(options) {
		this.getcaptcha();
		let username = uni.getStorageSync('username');
		this.loginParams.code = username;
	},
	methods: {
		rememberUsername() {
			// console.log('记住我');
			this.agreenRememberUsername = !this.agreenRememberUsername;
		},
		// 监听是否同意协议
		popupState(e) {
			if (e) {
				this.appAgreementDefaultSelect = true;
				this.isRfProtocolPopupShow = false;
			} else {
				this.appAgreementDefaultSelect = false;
				this.isRfProtocolPopupShow = false;
			}
		},
		// 显示协议popup
		handleRfProtocolPopupShow() {
			this.isRfProtocolPopupShow = true;
		},
		// 失去焦点的手机号
		blurMobileChange(e) {
			this.mobile = e.detail.value;
		},
		getcaptcha() {
			this.$http.post(captcha).then((res) => {
				this.loginParams.captchaToken = res.data.first;
				this.captchaImg = res.data.second;
			});
		},
		// 返回上一页
		navBack() {
			this.$mRouter.back();
		},
		// 统一跳转路由
		navTo(route) {
			this.$mRouter.push({
				route
			});
		},
		// 返回主页
		toHome() {
			this.$mRouter.reLaunch({
				route: '/pages/home/<USER>'
			});
		},

		// 提交表单
		async toLogin() {
			if (!this.appAgreementDefaultSelect) {
				this.$mHelper.toast('请阅读并同意协议', 1.5 * 1000);
				return;
			}
			// uni.removeStorageSync("loginMobile");
			// uni.removeStorageSync("loginPassword");
			// this.reqBody["mobile"] = this.loginParams["mobile"];
			// let cheRes, loginApi;
			// if (this.loginByPass) {
			//   loginApi = loginByPass;
			//   this.reqBody["password"] = this.loginParams["password"];
			//   cheRes = this.$mGraceChecker.check(
			//     this.reqBody,
			//     this.$mFormRule.loginByPassRule
			//   );
			// } else {
			//   this.reqBody["code"] = this.loginParams["code"];
			//   loginApi = loginBySmsCode;
			//   cheRes = this.$mGraceChecker.check(
			//     this.reqBody,
			//     this.$mFormRule.loginByCodeRule
			//   );
			// }
			// if (!cheRes) {
			//   this.$mHelper.toast(this.$mGraceChecker.error);
			//   return;
			// }
			// this.reqBody.group = this.$mHelper.platformGroupFilter();
			// const backUrl = uni.getStorageSync("backToPage");
			// if (backUrl.indexOf("recommendCode") !== -1) {
			//   this.reqBody.recommendCode =
			//     JSON.parse(backUrl)["query"]["recommendCode"];
			// }
			// this.handleLogin(this.reqBody, loginApi);
			//获取openid
			let that = this;
			if (that.agreenRememberUsername) {
				uni.setStorageSync('username', that.loginParams.code);
			} else {
				uni.removeStorageSync('username');
			}
			uni.login({
				provider: 'weixin',
				success: function (loginRes) {
					console.log(loginRes.code);
					that.loginParams.authCode = loginRes.code;
					// that.$http
					//   .get(getOpenId + "?code=" + loginRes.code)
					//   .then(async () => {
					that.$http
						.post(key)
						.then((res) => {
							console.log(res);
							that.loginParams.passwordToken = res.data.first;
							that.loginParams.key = res.data.second;
							// that.loginParams.password = encodeFun(
							//   that.loginParams.password,
							//   that.loginParams.key
							// );
							let password = encodeFun(that.loginParams.password, that.loginParams.key);
							that.$http
								.post(mpWechatLogin, {
									...that.loginParams,
									password: password
								})
								.then(async (res1) => {
									that.btnLoading = false;
									if (res1) {
										that.loading = false;
										that.$mStore.commit('login', res1.data);
										that.$mRouter.reLaunch({
											route: '/pages/home/<USER>'
										});
										that.$http.get(resources).then(async (res2) => {
											uni.setStorageSync('powerMenu', JSON.stringify(treeToArray(res2.data)));
										});
									} else {
										that.btnLoading = false;
										that.loading = false;
										that.$mHelper.toast('登录失败！');
										this.getcaptcha();
									}
								});
						})
						.catch();
					// });
				},
				fail: function (e) {
					console.log('获取登录信息错误：' + JSON.stringify(e));
					_this.btnLoading = false;
					_this.$mHelper.log('暂不支持小程序登录');
				}
			});
		},
		// 登录
		async handleLogin(params, loginApi) {
			this.btnLoading = true;
			await this.$http
				.post(loginApi, params)
				.then((r) => {
					this.$mHelper.toast('恭喜您，登录成功！');
					this.$mStore.commit('login', r);
					if (this.userInfo) {
						this.btnLoading = false;
						const oauthClientParams = {};
						/*  #ifdef MP-WEIXIN */
						oauthClientParams.oauth_client = 'wechatMp';
						/*  #endif  */
						/*  #ifndef MP-WEIXIN */
						oauthClientParams.oauth_client = 'wechat';
						/*  #endif  */
						const userInfo = JSON.parse(this.userInfo);
						this.$http.post(authLogin, {
							...userInfo,
							...oauthClientParams,
							gender: userInfo.sex || userInfo.gender,
							oauth_client_user_id: userInfo.openid || userInfo.openId,
							head_portrait: userInfo.headimgurl || userInfo.avatarUrl
						});
					}
					uni.removeStorageSync('wechatUserInfo');
					const backToPage = uni.getStorageSync('backToPage');
					uni.removeStorageSync('backToPage');
					if (backToPage) {
						if (
							backToPage.indexOf('/pages/index/profile') !== -1 ||
							backToPage.indexOf('/pages/cart/cart') !== -1 ||
							backToPage.indexOf('/pages/home/<USER>') !== -1 ||
							backToPage.indexOf('/pages/notify/notify') !== -1 ||
							backToPage.indexOf('/pages/category/category') !== -1
						) {
							this.$mRouter.reLaunch(JSON.parse(backToPage));
						} else {
							this.$mRouter.redirectTo(JSON.parse(backToPage));
						}
					} else {
						this.$mRouter.reLaunch({
							route: '/pages/index/profile'
						});
					}
				})
				.catch(() => {
					this.btnLoading = false;
				});
		},
		// 切换登录/注册
		tabClick(index) {
			this.tabCurrentIndex = index;
		},
		// 注册账号
		async toRegister() {
			if (this.$mSettingConfig.closeRegister) {
				this.$mHelper.toast('暂未开放注册，敬请期待～');
				return;
			}
			this.reqBody['mobile'] = this.registerParams['mobile'];
			this.reqBody['password'] = this.registerParams['password'];
			this.reqBody['code'] = this.registerParams['code'];
			this.reqBody['nickname'] = this.registerParams['nickname'];
			const cheRes = this.$mGraceChecker.check(this.reqBody, this.$mFormRule.registerRule);
			if (!cheRes) {
				this.$mHelper.toast(this.$mGraceChecker.error);
				return;
			}
			if (this.registerParams['password'] !== this.registerParams['password_repetition']) {
				this.$mHelper.toast('两次输入的密码不一致');
				return;
			}
			this.reqBody['password_repetition'] = this.registerParams['password_repetition'];
			this.reqBody['recommendCode'] = this.registerParams['recommendCode'];
			this.btnLoading = true;
			this.reqBody.group = this.$mHelper.platformGroupFilter();
			await this.$http
				.post(registerByPass, this.reqBody)
				.then((res) => {
					this.btnLoading = false;
					this.$mHelper.toast('恭喜您注册成功');
					uni.setStorageSync('loginMobile', this.reqBody['mobile']);
					uni.setStorageSync('loginPassword', this.reqBody['password']);
					this.$mRouter.push({
						route: '/pages/public/login'
					});
				})
				.catch(() => {
					this.btnLoading = false;
				});
		}
	}
};
</script>
<style lang="scss">
page {
	background: $color-white;
}

.remember {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;

	.content {
		margin: 0 4rpx 0 10rpx;
	}
}

.container {
	position: relative;
	width: 100vw;
	overflow: hidden;
	background: #fff;
	padding: 100rpx 0;

	.wrapper {
		position: relative;
		z-index: 90;
		background: #fff;
		padding-bottom: 40upx;
	}

	.back-btn {
		position: absolute;
		left: 40upx;
		z-index: 9999;
		padding-top: var(--status-bar-height);
		top: 40upx;
		font-size: 40upx;
		color: $font-color-dark;
	}

	.left-top-sign {
		font-size: 120upx;
		color: $page-color-base;
		position: relative;
		left: -16upx;
	}

	.right-top-sign {
		position: absolute;
		top: 80upx;
		right: -30upx;
		z-index: 95;

		&:before,
		&:after {
			display: block;
			content: '';
			width: 400upx;
			height: 80upx;
			background: #b4f3e2;
		}

		&:before {
			transform: rotate(50deg);
			border-radius: 0 50px 0 0;
		}

		&:after {
			position: absolute;
			right: -198upx;
			top: 0;
			transform: rotate(-50deg);
			border-radius: 50px 0 0 0;
			/* background: pink; */
		}
	}

	.left-bottom-sign {
		position: absolute;
		left: -270upx;
		bottom: -320upx;
		border: 100upx solid #d0d1fd;
		border-radius: 50%;
		padding: 180upx;
	}

	.welcome {
		position: relative;
		left: 50upx;
		top: -90upx;
		font-size: 46upx;
		color: #555;
		text-shadow: 1px 0px 1px rgba(0, 0, 0, 0.3);
	}

	.input-content {
		padding: 0 60upx;
	}

	.input-item {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: center;
		padding: 0 30upx;
		background: $page-color-light;
		height: 120upx;
		border-radius: 4px;
		margin-bottom: 50upx;

		&:last-child {
			margin-bottom: 0;
		}

		.tit {
			height: 50upx;
			line-height: 56upx;
			font-size: $font-sm + 2upx;
			color: $font-color-base;
		}

		input {
			height: 60upx;
			font-size: $font-base + 2upx;
			color: $font-color-dark;
			width: 100%;
		}
	}

	.input-item-sms-code {
		position: relative;
		width: 100%;

		.sms-code-btn {
			position: absolute;
			color: #111;
			right: 20upx;
			bottom: 20upx;
			font-size: 28upx;
		}

		.sms-code-resend {
			color: #999;
		}

		.sms-code-btn:after {
			border: none;
			background-color: transparent;
		}
	}

	.forget-section {
		font-size: $font-sm + 2upx;
		color: $font-color-spec;
		text-align: center;
		margin-top: 40upx;
	}

	.register-section {
		margin: 30upx 0 50upx 0;
		width: 100%;
		font-size: $font-sm + 2upx;
		color: $font-color-base;
		text-align: center;

		text {
			color: $font-color-spec;
			margin-left: 10upx;
		}

		text:first-child {
			margin-right: 10upx;
		}
	}

	.btn-group {
		display: flex;
		margin-bottom: 20upx;
	}
}

.login-type-2 {
	width: 100%;
	position: relative;

	.back-btn {
		position: absolute;
		left: 40upx;
		z-index: 9999;
		padding-top: var(--status-bar-height);
		top: 40upx;
		font-size: 48upx;
		color: $color-white;
	}

	.login-top {
		height: 460upx;
		position: relative;

		.desc {
			position: absolute;
			top: 200upx;
			left: 40upx;
			font-size: 48upx;

			.title {
				font-size: 48upx;
			}
		}

		.login-pic {
			position: absolute;
			width: 220upx;
			height: 270upx;
			right: 30upx;
			top: 100upx;
		}
	}

	.login-type-content {
		position: relative;
		top: -72upx;

		.login-bg {
			width: 94vw;
			height: 94vw;
			margin: 0 3vw;
		}

		.main {
			width: 94vw;
			position: absolute;
			top: 0;
			left: 3vw;

			.nav-bar {
				display: flex;
				height: 100upx;
				justify-content: center;
				align-items: center;
				position: relative;
				z-index: 10;

				.nav-bar-item {
					flex: 1;
					display: flex;
					height: 100%;
					line-height: 96upx;
					font-size: $font-lg;
					display: flex;
					margin: 0 120upx;
					justify-content: center;
				}

				.nav-bar-item-active {
					border-bottom: 5upx solid;
				}
			}

			.login-type-form {
				width: 80%;
				margin: 50upx auto;

				.input-item {
					position: relative;
					height: 90upx;
					line-height: 90upx;
					margin-bottom: $spacing-lg;

					.iconfont {
						font-size: 50upx;
						position: absolute;
						left: 0;
					}

					.login-type-input {
						height: 90upx;
						padding-left: 80upx;
						border-bottom: 1upx solid rgba(0, 0, 0, 0.1);
					}

					.sms-code-btn,
					sms-code-resend {
						width: 240upx;
						font-size: $font-base - 2upx;
					}
				}
			}

			.login-type-tips {
				padding: 0 50upx;
				display: flex;
				justify-content: space-between;
			}

			.confirm-btn {
				height: 80upx;
				line-height: 80upx;
			}
		}
	}

	.login-type-bottom {
		width: 100%;
		padding-bottom: 30upx;
		text-align: center;
		font-size: $font-lg;
	}
}

.codema {
	float: right;
	height: 88rpx;
	cursor: pointer;
	overflow: hidden;
	margin-top: -88rpx;
}

.logo {
	text-align: center;

	image {
		width: 180upx;
		height: 180upx;
		border-radius: 28upx;
	}
}
</style>
