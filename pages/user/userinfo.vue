<template>
  <view class="user" v-if="details">
    <view class="back" @click="onBack">
      <view class="back-one">
        <text></text>
      </view>
    </view>
    <!--头部-->
    <view class="user-section">
      <img class="bg" :src="userBg" />
      <view class="user-info-box">
        <view class="portrait-box">
          <image class="portrait" src="@/static/images/top.png" />
          <view class="username">
            <view class="name"> {{ details.userManageVo.name }} </view>
            <view
              class="phone"
              @tap.stop="phoneConfirm(details.userManageVo.mobileNumber)"
            >
              联系电话：{{ details.userManageVo.mobileNumber }}
              <text class="iconfont icondianhuatianchong"></text>
            </view>
            <view class="address">
              地址：{{
                details.locationRegion.fullRegion
                  ? details.locationRegion.fullRegion
                  : "暂无"
              }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区-->

    <view class="cover-container">
      <view class="top-box">
        <view class="tui-card">
          <view class="tui-card-header">{{ details.orderNumber }}</view>
          <view class="tui-card-cell">接单次数</view>
        </view>
        <view class="tui-card">
          <view class="tui-card-header">{{
            details.engineerInfo.yearOfService
          }}</view>
          <view class="tui-card-cell">从业年限</view>
        </view>
      </view>
      <view class="top-box">
        <view class="tui-card">
          <view class="tui-card-header-left">工程师介绍</view>
          <view class="tui-list-cell-left">{{
            details.engineerInfo.introduce
          }}</view>
        </view>
      </view>
      <view class="top-box">
        <view class="tui-card">
          <view class="tui-card-header-left">维修能力</view>
          <view class="list-header">
            <view class="cell">品牌</view>
            <view class="cell">系列</view>
            <view class="cell">维修能力</view>
          </view>
          <view
            class="list-list"
            v-for="(item, index) in details.engineerSkills"
          >
            <view class="cell">{{ item.brand }}</view>
            <view class="cell">{{ item.serial }}</view>
            <view class="cell">{{ item.skillExp.label }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
/**
 * @des 个人中心
 *
 * @<NAME_EMAIL>
 * @date 2020-01-10 11:41
 * @copyright 2019
 */
import {
  PATH,
  followList,
  shopInfo,
  topHeader,
  topHeader0,
  topHeader1,
  noticeDetil,
  pubalList,
} from "@/api/index";

import { orderCount } from "@/api/order";

import { mapMutations } from "vuex";
import $mAssetsPath from "@/config/assets.config";

export default {
  components: {},
  data() {
    return {
      userBg: this.$mAssetsPath.userBg,
      id: "",
      details: {},
    };
  },
  filters: {
    filterMemberLevel(val) {
      if (!val) return "普通用户";
      return val.name;
    },
    marketingTypeTag(marketingType) {
      let tag;
      switch (marketingType) {
        case "discount":
          tag = $mAssetsPath.discountTag;
          break;
        case "bargain":
          tag = $mAssetsPath.bargainTag;
          break;
        case "group_buy":
          tag = $mAssetsPath.groupTag;
          break;
        case "wholesale":
          tag = $mAssetsPath.wholesaleTag;
          break;
      }
      return tag;
    },
  },
  computed: {},
  onLoad() {
    this.id = uni.getStorageSync("userInfo").id;
  },
  async onShow() {
    await this.initData();
    await this.getInfo();
  },
  created() {},
  // #ifndef MP
  onNavigationBarButtonTap(e) {
    const index = e.index;
    if (index === 0) {
      this.navTo("/pages/system/set");
    } else if (index === 1) {
      // #ifdef APP-PLUS
      // eslint-disable-next-line
      const pages = getCurrentPages();
      const page = pages[pages.length - 1];
      const currentWebview = page.$getAppWebview();
      currentWebview.hideTitleNViewButtonRedDot({
        index,
      });
      // #endif
      this.$mRouter.switchTab({
        route: "/pages/notify/notify",
      });
    }
  },
  //下拉
  onPullDownRefresh() {
    this.initData();
    uni.stopPullDownRefresh();
  },
  // #endif
  methods: {
    ...mapMutations(["login"]),
    // 数据初始化
    async initData() {},

    // 统一跳转接口,拦截未登录路由
    navTo(route) {
      this.$mRouter.push({
        route,
      });
    },
    onBack() {
      console.log("444444444");
      uni.navigateBack({
        delta: 1,
      });
    },
    phoneConfirm(phone) {
      uni.makePhoneCall({
        phoneNumber: phone,
        success: () => {
          console.log("成功拨打电话");
        },
      });
    },
    getInfo() {
      this.$http.get(`/api/engineer-info/detail/${this.id}`).then((res) => {
        this.details = res.data;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.hticon {
  font-size: 24px;
}

page {
  background: #f5f6f8;
}

.user {
  .user-section {
    height: 520upx;
    padding: 150upx 30upx 0;
    position: relative;

    .bg {
      position: absolute;
      left: 0;
      top: 0;
      width: 100vw;
      height: 60vw;
      opacity: 0.84;
    }

    .user-info-box {
      height: 180upx;
      position: relative;
      z-index: 1;

      .portrait-box {
        width: 100%;
        height: 130upx;
        display: flex;
        align-items: center;

        .portrait {
          width: 113rpx;
          height: 113rpx;
          border: 5upx solid #fff;
          border-radius: 50%;
        }

        .username {
          flex: 1;
          padding-left: 20upx;
          box-sizing: border-box;
          font-size: $font-lg + 6upx;
          color: $color-white;
          color: #323333;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .name {
            font-size: 32rpx;
            font-weight: bold;
          }

          .phone {
            font-size: 27rpx;
            margin-top: 14rpx;

            .iconfont {
              color: #ff541e;
              font-size: 27rpx;
              margin-left: 20rpx;
              font-weight: bold;
            }
          }
        }

        button {
          background-color: transparent;
          font-size: $font-lg + 6upx;
          color: $font-color-dark;
          border: none;
        }

        button::after {
          border: none;
        }
      }

      .change {
        width: 100%;
        text-align: right;
        margin-top: 20upx;

        .btn {
          width: 180upx;
          height: 60upx;
          background: linear-gradient(-45deg, #f89030 0%, #fd4e19 100%);
          border-radius: 27upx;
          padding: 14upx 28upx;
          font-size: 27upx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #ffffff;
          text-align: center;
        }
      }
    }

    .vip-card-box {
      display: flex;
      flex-direction: column;
      color: #f7d680;
      height: 120upx;
      // background: url('https://yzimg.jikebang.com/gczh/imgs/vip-card.png');
      background-size: 100% 100%;
      border-radius: 16upx 16upx 0 0;
      overflow: hidden;
      position: relative;
      padding: 20upx 24upx;
      margin-top: -30upx;

      .b-btn {
        position: absolute;
        right: 24upx;
        top: 24upx;
        width: 132upx;
        height: 40upx;
        text-align: center;
        line-height: 40upx;
        font-size: 22upx;
        color: #36343c;
        border-radius: 20px;
        background: linear-gradient(to left, #f9e6af, #ffd465);
        z-index: 1;
      }

      .tit {
        font-size: $font-base + 2upx;
        color: #f7d680;
        margin-bottom: 28upx;

        .iconfont {
          color: #f6e5a3;
          display: inline-block;
          margin-right: 16upx;
        }
      }
    }
  }

  .cover-container {
    margin-top: -200upx;
    padding: 0 24rpx 20upx;
    position: relative;

    .top-box {
      width: 100%;
      display: flex;
      border-radius: 20upx;
      overflow: hidden;
      margin-bottom: 26upx;
      background: #fff;
      padding: 24rpx;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .tui-card {
      flex: 1;

      .tui-card-header,
      .tui-card-header-left {
        width: 100%;
        font-size: 60rpx;
        line-height: 80rpx;
        text-align: center;
      }

      .tui-card-header {
        font-size: 40rpx;
        font-weight: bold;
        line-height: 60rpx;
      }

      .tui-card-header-left {
        text-align: left;
        font-size: 28rpx;
        line-height: 80rpx;
        font-weight: bold;
      }

      .tui-card-cell,
      .tui-card-cell-left {
        width: 100%;
        font-size: 30rpx;
        line-height: 50rpx;
        text-align: center;
      }

      .tui-card-cell-left {
        text-align: left;
        line-height: 1;
      }

      .tui-card-cell {
        font-size: 27rpx;
      }
    }

    .list-header {
      width: 100%;
      margin: auto;
      display: flex;
      background: #fff6f3;

      .cell {
        line-height: 80rpx;
        flex: 1;
        text-align: center;
        color: #333;
        font-size: 27rpx;
      }
    }

    .list-list {
      width: 100%;
      margin: auto;
      display: flex;
      line-height: 80rpx;
      background: #fff;

      .cell {
        line-height: 80rpx;
        flex: 1;
        text-align: center;
        color: #666;
        font-size: 27rpx;
      }
    }

    .list-list:nth-child(even) {
      background: #fafafa;
    }

    .tui-list-cell-left {
      margin-bottom: 20rpx;
    }
  }
}

.img {
  width: 100%;
  display: flex;
  justify-content: center;
  text-align: center;

  .iconfont {
    font-size: $font-lg + 24upx;
  }
}

.share-btn {
  height: 142upx;
  text-align: left;
  background: none;
  padding: 5px 0 0 0;
  margin: 0;

  .text {
    color: #303133;
    font-size: 24upx;
  }
}

.share-btn:after {
  border: none;
  border-radius: none;
}

.newpub {
  background: #ff7200;
  width: 38rpx;
  height: 38rpx;
  border-radius: 50%;
  text-align: center;
  line-height: 38rpx;
  font-size: 24rpx;
  color: #fff;
  position: absolute;
  right: 30%;
}

.back {
  position: absolute;
  left: 0;
  top: 70rpx;
  width: 100rpx;
  height: 100rpx;
  z-index: 1000000;

  // 返回
  .back-one {
    display: block;
    align-items: center;
    justify-content: center;
    width: 50rpx;
    height: 50rpx;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 100%;
    position: relative;
    top: 20rpx;
    left: 20rpx;

    text {
      display: flex;
      width: 20rpx;
      height: 20rpx;
      border-left: 2rpx solid #ffffff;
      border-bottom: 2rpx solid #ffffff;
      transform: rotate(45deg);
      position: absolute;
      left: 20rpx;
      top: 15rpx;
    }
  }

  .action {
    background-color: transparent;

    text {
      border-color: #555555;
    }
  }
}
</style>
