<template>
	<view class="register">
		<view class="wrapper">
			<view class="welcome">
				<view class="welcome-title"> 店铺信息完善 </view>
			</view>
			<view class="main_info">
				<!-- <view class="input-content">
					<view class="input-item">
						<text class="title">角色</text>
						<view class="active-roles" style="flex: 1">
							<uni-data-picker style="flex: 1" v-model="registerParams.role" :localdata="rolesItem"
								placeholder="请选择您的角色" popup-title="请选择您的角色"
								:map="{ text: 'label', value: 'value' }"></uni-data-picker>
						</view>
					</view>
				</view> -->
				<view class="input-content">
					<view class="input-item">
						<text class="title">店招名</text>
						<input placeholder="请输入您的店招名" v-model="registerParams.name" type="text" />
					</view>
					<view class="input-item">
						<text class="title">姓 名</text>
						<input placeholder="请输入您的姓名" v-model="registerParams.staffName" type="text" />
					</view>
				</view>
				<view class="input-content">
					<view class="input-item">
						<text class="title">所在地区</text>
						<view class="active-address" style="flex: 1">
							<uni-data-picker style="flex: 1" :localdata="addressItems" placeholder="请选择您店铺的所属地区"
								popup-title="请选择您店铺的所属地区" @change="onchange"
								:map="{ text: 'name', value: 'code' }"></uni-data-picker>
						</view>
					</view>

					<view class="input-item">
						<text class="title">详细地址</text>
						<textarea class="textarea-content" auto-height="true" v-model="registerParams.address"
							placeholder="请输入您店铺的详细地址"></textarea>
					</view>
					<view class="input-totast">
						<view style="font-size: 24rpx;">*注：</view>
						<view class="totast-content">
							详细地址请输入准确，否则将影响您的收货或维修；
						</view>
					</view>
					<view class="input-totast">
						<view style="font-size: 24rpx;">*注：</view>
						<view class="totast-content">
							为了方便商城精准推荐耗材，请在新增机型时登记准确的机器型号；
						</view>
					</view>
				</view>
				<!-- <view class="input-content input-totast">
					<view class="input-totast totast-color">
						<view class="totast-content totast-title">
							角色权限选择说明:
						</view>
					</view>
					<view class="input-totast totast-color">
						<view style="font-size: 24rpx;">1、</view>
						<view class="totast-content">
							老板：名下所有店铺都具有人员及设备管理权限；
						</view>
					</view>
					<view class="input-totast totast-color">
						<view style="font-size: 24rpx;">2、</view>
						<view class="totast-content">
							店长：仅有当前店铺的人员和设备管理权限；
						</view>
					</view>
					<view class="input-totast totast-color">
						<view style="font-size: 24rpx;">3、</view>
						<view class="totast-content">
							财务：可查看所有店铺信息，但是对设备和人员没有管理权限；
						</view>
					</view>
					<view class="input-totast totast-color">
						<view style="font-size: 24rpx;">4、</view>
						<view class="totast-content">
							报修员：只能对当前店铺进行设备管理，不能对人员进行管理。
						</view>
					</view>
				</view> -->
			</view>
			
			<button class="confirm-btn" :disabled="btnLoading" :loading="btnLoading" @tap="toRegister">
				提交
			</button>
		</view>
	</view>
</template>
<script>
	import {
		regionTree,
		customer,
		roles
	} from "@/api/login";

	import uniDataPicker from "@/components/uni-data-picker/uni-data-picker.vue";

	export default {
		data() {
			return {
				// HOME热键信息
				menuInfo: this.$getMenu.menuInfo,
				// 角色选项
				rolesItem: [],
				// 地址选项
				addressItems: [],
				// 提交信息
				registerParams: {},
				// 提交按钮动画
				btnLoading: false,
			};
		},
		components: {
			uniDataPicker,
		},
		onShow() {},
		onLoad(options) {
			this.$http.get(`${regionTree}`).then((res) => {
				this.addressItems = res.data;
			});
			this.$http.get(`${roles}`).then((res) => {
				res.data.splice(2,1)
				this.rolesItem = res.data
				this.registerParams.role = this.rolesItem[0].value
			});
		},
		methods: {
			// 选择地区
			onchange(e) {
				this.registerParams.code = e.detail.value[e.detail.value.length - 1].value;
			},
			// 注册账号
			async toRegister() {
				// if(!this.registerParams.role){
				// 	uni.showToast({
				// 	  title: '请选择角色',
				// 	  icon:'none'
				// 	});
				// 	return;
				// }
				if(!this.registerParams.name){
					uni.showToast({
					  title: '请输入店招名',
					  icon:'none'
					});
					return;
				}
				if(!this.registerParams.staffName){
					uni.showToast({
					  title: '请输入姓名',
					  icon:'none'
					});
					return;
				}
				if(!this.registerParams.code){
					uni.showToast({
					  title: '请选择地区',
					  icon:'none'
					});
					return;
				}
				if(!this.registerParams.address){
					uni.showToast({
					  title: '请输入详细地址',
					  icon:'none'
					});
					return;
				}
				await this.$http
					.put(`${customer}`, this.registerParams)
					.then((res) => {
						this.btnLoading = false;
						this.$mHelper.toast("恭喜您注册成功");
						this.$mRouter.reLaunch({
							route: "/pages/profile/profile"
						});
					})
					.catch(() => {
						this.btnLoading = false;
					});
			},
		},
	};
</script>
<style lang="scss">
	.register {
		position: relative;
		width: 100vw;
		overflow: hidden;
		background: #f5f6f8;

		.wrapper {
			position: relative;
			width: 100vw;
			min-height: 100vh;
			background: #f5f6f8;
			padding-bottom: 40upx;

			.welcome {
				width: 100%;
				padding: 150rpx 22rpx 40rpx;
				background: #f8f8f8 url("https://benyin-1315885374.cos.ap-chengdu.myqcloud.com/program%2Fuser_bg.png");
				background-size: 100% 100%;
				position: fixed;
				top: 0;
				left: 0;
				z-index: 99;

				.welcome-title {
					width: 100%;
					font-size: 46upx;
					color: #333;
					display: flex;
					align-items: center;
				}
			}

			.main_info {
				min-height: calc(100vh - 240rpx);
				padding: 240rpx 22rpx 130rpx;

				.input-content {
					background: #fff;
					margin-top: 22rpx;
					border-radius: 12rpx;
					padding: 15rpx 30upx;

					.input-item {
						display: flex;
						align-items: center;
						min-height: 120upx;
						border-radius: 4px;
						border-top: 2rpx solid #f5f6f8;

						&:last-child {
							margin-bottom: 0;
						}

						&:first-child {
							border-top: 0;
						}

						.title {
							height: 50upx;
							line-height: 56upx;
							margin-right: 30rpx;
							font-size: 32rpx;
							font-weight: bold;
							color: #333;
							width: 130rpx;
						}

						.active-roles,
						.active-address {
							display: flex;
							align-items: center;
							justify-content: space-between;
							position: relative;

							.u-icon {
								position: absolute;
								right: 0;
							}
						}

						input {
							flex: 1;
							height: 60upx;
							font-size: $font-base + 2upx;
							color: #666666;
						}

						.textarea-content {
							flex: 1;
							margin-top: 10rpx;
							font-size: $font-base + 2upx;
							color: #666666;
						}
					}

					.input-totast {
						width: 100%;
						color: #e5452f;
						display: flex;
						.totast-content{
							flex: 1;
							font-size: 24rpx !important;
						}
						.totast-title{
							font-weight: bold;
							color: #333333;
							font-size: 32rpx !important;
						}
					}
					
					.totast-color{
						color: #666666 !important;
					}
				}
			}

			.confirm-btn {
				position: fixed;
				bottom: 22px;
				left: 10%;
				width: 80%;
				display: flex;
				justify-content: center;
				align-items: center;
				background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
				border-radius: 50px;
				color: #fff;
			}
		}
	}


	/deep/.uni-data-pickerview {
		.item {
			justify-content: center;
		}

		.check {
			margin-left: 6px;
			margin-right: 0;
			border: 2px solid #e5452f !important;
			border-left: 0 !important;
			border-top: 0 !important;
		}
	}

	/deep/.selected-item-active {
		border-bottom: 2px solid #e5452f !important;
	}

	/deep/.uni-data-tree-dialog {
		top: 60% !important;
	}

	/deep/.active-roles .uni-data-pickerview .selected-area {
		display: none;
	}
</style>