<template>
	<view class="main">
		<view class="box2">
			<!-- 购物车列表 -->
			<!-- <mescroll-body ref="mescrollRef" @down="downCallback" @up="upCallback" :down="downOption" :up="upOption" :top="0"> -->
			<view class="cart-list" v-if="goodsList.length > 0">
				<view class="list" v-for="goods in goodsList" :key="goods.id">
					<view class="order-info">
						<view class="time">领料单号：{{ goods.outWarehouseId }}</view>
						<view class="time">{{ goods.createdAt }}</view>
						<view class="time">配送方式：{{ goods.logisticsType.label || '无' }}</view>
					</view>
					<view class="goods" v-for="good in goods.itemList" :key="good.id">
						<view class="thumb" @tap.stop="previewImage(good.imageFiles[0].url)">
							<image :src="good.imageFiles[0].url" mode=""></image>
						</view>
						<view class="content">
							<view class="item">
								<view class="title">
									<text class="two-omit" style="font-weight: bold">{{ good.inventoryName }}</text>
								</view>
								<view class="title">
									<text class="two-omit">编号：{{ good.inventoryCode }}</text>
								</view>
								<view class="oem">
									<text class="oem-item">OEM：{{ good.numberOem }}</text>
								</view>
								<view class="title">
									<text class="two-omit">渠道：{{ good.manufacturerChannel.label }}</text>
								</view>
								<view class="title">
									<text class="two-omit" style="color: red">储位：{{ good.location || '' }}</text>
								</view>
							</view>
							<view class="item-r">
								<view class="num" @tap.stop>
									<view class="number">
										<text>{{ good.outWarehouseNumber }}</text>
									</view>
								</view>
								<view class="cancel-text" v-if="good.outStatus.value === 'yck'">已出库</view>
								<view class="cancel-btn" v-if="good.outStatus.value === 'dck' || good.outStatus.value === 'bfck'" @tap.stop="handleChooseCheck(good)">审核</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view v-if="goodsList.length === 0" style="padding-top: 200rpx">
				<rf-empty :info="'暂无可出库订单'"></rf-empty>
			</view>
			<u-modal :show="showModel" showCancelButton closeOnClickOverlay @close="handleModelCancel" @cancel="handleModelCancel" @confirm="handleConfirm()">
				<view class="slot-content">
					<view class="list">
						<text>出库状态：</text>
						<u-tag :text="outWarehouseInfo.outStatus.label" plain size="mini" type="warning"></u-tag>
					</view>
					<view class="list">
						<text>应出库量：</text>
						<text>{{ outWarehouseInfo.outWarehouseNumber }}</text>
					</view>
					<view class="list">
						<text>已出库量：</text>
						<text>{{ outWarehouseInfo.auidtOutWarehouseNumber }}</text>
					</view>
					<view class="list">
						<text>取消出库量：</text>
						<text>{{ outWarehouseInfo.cancelOutWarehouseNumber }}</text>
					</view>
					<view class="list" style="width: 100%">
						<text>批次号：</text>
						<uni-data-select
							class="data-select"
							style="flex: 1"
							placeholder="请选择批次号"
							v-model="outWarehouseFrom.batchId"
							:localdata="batchCodeListRange"
							@change="handleBatchCode"
						></uni-data-select>
					</view>
					<view class="list" style="width: 100%">
						<text>库存量：</text>
						<text>{{ outWarehouseFrom.remWarehouseNumber }}</text>
					</view>
					<view class="list" style="width: 100%">
						<view class="adjust">
							出库数量：
							<u-number-box v-model="outWarehouseFrom.auidtOutWarehouseNumber" :min="0" integer :max="outWarehouseFrom.remWarehouseNumber"></u-number-box>
						</view>
					</view>
				</view>
			</u-modal>
			<view class="loading" v-if="isEnloading && goodsList.length">{{ isloading ? '加载中...' : goodsList.length < total ? '上拉加载更多' : '' }}</view>
			<view class="loading" v-if="goodsList.length > 0 && goodsList.length == total">—— 到底啦 ——</view>
		</view>
	</view>
</template>

<script>
import { getOutWarehouseList, getBatchNo, outWarehouseAudit } from '@/api/system.js';

export default {
	components: {},
	data() {
		return {
			// 是否到底
			isEnloading: false,
			// 分页参数
			pageNumber: 1,
			// 总数
			total: 0,
			// 是否加载完成
			isloading: true,
			goodsList: [],
			// 图片预览
			previewList: [],
			showModel: false,
			goodsId: '',
			batchCodeListRange: [], // 批次号下拉选择数据
			batchCodeList: [], // 批次号列表
			outWarehouseInfo: {},
			outWarehouseFrom: {
				batchCode: '', // 批次号
				auidtOutWarehouseNumber: 0 //出库数量
			}
		};
	},
	onLoad() {
		this.refresh();
		// this.getData();
	},
	// 滚动到底部
	onReachBottom() {
		if ((this.pageNumber - 1) * 10 >= this.total) {
			this.isEnloading = true;
			return;
		}
		this.getData(); // 商品列表
	},
	onPullDownRefresh() {
		if (this.isloading) return;
		uni.startPullDownRefresh({
			success: () => {
				this.refresh();
			}
		});
		uni.stopPullDownRefresh();
	},
	created() {},
	methods: {
		/**
		 * @description 出库审核
		 * @param {Object} good
		 */
		handleChooseCheck(good) {
			this.outWarehouseInfo = good;
			this.$set(this.outWarehouseFrom, 'id', good.id);
			this.$set(this.outWarehouseFrom, 'outWarehouseId', good.outWarehouseId);
			this.$set(this.outWarehouseFrom, 'inventoryCode', good.inventoryCode);
			getBatchNo({
				code: good.inventoryCode,
				warehouseId: good.warehouseId
			}).then((res) => {
				this.batchCodeList = res.data;
				this.batchCodeListRange = res.data.map((item) => ({
					text: item.batchCode,
					value: item.id
				}));
				this.showModel = true;
			});
		},
		handleBatchCode(e) {
			console.log(e);
			this.batchCodeList.map((item) => {
				if (item.id === e) {
					this.$set(this.outWarehouseFrom, 'batchCode', item.batchCode);
					// this.$set(this.outWarehouseFrom, 'batchId', item.id);
					this.$set(this.outWarehouseFrom, 'remWarehouseNumber', item.remWarehouseNumber || 0);
				}
			});
		},
		handleModelCancel() {
			this.refreshOuthouseForm();
			this.showModel = false;
		},
		/**
		 * @description 确认取消领取耗材
		 */
		handleConfirm() {
			const { outWarehouseFrom } = this;
			if (!outWarehouseFrom.batchCode) {
				uni.showToast({
					title: '请选择批次号',
					icon: 'none'
				});
				return;
			}
			if (outWarehouseFrom.auidtOutWarehouseNumber === 0) {
				uni.showToast({
					title: '出库数量不能为0',
					icon: 'none'
				});
				return;
			}
			if (outWarehouseFrom.auidtOutWarehouseNumber > outWarehouseFrom.remWarehouseNumber) {
				uni.showToast({
					title: '出库数量不能大于库存数量',
					icon: 'none'
				});
				return;
			}
			outWarehouseAudit(outWarehouseFrom).then(async (res) => {
				uni.showToast({
					title: '审核成功',
					icon: 'none'
				});
				await this.refreshOuthouseForm();
				await this.refresh();
				this.showModel = false;
			});
		},
		previewImage(url) {
			this.previewList = [];
			this.previewList.push(url);
			uni.previewImage({
				urls: this.previewList,
				current: 0
			});
		},
		getData() {
			this.isloading = true;
			getOutWarehouseList({
				pageNumber: this.pageNumber,
				pageSize: 10
			}).then((res) => {
				this.pageNumber++;
				this.isloading = false;
				this.isEnloading = false;
				this.goodsList = [...this.goodsList, ...res.data.rows];
				this.total = res.data.total;
			});
		},
		refresh() {
			this.pageNumber = 1;
			this.goodsList = [];
			this.total = 0;
			this.getData();
		},
		refreshOuthouseForm() {
			this.outWarehouseFrom = {
				batchCode: '', // 批次号
				auidtOutWarehouseNumber: 0 //出库数量
			};
		}
	}
};
</script>

<style scoped lang="scss">
.main {
	width: 100vw;
	height: 100vh;
	padding: 0 22rpx;
	box-sizing: border-box;
	background-color: #fff;
	.slot-content {
		width: 100%;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		justify-content: space-between;
		.list {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			line-height: 2.5;
			width: 50%;
			.adjust {
				display: flex;
				align-items: center;
			}
		}
	}
	.input-totast {
		width: 100%;
		color: #e5452f;
		display: flex;

		.totast-content {
			flex: 1;
			font-size: 28rpx !important;
		}
	}

	.pd_body {
		width: 100%;
		// position: relative;

		.pd_input {
			font-size: 14px !important;
			border: 1px solid #e5e5e5 !important;
			box-sizing: border-box !important;
			border-radius: 4px !important;
			height: 80rpx !important;
			padding: 0 22rpx !important;
		}

		.socrll-check {
			width: 100%;
			position: absolute;
			top: 100rpx;
			left: 0;
			z-index: 3;
			padding: 0 22rpx;

			/deep/.uni-select__selector-scroll {
				width: 100%;
				height: 210px;
				padding: 4px 22rpx !important;
				box-sizing: border-box;
				background-color: #ffffff;
				border: 1px solid #ebeef5;
				border-radius: 6px;
				box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

				.socrll-list {
					height: 210px;
					overflow: hidden;
					overflow-y: scroll;
				}

				.socrll-item {
					display: flex;
					cursor: pointer;
					line-height: 35px;
					font-size: 14px;
					text-align: center;
				}
			}

			.no-socrll-list {
				height: 45px;
				padding: 4px 22rpx !important;
				box-sizing: border-box;
				background-color: #ffffff;
				border: 1px solid #ebeef5;
				border-radius: 6px;
				box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
				overflow: hidden;
				overflow-y: scroll;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 28rpx;
				color: #999;
			}

			.socrll-popper__arrow {
				filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
				position: absolute;
				top: -4px;
				left: 15%;
				margin-right: 3px;
				border-top-width: 0;
				border: 1px solid #ebeef5;
				border-bottom-color: #ffffff;
				border-right-color: #ffffff;
				width: 18rpx;
				height: 18rpx;
				background-color: #ffffff;
				transform: rotate(45deg);
			}
		}
	}

	.service {
		width: 50rpx;
		min-height: 80rpx;
		position: fixed;
		right: 0;
		bottom: 200rpx;
		text-align: center;
		border-radius: 8rpx 0 0 8rpx;
		float: right;
		border: 2rpx solid #e5452f;
		border-right: 0;
		color: #e5452f;
	}

	.footer {
		width: 50%;
		display: flex;
		align-items: center;
		margin: auto;
		.confirm-btn {
			width: 50%;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border: none !important;
			color: #fff;
			border-radius: 37px;
			font-size: 28rpx;
			margin: 0;
		}

		.save {
			background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
		}

		.cancel {
			background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
		}
	}
}
/* 购物车列表 */
.cart-list {
	width: 100%;
	padding: 20rpx 0;

	.list {
		background-color: #ffffff;
		box-shadow: 0 0 20rpx #f6f6f6;
		border-radius: 10rpx;
		.order-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex-wrap: wrap;
		}
		.goods {
			display: flex;
			align-items: center;
			width: 100%;
			height: 200rpx;
			margin: 10rpx 0;
			padding: 10rpx 0;
			.thumb {
				display: flex;
				// align-items: center;
				justify-content: center;
				align-items: center;
				width: 27%;
				height: 100%;
				// margin-top: 20rpx;

				image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 10rpx;
				}
			}

			.content {
				flex: 1;
				display: flex;
				align-items: center;
				.item {
					flex: 3;
					padding: 10rpx 0;
					// width: 80%;
					height: 100%;

					.title {
						display: flex;
						align-items: center;
						width: 100%;
						height: auto;

						text {
							font-size: 26rpx;
							color: #212121;
						}
					}
					.oem {
						text {
							font-size: 24rpx;
							color: #212121;
						}
					}
					.attribute {
						display: flex;
						align-items: center;
						margin-top: 10rpx;

						.attr {
							display: flex;
							align-items: center;
							padding: 5rpx 20rpx;
							// height: 40rpx;
							background-color: #f6f6f6;
							border-radius: 10rpx;

							text {
								font-size: 24rpx;
								color: #333333;
							}

							.more {
								display: flex;
								width: 10rpx;
								height: 10rpx;
								border-left: 2rpx solid #333333;
								border-bottom: 2rpx solid #333333;
								transform: rotate(-45deg);
								margin-left: 10rpx;
							}
						}
					}

					.price-num {
						display: flex;
						align-items: center;
						justify-content: space-between;
						height: 45rpx;

						.price {
							display: flex;
							align-items: center;

							.min {
								color: #fe3b0f;
								font-size: 24rpx;
							}

							.max {
								font-size: 28rpx;
								color: #fe3b0f;
								font-weight: bold;
							}
						}
					}
				}
				.item-r {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					gap: 20rpx;
					.num {
						display: flex;
						height: 40rpx;

						.add {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 60rpx;
							height: 40rpx;
							background-color: #ffffff;

							text {
								color: #212121;
								font-size: 24rpx;
							}
						}

						.number {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 80rpx;
							height: 40rpx;
							background-color: #f6f6f6;
							border-radius: 8rpx;
							text-align: center;

							text {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
					.cancel-text {
						font-size: 24rpx;
					}
					.cancel-btn {
						width: 60%;
						height: 45rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						border: none !important;
						color: #fff;
						border-radius: 37px;
						font-size: 25rpx;
						margin: 0;
						background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
					}
				}
			}
		}
	}
}

.background {
	background: #f5f6f8;
}

.time {
	font-size: 26rpx;
	color: #827f7f;
	font-weight: 800;
	padding: 10rpx 0;
}

.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 40rpx;
}
</style>
