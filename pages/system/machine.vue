<template>
	<view class="main">
		<view class="content" v-if="brandList.length != 0">
			<view class="staff-info" v-for="(item, index) in brandList" :key="index">
				<img class="staff-head" :src="item.deviceGroupImg.url ? item.deviceGroupImg.url : '@/static/images/top.png'"
					alt="" />
				<view class="item-right">
					<view class="name-info">
						<view class="staff-name">{{ item.deviceGroup.label }}
							<view class="del" :class="item.status ? 'isopen' : ''" @click="delInfoFn(item,index)">
								{{ item.status ? '禁用' : '启用'}}
							</view>
						</view>
					</view>
					<!-- <view class="staff-phone">{{item.tel}}</view> -->
					<uni-data-picker :localdata="brandCheck" :clear-icon="false" :value="item.productId" readonly="true"
						:map="{ text: 'name', value: 'id' }"></uni-data-picker>
				</view>
			</view>
			<view class="loading" v-if="brandList.length !== 0 && loading == false">{{ totast }}</view>
		</view>
		<view class="footer">
			<button class="confirm-btn" @click="addInfoFn()">新增设备</button>
		</view>
		<!-- 暂无员工信息 -->
		<rf-empty :info="'暂无设备信息'" v-if="brandList.length === 0"></rf-empty>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="loadingInit"></rfLoading>
	</view>
</template>

<script>
	import {
		customerDeviceGoupPage,
		customerAllTree,
		customerUpdateStatus
	} from "@/api/index";
	export default {
		data() {
			return {
				brandList: [], //列表数据
				brandListParms: {
					customerId: uni.getStorageSync("userInfo").customerId,
					desc: false,
					orderBy: "",
					pageNumber: 1,
					pageSize: 10,
				},
				brandCheck: [],
				// 关联品牌
				brandParms: {
					pageNumber: 1, // 页码
					pageSize: 9999, // 页大小
					desc: false, // 是否倒叙排序 true:倒叙(DESC) false:升序(ASC)
					fullIdPath: null, // 品牌/产品树/系列id全路径（1、2、3级分组）
					name: null, // 	机型/机型全称
					orderBy: null, // 排序字段名称
				},
				loading: false,
				loadingInit: true,
				total: 0, //总数
				totast: "", // —— 到底啦 ——
			};
		},
		onShow() {
			// this.$mHelper.loadVerify();
			this.refresh();
		},
		methods: {
			// 获取列表
			customersStaff() {
				this.totast = "加载中...";
				this.$http.get(customerDeviceGoupPage, this.brandListParms).then((res) => {
					if (res.code === 200) {
						this.loading = true;
						this.loadingInit = false;
						res.data.rows.forEach((item) => {
							this.brandList.push(item);
						});
						this.total = res.data.total;
					}
				}).catch(err => {
					this.loadingInit = false;
				});
			},
			// 获取管理品牌
			brandCheckFn() {
				this.$http.get(customerAllTree, this.brandParms).then((res) => {
					this.brandCheck = res.data;
				});
			},
			// 去新增
			addInfoFn() {
				let route = "/pages/system/addMachine";
				this.$mRouter.push({
					route,
				});
			},
			// 删除
			async delInfoFn(item, index) {
				let verify = await this.$http.post(this.$mHelper.verifyAccessToken);
				if (verify.data === 2) {
					this.$mHelper.toast("网络异常，请稍后再试...");
					this.$mHelper.relogin();
					return false;
				}
				let parms = {
					customerId: item.customerId,
					deleted: 0,
					id: item.id,
					productId: item.productId,
					status: !item.status,
				}
				uni.showModal({
					content: `确认${ item.status ?  "禁用" :"启用" }该设备？`,
					success: (res) => {
						if (res.confirm) {
							this.loadingInit = true;
							this.$http
								.post(customerUpdateStatus, parms)
								.then((res) => {
									this.loadingInit = false;
									this.brandList[index].status = !item.status
									uni.showToast({
										title: item.status ? '已启用' : '已禁用',
										icon: 'none'
									});
								});
						} else {
							console.log("cancel"); //点击取消之后执行的代码
						}
					},
				});
			},
			refresh() {
				this.brandList = [];
				this.brandCheck = [];
				this.brandListParms.pageNumber = 1
				this.customersStaff()
				this.brandCheckFn();
			}
		},
		onReachBottom() {
			if (this.brandList.length == this.total) {
				this.totast = "—— 到底啦 ——";
				this.loading = false;
				return;
			}
			this.brandListParms.pageNumber++;
			this.customersStaff();
		},
	};
</script>

<style lang="scss" scoped>
	.main {
		width: 100%;
		min-height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-bottom: 136rpx;
		box-sizing: border-box;

		.content {
			width: 100%;
			min-height: calc(100vh - 136rpx);
			padding: 22rpx 22rpx 0;
			display: flex;
			flex-direction: column;

			.staff-info {
				width: 100%;
				min-height: 20rpx;
				padding: 20rpx;
				border-radius: 8rpx;
				display: flex;
				align-items: center;
				background-color: #fff;
				font-size: 28rpx;
				color: #000;
				margin-bottom: 20rpx;

				.staff-head {
					width: 130rpx;
					height: 130rpx;
					border-radius: 8rpx;
					margin-right: 15rpx;
				}

				.item-right {
					flex: 1;
					min-height: 130rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.name-info {
						width: 100%;
						display: flex;
						align-items: center;
						justify-content: space-between;

						.staff-name {
							flex: 1;
							font-size: 32rpx;
							display: flex;
							align-items: center;
							justify-content: space-between;

							.isopen {
								color: #000 !important;
								border: 1px solid #000 !important;
							}

							.del {
								color: #e5452f;
								border: 1px solid #e5452f;
								padding: 4rpx 12rpx;
								font-size: 22rpx;
								border-radius: 10px;
							}
						}

						.staff-post {
							color: #666;
						}
					}

					.staff-phone {
						width: 100%;
						display: flex;
						align-items: center;
						justify-content: space-between;
					}
				}
			}

			.loading {
				height: 80upx;
				line-height: 80upx;
				text-align: center;
				color: #ccc;
				font-size: 24upx;
				width: 100%;
				margin-bottom: 20upx;
			}
		}

		.footer {
			width: 100%;
			padding: 18rpx 0 42rpx;
			display: flex;
			justify-content: center;
			background-color: #fff;
			position: fixed;
			bottom: 0;
			left: 0;

			.confirm-btn {
				width: 80%;
				margin: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
				border-radius: 50px;
				color: #fff;
			}
		}
	}

	/deep/.input-value {
		padding: 0 !important;
		// height: 80rpx;
		font-size: 12px;
		height: auto !important;
		min-height: 84rpx !important;
	}

	/deep/.input-value-border {
		border: none;
	}

	/deep/.selected-list {
		// display: none !important,
		flex-wrap: wrap;

		.selected-item:nth-of-type(2) {
			display: none !important,
		}

		.selected-item:nth-of-type(3) {
			display: none !important,
		}
	}

	/deep/.text-color {
		font-size: 14px;
	}

	/deep/.placeholder {
		display: none !important;
	}
</style>