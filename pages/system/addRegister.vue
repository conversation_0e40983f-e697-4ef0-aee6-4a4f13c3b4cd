<template>
	<view class="main">
		<form class="formTable">
			<uni-section title="姓名" type="line">
				<input class="uni-input" name="input" v-model="parms.name" placeholder="请输入员工姓名" />
				<uni-icons v-if="parms.name" type="close" size="17" class="close_icon" color="#999"
					@click.stop="clear1"></uni-icons>
			</uni-section>
			<uni-section title="手机号" type="line">
				<input class="uni-input" type="number" maxlength="11" name="input" v-model="parms.tel" placeholder="请输入员工手机号" />
				<uni-icons v-if="parms.tel" type="close" size="17" class="close_icon" color="#999"
					@click.stop="clear2"></uni-icons>
			</uni-section>
			<uni-section title="角色" sub-title="(请选择角色 老板、财务、报修员、店长)" type="line">
				<uni-data-select field="label as text" v-model="parms.role" :localdata="range1"
					:clear="false"></uni-data-select>
			</uni-section>
		</form>
		<view class="footer">
			<button class="confirm-btn save" @click="saveFn(0)">取消</button>
			<button class="confirm-btn cancel" @click="saveFn(1)">保存</button>
		</view>
	</view>
</template>

<script>
	import {
		customersStaff
	} from "@/api/index";
	export default {
		data() {
			return {
				range1: [],
				range2: [{
						value: "0",
						text: "禁用",
					},
					{
						value: "1",
						text: "启用",
					},
				],
				parms: {
					customerId: uni.getStorageSync("userInfo").customerId, //	客户id
					name: "", // 员工名称
					role: "", // 角色(字典项码)
					status1: "1", // 账号状态(true启用/false禁用)
					status: true, // 账号状态(true启用/false禁用)
					tel: "", // 手机号
					createdAt: null, // 创建时间
					deleted: null, // 是否删除
					id: null, // id
					updatedAt: null, // 更新时间
				},
			};
		},
		onLoad() {
			// this.$mHelper.loadVerify();
		},
		onShow() {
			this.range1Fn();
		},
		created() {},
		methods: {
			clear1() {
				uni.hideKeyboard();
				this.parms.name = '';
			},
			clear2() {
				uni.hideKeyboard();
				this.parms.tel = '';
			},
			range1Fn() {
				this.$http
					.get(`/api/magina/api/code/dict-item/${500}/tree`)
					.then((res) => {
						res.data.forEach((item) => {
							this.range1.push({
								value: item.value,
								text: item.label,
							});

						});
						this.parms.role = this.range1[0].value
					});
			},
			async saveFn(val) {
				let verify = await this.$http.post(this.$mHelper.verifyAccessToken);
				if (verify.data === 2) {
					this.$mHelper.toast("网络异常，请稍后再试...");
					this.$mHelper.relogin();
					return false;
				}
				const regMobile =
					/^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[189])\d{8}$/;
				if (val) {
					if (!this.parms.name) {
						uni.showToast({
							title: "请输入员工姓名",
							icon: "none",
						});
						return;
					} else if (!this.parms.role) {
						uni.showToast({
							title: "请选择员工角色",
							icon: "none",
						});
						return;
					} else if (!this.parms.status1) {
						uni.showToast({
							title: "请选择员工账号状态",
							icon: "none",
						});
						return;
					}

					if (!this.parms.tel) {
						uni.showToast({
							title: "请输入员工电话",
							icon: "none",
						});
						return;
					} else if (!regMobile.test(this.parms.tel)) {
						uni.showToast({
							title: "请输入正确的手机号",
							icon: "none",
						});
						return;
					}

					this.parms.status1 == "1" ?
						(this.parms.status = true) :
						(this.parms.status = false);
					this.$http
						.post(customersStaff, this.parms)
						.then((res) => {
							uni.showToast({
								title: "添加成功",
								icon: "none",
							});
							setTimeout(() => {
								this.toPageFn();
							}, 300);
						})
						.catch((err) => {
							uni.showToast({
								title: "请勿重复提交",
								icon: "none",
							});
						});
				} else {
					this.toPageFn();
				}
			},
			toPageFn() {
				uni.navigateBack({
					delta: 1,
				});

				// uni.redirectTo({
				//   url: "/pages/system/register",
				// });
			},
		},
	};
</script>

<style lang="scss" scoped>
	.main {
		width: 100vw;
		height: 100vh;
		padding: 0 22rpx;
		box-sizing: border-box;
		background-color: #fff;

		.formTable {
			// width: 100%;
			// padding: 22rpx;
			// box-sizing: border-box;
		}

		.footer {
			width: 100%;
			display: flex;
			align-items: center;
			position: fixed;
			bottom: 22px;
			left: 0;

			.confirm-btn {
				width: 40%;
				height: 75rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border: none !important;
				color: #fff;
				border-radius: 37px;
			}

			.save {
				background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
			}

			.cancel {
				background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
			}
		}
	}

	/deep/.uni-select {
		height: 80rpx !important;
		padding: 0 22rpx !important;
	}

	/deep/.line {
		background: linear-gradient(to bottom, #e5452f 0%, #ee822f 100%) !important;
	}

	/deep/.uni-section-content {
		height: 80rpx !important;
		padding: 0 22rpx !important;
		position: relative;

		.close_icon {
			position: absolute;
			right: 44rpx;
			top: 16rpx;
			z-index: 10000;
		}
	}

	/deep/.uni-section__content-title {
		color: #000 !important;
		font-size: 32rpx !important;
	}

	/deep/.uni-input {
		height: 80rpx !important;
		line-height: 40rpx !important;
		border: 1rpx solid #e5e5e5;
		padding: 15rpx;
		border-radius: 10rpx;
	}

	/deep/.uni-select__input-placeholder {
		font-size: 14px !important;
		color: #999 !important;
	}

	/deep/.uni-section-header__content {
		flex-direction: row !important;
		align-items: center;
	}

	/deep/.uni-section-header__content-sub {
		margin: 0 !important;
		margin-left: 15rpx !important;
		color: #f5c744 !important;
	}
</style>