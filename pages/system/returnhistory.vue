<template>
	<view class="main">
		<!-- 		<view class="box1">
			<FormQuery :form-columns="formColumns" @search="search" />
		</view> -->
		<view class="box2">
			<!-- 购物车列表 -->
			<!-- <mescroll-body ref="mescrollRef" @down="downCallback" @up="upCallback" :down="downOption" :up="upOption" :top="0"> -->
			<view class="cart-list" v-if="goodsList.length > 0">
				<view class="list" v-for="goods in goodsList" :key="goods.id">
					<view class="order-info">
						<view class="time">领料单号：{{ goods.code }}</view>
						<view class="time">{{ goods.createdAt }}</view>
					</view>
					<view class="goods" v-for="good in goods.applyReturnDetailList" :key="good.id">
						<view class="thumb" @tap.stop="previewImage(good.itemStore.skuInfo.picUrl[0].url)">
							<image :src="good.itemStore.skuInfo.picUrl[0].url" mode=""></image>
						</view>
						<view class="item">
							<view class="title">
								<text class="two-omit">{{ good.itemStore.itemName }}</text>
							</view>
							<view class="oem">
								<text class="oem-item">OEM编号：{{ good.itemStore.oemNumber }}</text>

								<u-tag :text="getStatusText(goods.status)" plain :type="getStatusType(goods.status)" size="mini"></u-tag>
							</view>
							<view class="attribute">
								<view class="attr">
									<text>{{ good.itemStore.skuInfo.saleAttrVals.map((item) => item.val) }}</text>
								</view>
							</view>
							<view class="price-num">
								<view class="price">
									<text class="min">￥</text>
									<text class="max">{{ good.itemStore.saleUnitPrice }}</text>
								</view>
								<view class="num" @tap.stop>
									<!-- <text class="add" @tap.stop="changeBuyNumber(good, 'sub')">
                    <text class="iconfont icon-subtract"></text>
                  </text> -->
									<view class="number">
										<text>{{ good.num }}</text>
									</view>
									<!-- <text class="add" @tap.stop="changeBuyNumber(good, 'add')">
                    <text class="iconfont icon-add"></text>
                  </text> -->
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view v-if="goodsList.length === 0" style="padding-top: 200rpx">
				<rf-empty :info="'暂无耗材申领记录'"></rf-empty>
			</view>
			<view class="loading" v-if="isEnloading && goodsList.length">{{ isloading ? '加载中...' : goodsList.length < total ? '上拉加载更多' : '' }}</view>
			<view class="loading" v-if="goodsList.length > 0 && goodsList.length == total">—— 到底啦 ——</view>
		</view>
	</view>
</template>

<script>
import { returnItemStorelist } from '@/api/index';
import FormQuery from '@/components/formQuery/index';
export default {
	components: { FormQuery },
	data() {
		return {
			// 是否到底
			isEnloading: false,
			// 分页参数
			pageNumber: 1,
			// 总数
			total: 0,
			// 是否加载完成
			isloading: true,
			goodsList: [],
			// 图片预览
			previewList: [],
			formColumns: [
				{
					dataIndex: 'applyCode',
					title: '领料单号',
					valueType: 'input'
				},

				{
					dataIndex: 'date',
					title: '领料时间',
					valueType: 'datetime',
					inputType: 'daterange'
				},
				{
					dataIndex: 'oemNumber',
					title: 'OEM编号',
					valueType: 'input'
				}
			]
		};
	},
	onLoad() {},
	onShow() {
		this.getData();
	},
	// 滚动到底部
	onReachBottom() {
		if ((this.pageNumber - 1) * 10 >= this.total) {
			this.isEnloading = true;
			return;
		}
		console.log('触底');
		this.getData(); // 商品列表
	},
	// 下拉刷新
	onPullDownRefresh() {
		// this.getData(); // 商品列表
		uni.stopPullDownRefresh(); // 停止下拉刷新
	},
	created() {},
	methods: {
		previewImage(url) {
			this.previewList = [];
			this.previewList.push(url);
			uni.previewImage({
				urls: this.previewList,
				current: 0
			});
		},
		getData() {
			this.isloading = true;
			this.$http
				.post(returnItemStorelist, {
					pageNumber: this.pageNumber,
					pageSize: 10
				})
				.then((res) => {
					this.pageNumber++;
					this.isloading = false;
					this.isEnloading = false;
					this.goodsList = [...this.goodsList, ...res.data.rows];
					this.total = res.data.total;
				});
		},
		getStatusText(status) {
			switch (status) {
				case 'CREATE':
					return '提交申请';
				case 'WAIT_IN_WAREHOUSE':
					return '待入库';
				case 'REJECT':
					return '驳回';
				case 'DONE':
					return '完成';
				default:
					return '';
			}
		},
		getStatusType(status) {
			switch (status) {
				case 'CREATE':
					return 'warning';
				case 'WAIT_IN_WAREHOUSE':
					return 'primary';
				case 'REJECT':
					return 'error';
				case 'DONE':
					return 'success';
				default:
					return 'primary';
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	width: 100vw;
	height: 100vh;
	padding: 0 22rpx;
	box-sizing: border-box;
	background-color: #fff;
	.box2 {
		// margin-top: 420rpx;
	}

	.input-totast {
		width: 100%;
		color: #e5452f;
		display: flex;

		.totast-content {
			flex: 1;
			font-size: 28rpx !important;
		}
	}

	.service {
		width: 50rpx;
		min-height: 80rpx;
		position: fixed;
		right: 0;
		bottom: 200rpx;
		text-align: center;
		border-radius: 8rpx 0 0 8rpx;
		float: right;
		border: 2rpx solid #e5452f;
		border-right: 0;
		color: #e5452f;
	}
}

.icons {
	font-family: iconfont;
	font-size: 32upx;
	font-style: normal;
	color: #999;
}

/deep/.uni-section-header {
	width: 150rpx !important;
	float: left;
	clear: both;
}
</style>
<style scoped lang="scss">
.page {
	// padding-bottom: 200rpx;
	background: #f5f6f8;
	// min-height: 100vh;
	min-height: 100vh;
}

.head {
	position: fixed;
	left: 0;
	top: 0;
	z-index: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 80rpx;

	background-color: #ffffff;

	.title {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;
		font-size: 30rpx;
		color: #212121;
	}

	.edit {
		position: absolute;
		right: 20rpx;
		top: 50%;
		transform: translate(0, -50%);
		background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
		padding: 8rpx 20rpx;
		border-radius: 60px;

		text {
			color: #fff;
			font-size: 26rpx;
		}
	}
}

/* 购物车列表 */
.cart-list {
	width: 100%;
	padding: 20rpx 0;

	.list {
		background-color: #ffffff;
		box-shadow: 0 0 20rpx #f6f6f6;
		border-radius: 10rpx;
		.order-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.goods {
			display: flex;
			align-items: center;
			width: 100%;
			height: 210rpx;

			.thumb {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 30%;
				height: 100%;
				// margin-top: 20rpx;

				image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 10rpx;
				}
			}

			.item {
				padding: 10rpx 0;
				width: 70%;
				height: 100%;

				.title {
					display: flex;
					align-items: center;
					width: 100%;
					height: auto;

					text {
						font-size: 26rpx;
						color: #212121;
					}
				}
				.oem {
					display: flex;
					justify-content: space-between;
					text {
						font-size: 24rpx;
						color: #212121;
					}
				}
				.attribute {
					display: flex;
					align-items: center;
					margin-top: 10rpx;

					.attr {
						display: flex;
						align-items: center;
						padding: 0 20rpx;
						height: 40rpx;
						background-color: #f6f6f6;
						border-radius: 10rpx;

						text {
							font-size: 24rpx;
							color: #333333;
						}

						.more {
							display: flex;
							width: 10rpx;
							height: 10rpx;
							border-left: 2rpx solid #333333;
							border-bottom: 2rpx solid #333333;
							transform: rotate(-45deg);
							margin-left: 10rpx;
						}
					}
				}

				.price-num {
					display: flex;
					align-items: center;
					justify-content: space-between;
					height: 45rpx;

					.price {
						display: flex;

						.min {
							color: #fe3b0f;
							font-size: 24rpx;
						}

						.max {
							font-size: 28rpx;
							color: #fe3b0f;
							font-weight: bold;
						}
					}

					.num {
						display: flex;
						height: 40rpx;
						margin-right: 20rpx;

						.add {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 60rpx;
							height: 40rpx;
							background-color: #ffffff;

							text {
								color: #212121;
								font-size: 24rpx;
							}
						}

						.number {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 80rpx;
							height: 40rpx;
							background-color: #f6f6f6;
							border-radius: 8rpx;
							text-align: center;

							text {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
				}
			}
		}
	}
}

.background {
	background: #f5f6f8;
}

/* 购物车失效商品列表 */
.lose-efficacy-list {
	width: 100%;
	background-color: #ffffff;
	padding: 0 30rpx;
	margin-top: 30rpx;
	border-radius: 10rpx;
	overflow: hidden;

	.lose-efficacy-title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		height: 80rpx;

		.title {
			display: flex;
			align-items: center;
			height: 100%;

			text {
				font-size: 28rpx;
				color: #222222;
			}
		}

		.empty {
			display: flex;
			align-items: center;
			height: 100%;

			text {
				font-size: 26rpx;
				color: #fe3b0f;
			}
		}
	}

	.list {
		display: flex;
		align-items: center;
		width: 100%;
		height: 185rpx;
		border-bottom: 1px solid #f6f6f6;

		.tag {
			display: flex;
			align-items: center;
			width: 10%;
			height: 100%;

			text {
				padding: 4rpx 10rpx;
				font-size: 24rpx;
				color: #ffffff;
				background-color: rgba(0, 0, 0, 0.3);
				border-radius: 20rpx;
			}
		}

		.goods {
			display: flex;
			align-items: center;
			width: 90%;
			height: 100%;
			background-color: #ffffff;
			border-radius: 10rpx;

			.pictrue {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 30%;
				height: 100%;

				image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 10rpx;
				}
			}

			.item {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				width: 70%;
				height: 160rpx;

				.title {
					width: 100%;

					text {
						font-size: 28rpx;
						color: #999999;
					}
				}

				.explain {
					display: flex;
					align-items: center;

					text {
						font-size: 24rpx;
						color: #222222;
					}
				}
			}
		}
	}
}

.time {
	font-size: 24rpx;
	color: #827f7f;
	font-weight: 800;
	padding: 10rpx 0;
}

/* 为你推荐 */
.recommend-info {
	width: 100%;
	background-color: #f2f2f2;
	cursor: pointer;

	.recommend-title {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;

		.title {
			display: flex;
			align-items: center;

			image {
				width: 416rpx;
				height: 40rpx;
			}
		}
	}

	.goods-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 0 30rpx;

		.list {
			width: 49%;
			height: 540rpx;
			margin-bottom: 20rpx;
			background-color: #ffffff;
			border-radius: 10rpx;
			overflow: hidden;

			.pictrue {
				display: flex;
				justify-content: center;
				width: 100%;

				image {
					height: 350rpx;
				}
			}

			.title-tag {
				// display: flex;
				height: 100rpx;
				padding: 20rpx;

				.tag {
					float: left;
					margin-right: 10rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					white-space: normal;
					font-size: 26rpx;
					line-height: 40rpx;

					text {
						font-size: 24rpx;
						color: #ffffff;
						padding: 4rpx 16rpx;
						background: linear-gradient(to right, #fe3b0f, #fc603a);
						border-radius: 6rpx;
						margin-right: 10rpx;
					}
				}
			}

			.price-info {
				display: flex;
				flex-wrap: wrap;
				align-items: center;
				justify-content: space-between;
				padding: 0 20rpx;
				height: 80rpx;

				.user-price {
					display: flex;
					align-items: center;

					text {
						color: #ff0000;
					}

					.min {
						font-size: 24rpx;
					}

					.max {
						font-size: 32rpx;
					}
				}

				.vip-price {
					display: flex;
					align-items: center;

					image {
						width: 26rpx;
						height: 26rpx;
						margin-right: 10rpx;
					}

					text {
						color: #fcb735;
						font-size: 24rpx;
					}
				}
			}
		}
	}
}

/* 结算 */
.close-account {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 100rpx;
	background-color: #ffffff;
	border-top: 2rpx solid #f6f6f6;

	.check-total {
		display: flex;
		align-items: center;
		width: 50%;
		height: 100%;

		.check {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 40%;
			height: 100%;

			text {
				font-size: 36rpx;
				color: #333333;
			}

			.icon-checked {
				color: #fe3b0f;
				// box-shadow: 0 0 10rpx  #fe3b0f;
			}

			.all {
				font-size: 24rpx;
				margin-left: 10rpx;
			}
		}

		.total {
			display: flex;
			align-items: center;
			width: 60%;
			height: 100%;

			text {
				font-size: 24rpx;
				color: #333333;
			}

			.price {
				font-weight: bold;
				color: #fe3b0f;
			}
		}
	}

	.account {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-right: 4%;
		margin-top: 20rpx;

		.btn-calculate {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 300rpx;
			height: 60rpx;
			background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
			border-radius: 60rpx;

			text {
				color: #ffffff;
				font-size: 24rpx;
			}
		}

		.btn-del {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.attention {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 140rpx;
				height: 60rpx;
				border: 2rpx solid #eeeeee;
				border-radius: 60rpx;
				color: #333333;
				font-size: 24rpx;
				margin-right: 20rpx;
			}

			.del {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100rpx;
				height: 60rpx;
				background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
				border-radius: 60rpx;
				color: #ffffff;
				font-size: 24rpx;
			}
		}
	}
}

.icon-subtract {
	position: relative;
	margin: 0 20rpx;

	&::after {
		content: '';
		display: block;
		width: 25rpx;
		height: 5rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}
}

.icon-add {
	position: relative;
	margin: 0 20rpx;

	&::after {
		content: '';
		display: block;
		width: 25rpx;
		height: 5rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}

	&::before {
		content: '';
		display: block;
		width: 5rpx;
		height: 25rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}
}

.check-icon {
	width: 35rpx;
	height: 35rpx;
	border: 1px solid #999999;
	border-radius: 50%;

	&.active {
		border: 1px solid #e5452f;
		color: #e5452f !important;
		line-height: 30rpx;
	}
}

/deep/.uni-stat__select {
	width: 80%;
}

.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 40rpx;
}
</style>
