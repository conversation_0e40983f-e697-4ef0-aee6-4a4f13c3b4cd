<template>
  <view class="page">
    <view class="goods-tab">
      <view class="tab">
        <text class="action">默认</text>
      </view>
      <view class="tab">
        <text>降价</text>
      </view>
      <view class="tab">
        <text>促销</text>
      </view>
      <view class="tab">
        <text>分类</text>
      </view>
      <view class="tab" @click="isEdit = !isEdit">
        <text>{{ isEdit ? '完成' : '编辑' }}</text>
      </view>
    </view>
    <view class="goods-list">
      <view class="list" v-for="(item, index) in 8" :key="index">
        <view class="check" :style="isEdit ? 'display: flex' : 'display: none'">
          <text class="iconfont icon-check"></text>
        </view>
        <view class="thumb">
          <image :src="'/static/img/goods_thumb_0' + (index + 1) + '.png'" mode=""></image>
        </view>
        <view class="item">
          <view class="title">
            <text class="two-omit">薇妮(Viney)时尚包包女包牛皮单肩包女休闲百搭斜挎包韩版小方包潮(枪色)</text>
          </view>
          <view class="price-more">
            <view class="price">￥188.00</view>
            <view class="depreciate" :style="!isEdit ? 'display: flex' : 'display: none'">
              <text>比关注时降价300.00元</text>
            </view>
          </view>
          <view class="goods-btn">
            <view class="btn">
              <text>看相似</text>
              <text>降价通知</text>
            </view>
            <view class="cart">
              <text class="iconfont icon-cart"></text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="footer-btn" :style="isEdit ? 'display: flex' : 'display: none'">
      <view class="btn">取消关注</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isEdit: false,
    };
  },
  methods: {

  }
}
</script>

<style scoped lang="scss">
.page {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
}

.goods-tab {
  position: fixed;
  left: 0;
  top: 0;
  /* #ifdef H5 */
  top: 88rpx;
  /* #endif */
  z-index: 10;
  display: flex;
  align-items: center;
  width: 100%;
  height: 80rpx;
  background-color: #FFFFFF;

  .tab {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20%;
    height: 100%;

    text {
      display: flex;
      align-items: center;
      height: 100%;
      border-bottom: 4rpx solid transparent;
    }

    .action {
      color: $base;
      border-bottom: 4rpx solid $base;
    }
  }
}

.goods-list {
  padding: 0 4%;
  background-color: #FFFFFF;
  margin-top: 100rpx;
  padding-bottom: 80rpx;

  .check {
    display: flex;
    align-items: center;
    width: 60rpx;
    height: 100%;

    text {
      color: #C0C0C0;
      font-size: 34rpx;
    }
  }

  .list {
    display: flex;
    align-items: center;
    width: 100%;
    height: 260rpx;
    margin-bottom: 10rpx;

    .thumb {
      width: 40%;
      height: 240rpx;

      image {
        width: 240rpx;
        height: 240rpx;
        border-radius: 10rpx;
      }
    }

    .item {
      width: 60%;
      height: 100%;
      border-bottom: 2rpx solid #EEEEEE;

      .title {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100rpx;

        text {
          font-size: 26rpx;
          color: #222222;
        }
      }

      .price-more {
        display: flex;
        align-items: center;
        width: 100%;
        height: 80rpx;

        .price {
          font-size: 32rpx;
          color: $base;
          font-weight: bold;
        }

        .depreciate {
          display: flex;
          align-items: center;

          text {
            display: block;
            padding: 4rpx 8rpx;
            font-size: 24rpx;
            color: $base;
            border: 2rpx solid $base;
            border-radius: 20rpx;
            transform: scale(0.8);
          }
        }
      }

      .goods-btn {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 80rpx;

        .btn {
          display: flex;
          align-items: center;

          text {
            padding: 6rpx 14rpx;
            font-size: 24rpx;
            color: #555555;
            border: 2rpx solid #C0C0C0;
            border-radius: 60rpx;
            margin-right: 20rpx;
          }
        }

        .cart {
          display: flex;
          align-items: center;
          padding: 10rpx;
          border: 2rpx solid $base;
          border-radius: 100%;

          text {
            font-size: 34rpx;
            color: $base;
          }
        }
      }
    }
  }
}

/* 底部 */
.footer-btn {
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 80rpx;
  background-color: #FFFFFF;

  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90%;
    height: 70rpx;
    background: linear-gradient(to right, $base, $change-clor);
    border-radius: 70rpx;
    font-size: 28rpx;
    color: #FFFFFF;
  }
}</style>
