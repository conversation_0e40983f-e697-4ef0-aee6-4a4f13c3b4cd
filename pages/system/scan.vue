<template>
  <view class="jieguo" :style="{ background: colors }">
    <view class="place"> </view>
    <view class="jieguo_box" v-if="type == 1">
      <view class="success">
        <text class="iconfont iconduihao-yuan-F" style="color: #1afa2a"></text>
      </view>
      <view class="texts">授权成功</view>
      <view class="ewm" @tap="jumpUser">返回首页>>></view>
    </view>
    <view class="jieguo_box" v-if="type > 1">
      <view class="success">
        <text
          class="iconfont icongantanhao-yuankuang"
          style="color: #ff541e"
        ></text>
      </view>
      <view class="texts" v-if="type == 2">该二维码无效</view>
      <view class="texts" v-if="type == 3">扫描二维码出现错误</view>
      <view class="texts" v-if="type == 4">请扫描正确的二维码</view>
      <view class="texts" v-if="type == 5">{{ msg }}</view>
      <view class="ewm" @tap="jumpUser">返回首页>>></view>
    </view>
  </view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
      type: 0,
      msg: null,
    };
  },

  components: {},
  props: {},

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(params) {
    this.type = params.type;
    this.msg = params.msg;
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {},

  methods: {
    onsuccess() {
      uni.switchTab({
        url: "/pages/home/<USER>",
      });
    },

    jumpUser() {
      uni.switchTab({
        url: "/pages/home/<USER>",
      });
    },
    onOrder() {
      uni.navigateTo({
        url: `/pages/order/orderDetails?id=${this.orderNum}`,
      });
    },
  },
};
</script>
<style>
.jieguo {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  bottom: 0;
  padding: 0 8%;
  overflow: hidden;
}

.place {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: linear-gradient(
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.3),
    rgba(255, 255, 255, 0.5),
    #ffffff
  );
  z-index: 10;
}

.jieguo_box {
  width: 88%;

  padding: 20upx;
  z-index: 900;
  position: absolute;
  top: 150upx;
  left: 50%;
  transform: translateX(-50%);
}

.success {
  text-align: center;
  margin-top: 50upx;
}

.success text {
  font-size: 140upx;
}

.jieguo .texts,
.moneys {
  text-align: center;
  font-size: 32upx;
  margin-top: 20upx;
  font-weight: bold;
}

.wancheng {
  width: 80%;
  height: 80upx;
  border: 2upx solid #3e7e8b;
  color: #3e7e8b;
  text-align: center;
  line-height: 80upx;
  border-radius: 10upx;
  margin: 0 auto;
  margin-top: 150upx;
  font-weight: bold;
}

.ewm {
  margin-top: 20upx;
  height: 40upx;
  line-height: 40upx;
  text-align: center;
  font-size: 24upx;
  color: #999;
}
</style>
