<template>
	<view class="main">
		<view class="base-info">
			<view class="item">领料单号：{{ auditData.orderNum }}</view>
			<view class="item">领料时间：{{ auditData.orderTime }}</view>
			<view class="item">配送方式：{{ auditData.logisticsProvider.label || '' }}</view>
			<view class="item">订单状态：{{ getOrderStatusChinese(auditData.orderStatus) }}</view>
			<view class="item">
				支付金额（元）：
				<text style="color: #f37b1d">￥{{ auditData.actualAmount || '' }}</text>
			</view>
		</view>
		<view class="customer-main" style="width: 100%">
			<view class="wrapper">
				<view class="main_info" style="padding: 0">
					<view class="input-content" style="padding: 10rpx">
						<view class="top-box">
							<view class="tui-card">
								<view class="list-header">
									<view class="cell" style="flex: 1.5">OEM编号</view>
									<view class="cell" style="flex: 2">名称</view>
									<view class="cell" style="flex: 1">制造渠道</view>
									<view class="cell" style="flex: 0.6">数量</view>
									<view class="cell" style="flex: 1">金额</view>
								</view>
								<view class="list-list" v-for="item in infoList" :key="item.id">
									<view class="cell" style="flex: 1.5" @tap.stop="showContent(item.storageArticle.numberOem)">{{ item.storageArticle.numberOem || '' }}</view>
									<view class="cell" style="flex: 2" @tap.stop="showContent(item.storageArticle.name)">{{ item.storageArticle.name || '' }}</view>
									<view class="cell" style="flex: 1">{{ item.storageArticle.manufacturerChannel.label || '' }}</view>
									<view class="cell" style="flex: 0.6">{{ item.itemNum }}</view>
									<view class="cell" style="flex: 1">{{ item.payAmount }}</view>
								</view>
								<view v-if="infoList.length === 0">
									<rf-empty :info="'暂无数据'"></rf-empty>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 审核 -->
		<view class="close-account">
			<view class="account" v-if="type === 'audit'">
				<view class="btn-calculate" @click.stop="handleAudit('REFUSE')">
					<text>拒绝</text>
				</view>
				<view class="btn-calculate" @click.stop="handleAudit('APPROVE')">
					<text>通过审核</text>
				</view>
			</view>
			<view class="account" style="padding-right: 0" v-if="type === 'info'">
				<view class="btn-calculate" @click.stop="handleClose">
					<text>关闭</text>
				</view>
			</view>
		</view>
		<u-modal :show="show" :content="content" closeOnClickOverlay @confirm="show = false" @close="show = false"></u-modal>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
import { getCustomerMaterialAuditDetailApi, auditCustomerMaterialApi } from '@/api/custom.js';
export default {
	data() {
		return {
			code: '',
			loading: true,
			infoList: [],
			auditData: {},
			show: false,
			content: '',
			type: 'info'
		};
	},
	onLoad(options) {
		this.code = options.code;
		this.type = options.type;
		this.getbaseData();
	},
	methods: {
		getbaseData() {
			this.loading = true;
			getCustomerMaterialAuditDetailApi(this.code)
				.then((res) => {
					this.auditData = res.data.tradeOrder;
					this.infoList = this.auditData.tradeOrderDetailList || [];
				})
				.catch((err) => {
					this.$mHelper.toast(err || '系统出错啦，请稍后再试！');
				})
				.finally(() => {
					this.loading = false;
				});
		},
		handleAudit(status) {
			uni.showModal({
				title: '提示',
				content: status === 'APPROVE' ? '是否确认审核通过？' : '是否确认拒绝审核？',
				success: (res) => {
					if (res.confirm) {
						this.loading = true;
						const args = {
							id: this.auditData.id,
							auditStatus: status
						};
						auditCustomerMaterialApi(args)
							.then((res) => {
								const message = status === 'APPROVE' ? '审核通过' : '已驳回';
								this.$mHelper.toast(message);
								setTimeout(() => {
									uni.navigateBack();
								}, 300);
							})
							.catch((err) => {
								this.$mHelper.toast(err || '系统出错啦，请稍后再试！');
							})
							.finally(() => {
								this.loading = false;
							});
					}
				}
			});
		},
		getOrderStatusChinese(orderStatus) {
			let value = '';
			switch (orderStatus) {
				case 'CLOSED':
					value = '订单关闭';
					break;
				case 'PAID':
					value = '已支付';
					break;
				case 'SUCCESS':
					value = '交易成功';
					break;
				case 'WAIT_DELIVER':
					value = '待发货';
					break;
				case 'WAIT_RECEIVE':
					value = '待收货';
					break;
				case 'WAIT_AUDIT':
					value = '待审核';
					break;
			}
			return value;
		},
		handleClose() {
			uni.navigateBack();
		},
		showContent(content) {
			this.content = '';
			this.content = content;
			this.show = true;
		}
	}
};
</script>

<style scoped lang="scss">
.main {
	padding: 22rpx;
	.base-info {
		background-color: #fff;
		padding: 10rpx;
		border-radius: 12rpx;
		.item {
			padding: 10rpx 0;
		}
	}
}
/* 审核 */
.close-account {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 130rpx;
	background-color: #ffffff;
	border-top: 2rpx solid #f6f6f6;
	.account {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-right: 4%;
		margin-top: 20rpx;

		.btn-calculate {
			display: flex;
			justify-content: center;
			align-items: center;
			flex: 1;
			margin: 0 30rpx;
			height: 60rpx;
			background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
			border-radius: 60rpx;

			text {
				color: #ffffff;
				font-size: 24rpx;
			}
		}
	}
}
</style>
