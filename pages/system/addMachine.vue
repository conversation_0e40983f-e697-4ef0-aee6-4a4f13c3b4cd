<template>
	<view class="main">
		<form class="formTable">

			<uni-section class="pd_body" title="关联品牌" type="line">
				<input class="pd_input" type="text" v-model="parms2.name" @focus="show=true" @input="range2Fn"
					placeholder="请输入（如: C7780,V80,7502,C1070）">
				<uni-icons :type="show ? 'arrowup' : 'arrowdown'" class="pd_icon" color="#999" size="14"></uni-icons>

				<uni-icons v-if="parms2.name" type="close" size="17" class="close_icon" color="#999"
					@click.stop="clear"></uni-icons>

				</input>
				<view class="socrll-check" v-if="show">
					<view class="socrll-popper__arrow"></view>
					<scroll-view class="uni-select__selector-scroll" v-if="range2.length>0">
						<view class="socrll-list">
							<view class="socrll-item" v-for="(item,index) in range2" :key="index" @click="sureCheckFn(item)">
								{{item.brand}}/{{item.name}}
							</view>
						</view>

					</scroll-view>
					<view class="no-socrll-list" v-else>
						暂无该机型
					</view>

				</view>
			</uni-section>

			<uni-section title="设备组名称" type="line">
				<uni-data-select v-model="parms1.deviceGroup" :localdata="range1" :clear='false'></uni-data-select>
			</uni-section>
			<view class="input-totast">
				<view style="font-size: 28rpx;font-weight: bold;">*注：</view>
				<view class="totast-content">
					1.请务必准确登记你的机型，否则将导致商品推荐错误，导致给您带来维修。查询等服务的不便；<br>
					2.如果你不能很准确的知道你的机器型号，请在机器正前方，拍张清晰的照片发给客服，客服会帮你确认机型；<br>
					3.若随意填与机型，导致商品匹配错误，造成的商品退换货的物流费用，将由亲爱的贵客您承担。<br>
				</view>
			</view>
			<view class="service" @click="serviceFn">
				咨询客服
			</view>
		</form>
		<view class="footer">
			<button class="confirm-btn save" @click="saveFn(0)">取消</button>
			<button class="confirm-btn cancel" @click="saveFn(1)">保存</button>
		</view>
	</view>
</template>

<script>
	import {
		customerDeviceGoup,
		customerAllTree,
		customerModelPage
	} from "@/api/index";
	export default {
		data() {
			return {
				show: false,
				range1: [],
				range2: [],
				// 提交
				parms1: {
					createdAt: null, // 创建时间
					customerId: uni.getStorageSync('userInfo').customerId, //	客户id
					deleted: null, // 是否删除
					deviceCode: null, // 设备验证码
					deviceGroup: '', //	设备组名称(字典项码)
					deviceGroupImg: {
						key: null,
						name: null,
						url: null,
					},
					deviceOn: 1102, // 设备新旧(字典项码)
					deviceStatus: 901, // 设备状态(字典项码)
					fixStatus: 1501, // 维修状态(字典项码)
					id: null, // id
					productId: '', // 关联产品树id
					treatyType: 1201, // 合约类型(字典项码)
					updatedAt: null, // 更新时间
					usingStatus: 1001, // 使用状态(字典项码)
				},
				// 关联品牌
				parms2: {
					pageNumber: 1, // 页码
					pageSize: 9999, // 页大小
					desc: false, // 是否倒叙排序 true:倒叙(DESC) false:升序(ASC)	
					fullIdPath: null, // 品牌/产品树/系列id全路径（1、2、3级分组）
					name: null, // 	机型/机型全称
					orderBy: null, // 排序字段名称
				}
			}
		},
		onLoad() {
			// this.$mHelper.loadVerify();
		},
		onShow() {
			this.range1Fn()
			this.range2Fn()
		},
		created() {},
		methods: {
			// 获取设备
			range1Fn() {
				this.$http.get(`/api/customer-device-group/group-name-drop-down/${this.parms1.customerId}`).then(res => {
					res.data.forEach(item => {
						this.range1.push({
							value: item.value,
							text: item.label
						})
						this.parms1.deviceGroup = this.range1[0].value
					})
				})
			},
			// 获取管理品牌或输入
			range2Fn() {
				this.$http.get(customerModelPage, this.parms2).then(res => {
					this.range2 = res.data.rows
				})
			},
			// 客服咨询
			serviceFn() {
				wx.openCustomerServiceChat({
					extInfo: {
						url: "https://work.weixin.qq.com/kfid/kfca73cb4d1464a6a43",
					},
					corpId: "ww8f007820b431a55e",
					success(res) {},
				});
			},
			// 确认机型
			sureCheckFn(item) {
				this.parms1.productId = item.id;
				this.parms2.name = item.name;
				this.show = false
			},
			// 
			onchange(e) {
				this.parms1.productId = e.detail.value[e.detail.value.length - 1].value
			},
			// 提交 
			async saveFn(val) {
				let verify = await this.$http.post(this.$mHelper.verifyAccessToken);
				if (verify.data === 2) {
					this.$mHelper.toast("网络异常，请稍后再试...");
					this.$mHelper.relogin();
					return false;
				}
				if (val) {
					if (!this.parms1.productId) {
						uni.showToast({
							title: '请选择关联品牌',
							icon: 'none'
						})
						return
					} else if (!this.parms1.deviceGroup) {
						uni.showToast({
							title: '请选择设备名称',
							icon: 'none'
						})
						return
					}
					this.$http.post(customerDeviceGoup, this.parms1).then(res => {
						uni.showToast({
							title: '添加成功',
							icon: 'none'
						})
						this.toPageFn()
					}).catch(err => {
						uni.showToast({
							title: '请勿重复提交',
							icon: 'none'
						})
					})

				} else {
					this.toPageFn()
				}
			},
			toPageFn() {
				let route = '/pages/system/machine'
				this.$mRouter.push({
					route,
				});
			},
			clear() {
				// HM修改 收起键盘
				uni.hideKeyboard();
				this.parms2.name = '';
				this.show = false;
			},

		}
	}
</script>

<style lang="scss" scoped>
	.main {
		width: 100vw;
		height: 100vh;
		padding: 0 22rpx;
		box-sizing: border-box;
		background-color: #fff;

		.input-totast {
			width: 100%;
			color: #e5452f;
			display: flex;

			.totast-content {
				flex: 1;
				font-size: 28rpx !important;
			}
		}

		.pd_body {
			width: 100%;
			// position: relative;

			.pd_input {
				font-size: 14px !important;
				border: 1px solid #e5e5e5 !important;
				box-sizing: border-box !important;
				border-radius: 4px !important;
				height: 80rpx !important;
				padding: 0 22rpx !important;
			}

			.socrll-check {
				width: 100%;
				position: absolute;
				top: 100rpx;
				left: 0;
				z-index: 3;
				padding: 0 22rpx;

				/deep/.uni-select__selector-scroll {
					width: 100%;
					height: 210px;
					padding: 4px 22rpx !important;
					box-sizing: border-box;
					background-color: #FFFFFF;
					border: 1px solid #EBEEF5;
					border-radius: 6px;
					box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);


					.socrll-list {
						height: 210px;
						overflow: hidden;
						overflow-y: scroll;
					}

					.socrll-item {
						display: flex;
						cursor: pointer;
						line-height: 35px;
						font-size: 14px;
						text-align: center;
					}
				}

				.no-socrll-list {
					height: 45px;
					padding: 4px 22rpx !important;
					box-sizing: border-box;
					background-color: #FFFFFF;
					border: 1px solid #EBEEF5;
					border-radius: 6px;
					box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
					overflow: hidden;
					overflow-y: scroll;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 28rpx;
					color: #999;
				}

				.socrll-popper__arrow {
					filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
					position: absolute;
					top: -4px;
					left: 15%;
					margin-right: 3px;
					border-top-width: 0;
					border: 1px solid #EBEEF5;
					border-bottom-color: #FFFFFF;
					border-right-color: #FFFFFF;
					width: 18rpx;
					height: 18rpx;
					background-color: #FFFFFF;
					transform: rotate(45deg);
				}
			}
		}

		.service {
			width: 50rpx;
			min-height: 80rpx;
			position: fixed;
			right: 0;
			bottom: 200rpx;
			text-align: center;
			border-radius: 8rpx 0 0 8rpx;
			float: right;
			border: 2rpx solid #e5452f;
			border-right: 0;
			color: #e5452f;
		}

		.footer {
			width: 100%;
			display: flex;
			align-items: center;
			position: fixed;
			bottom: 22px;
			left: 0;

			.confirm-btn {
				width: 40%;
				height: 75rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border: none !important;
				color: #fff;
				border-radius: 37px;
			}

			.save {
				background: linear-gradient(90deg, #F5C744 0%, #EE822F 100%);
			}

			.cancel {
				background: linear-gradient(90deg, #E5452F 0%, #EE822F 100%);
			}
		}

	}

	/deep/.uni-select {
		height: 80rpx !important;
		padding: 0 22rpx !important;
	}

	/deep/.line {
		background: linear-gradient(to bottom, #E5452F 0%, #EE822F 100%) !important;
	}

	/deep/.uni-data-pickerview {
		.item {
			justify-content: center;
		}
	}

	/deep/.uni-section-content {
		height: 80rpx !important;
		padding: 0 22rpx !important;
		margin-bottom: 22rpx;

	}

	/deep/.uni-section__content-title {
		color: #000 !important;
		font-size: 32rpx !important;
	}

	/deep/.uni-input {
		height: 80rpx !important;
		line-height: 40rpx !important;
		border: 1rpx solid #e5e5e5;
		padding: 15rpx;
		border-radius: 10rpx;

	}

	/deep/.uni-select__input-placeholder {
		font-size: 14px !important;
		color: #999 !important;
	}

	/deep/.input-value {
		padding: 0 14rpx 0 22rpx !important;
		height: 80rpx;
	}

	/deep/.selected-area {
		font-size: 14px !important;
		color: #999 !important;
	}

	/deep/.selected-item-active {
		border-bottom: 2px solid #E5452F;
	}

	/deep/.selected-item {
		margin: 0 6rpx !important;
		padding: 8rpx 0 !important;
	}

	/deep/.check {
		border-color: #e5452f !important;
		margin-left: 6px;
		margin-right: 0;
	}

	.pd_body {
		/deep/.uni-section-content {
			position: relative;

			.pd_icon {
				position: absolute;
				right: 44rpx;
				top: 20rpx;
			}

			.close_icon {
				position: absolute;
				right: 88rpx;
				top: 16rpx;
				z-index: 10000;
			}
		}
	}

	.icons {
		font-family: iconfont;
		font-size: 32upx;
		font-style: normal;
		color: #999;
	}
</style>