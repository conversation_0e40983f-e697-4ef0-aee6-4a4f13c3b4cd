<template>
	<view class="set">
		<!--  @tap="
	  item.title!=='个人资料'?navTo(item.url):userInfo.staff ? navTo('/pages/user/agentinfo') : navTo('/pages/user/userinfo')
	  " -->
		<view
			class="list-cell b-b"
			:class="{ 'm-t': item.class === 'mT' }"
			v-for="item in setList"
			:key="item.title"
			v-show="item.title == '个人资料' ? (userInfo.staff ? true : false) : true"
			@tap="navTo(item.url)"
			hover-class="cell-hover"
			:hover-stay-time="50"
		>
			<text class="cell-tit">{{ item.title }}</text>
			<text class="cell-tip">{{ item.content }}</text>
			<!-- <text class="cell-more iconfont iconyou"></text> -->
		</view>
		<view class="list-cell log-out-btn" :class="'text-' + themeColor.name" @tap="toLogout">
			<text class="cell-tit">退出登录</text>
		</view>
	</view>
</template>
<script>
import { logout } from '@/api/login';
export default {
	data() {
		return {
			isVersionUpgradeShow: false,
			loadProgress: 0,
			CustomBar: this.CustomBar,
			setList: this.$mConstDataConfig.setList,
			styleUserIsOpen: this.$mSettingConfig.styleUserIsOpen,
			notifyChecked: false,
			isNewVersion: false,
			colorModal: false,
			userInfo: {}
		};
	},
	onLoad() {
		// this.$mHelper.loadVerify();
		this.initData();

		// #ifdef APP-PLUS
		if (uni.getSystemInfoSync().platform === 'ios') {
			this.CustomBar = 0;
		}
		// #endif
	},
	methods: {
		// 初始化数据
		initData() {
			if (uni.getStorageSync('userInfo')) {
				this.userInfo = uni.getStorageSync('userInfo');
			}
			// 缓存大小
			// this.setList[5].content = `${uni.getStorageInfoSync().currentSize} kb`;
			// #ifdef APP-PLUS
			// eslint-disable-next-line
			// this.setList[8].content = `当前版本 ${plus.runtime.version}`;
			// #endif
		},
		// 通用跳转
		navTo(route) {
			if (!route) return;
			if (route === 'clearCache') {
				uni.showModal({
					content: '确定要清除缓存吗',
					success: (e) => {
						if (e.confirm) {
							uni.clearStorageSync();
							this.setList[5].content = '0 kb';
							this.$mStore.commit('login', this.userInfo);
							this.$mHelper.toast('清除缓存成功');
						}
					}
				});
				return;
			} else if (route === 'versionUpgrade') {
				this.isVersionUpgradeShow = true;
				if (this.isNewVersion) {
					this.$mHelper.toast('已经是最新版本');
				}
				return;
			}
			this.$mRouter.push({
				route
			});
		},
		// 退出登录
		toLogout() {
			uni.showModal({
				content: '确定要退出登录么',
				success: (e) => {
					if (e.confirm) {
						this.$http
							.put(logout)
							.then(async (res) => {
								console.log(res);
								this.btnLoading = false;
								if (res) {
									this.$mStore.commit('logout');
									uni.reLaunch({
										url: '/pages/home/<USER>'
									});
								} else {
									this.$mHelper.toast('注销失败！');
								}
							})
							.catch((err) => {
								console.log(err);
							});
					}
				}
			});
		},
		showColorModal() {
			this.colorModal = true;
		},
		SetColor(item) {
			this.colorModal = false;
			this.themeColor = item;
			this.$mStore.commit('setThemeColor', item);
		}
	}
};
</script>
<style lang="scss">
page {
	background: $page-color-base;
}

.set {
	padding: $spacing-base 0;
}

.cu-list.card-menu {
	margin: $spacing-base 0;

	.title {
		margin-left: $spacing-base;
	}
}
</style>
