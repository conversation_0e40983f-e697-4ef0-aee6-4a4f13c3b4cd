<template>
	<view class="main">
		<view class="box1">
			<FormQuery :form-columns="formColumns" @search="search" />
		</view>

		<view class="box2">
			<!-- 购物车列表 -->
			<!-- <mescroll-body ref="mescrollRef" @down="downCallback" @up="upCallback" :down="downOption" :up="upOption" :top="0"> -->
			<view class="cart-list" v-if="goodsList.length > 0">
				<view class="list" v-for="good in goodsList" :key="good.id">
					<!-- good.id, good.SkuNum -->
					<view class="check" @click="checkGood(good.choosesku.id, good.SkuNum)">
						<!-- 'active iconfont icon-duihao': checkList.indexOf(good.id) !== -1 -->
						<text
							class="check-icon"
							:class="{
								'active iconfont icon-duihao': isCheck(good.choosesku.id)
							}"
						></text>
					</view>
					<view class="goods">
						<view class="thumb" @tap.stop="previewImage(good.picsUrl[0].url)">
							<image :src="good.picsUrl[0].url" mode=""></image>
						</view>
						<view class="item">
							<view class="title">
								<text class="two-omit">{{ good.name }}</text>
							</view>
							<view class="oem">
								<text class="oem-item">OEM编号：{{ good.choosesku.invSkuOem }}</text>
							</view>
							<view class="attribute" @click="getGoodsAttr(good)">
								<view class="attr">
									<!-- <text >{{
                    good.choosesku.saleAttrVals.map(
                      (item) => item.name + ":" + item.val
                    )
                  }}</text> -->
									<text>{{ good.saleAttrValsStr }}</text>
									<text class="more"></text>
								</view>
							</view>
							<view class="price-num">
								<view class="price">
									<text class="min">￥</text>
									<text class="max">{{ good.choosesku.saleUnitPrice }}</text>
								</view>
								<view class="num" @tap.stop>
									<text class="add" @tap.stop="changeBuyNumber(good, 'sub')">
										<text class="iconfont icon-subtract"></text>
									</text>
									<view class="number">
										<text>{{ good.SkuNum }}</text>
										<!-- <input v-model="good.SkuNum" type="number" maxlength="8" /> -->
									</view>
									<text class="add" @tap.stop="changeBuyNumber(good, 'add')">
										<text class="iconfont icon-add" :class="{ 'icon-disable': good.SkuNum === good.choosesku.availableNum }"></text>
									</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view v-if="goodsList.length === 0" style="padding-top: 200rpx">
				<rf-empty :info="'暂未查询到耗材商品哦'"></rf-empty>
			</view>
			<view class="loading" v-if="!isEnloading && goodsList.length">{{ isloading ? '加载中...' : goodsList.length < total ? '上拉加载更多' : '' }}</view>
			<view class="loading" v-if="goodsList.length > 0 && goodsList.length == total && isEnloading">—— 到底啦 ——</view>

			<!-- 结算 -->
			<view class="close-account">
				<view class="account">
					<view class="btn-calculate" @click="hisApply">
						<text>申领记录</text>
					</view>
					<!-- <view class="btn-calculate" @click="subApply">
						<text>确认申领({{ totalCount }})</text>
					</view> -->
					<view class="btn-calculate" @click="addInentory">
						<text>加入清单({{ totalCount }})</text>
					</view>
				</view>
			</view>
		</view>
		<goods-attr
			ref="GoodsAttr"
			:allSaleAttr="allSaleAttr"
			:sku="sku"
			@choose="handleAttrChoose"
			@addCart="addCart"
			:attrMap="attrMap"
			:skuMap="skuMap"
			:tempSku="tempSku"
		></goods-attr>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
import { brandSerialList, getFilterData, consumablesList, applyWare, brandModelList, classifyList } from '@/api/index';
import GoodsAttr from './components/GoodsAttr.vue';
import FormQuery from '@/components/formQuery/index';
export default {
	components: {
		GoodsAttr,
		FormQuery
	},
	data() {
		return {
			// 是否到底
			isEnloading: false,
			// 分页参数
			pageNumber: 1,
			// 总数
			total: 0,
			// 是否加载完成
			isloading: true,
			searchparms: {},
			productList: [],
			categoryList: [],
			categoryListId: '',
			classList: [],
			categoryId: '',
			unitList: [],
			unitListId: '',
			sku: [],
			allSaleAttr: {},
			attrMap: {},
			skuMap: [],
			loading: true,
			tempSku: {},
			currentChoose: {},
			// -------
			mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
			// 下拉刷新的配置(可选, 绝大部分情况无需配置)
			downOption: {},
			// 上拉加载的配置(可选, 绝大部分情况无需配置)
			upOption: {
				use: false,
				toTop: {
					src: ''
				}
			},
			goodsList: [],
			pagination: {
				pageNumber: 1,
				pageSize: 10,
				total: 0
			},
			checkList: [],
			totalPrice: 0,
			// 申领总数
			totalCount: 0,
			isSuccess: false,
			// 图片预览
			previewList: [],
			formColumns: [
				{
					dataIndex: 'product',
					title: '适用机型',
					valueType: 'machine'
				},
				{
					dataIndex: 'categoryId',
					title: '物品分类',
					valueType: 'select',
					localdata: []
				},
				{
					dataIndex: 'categoryListId',
					title: '零件分类',
					valueType: 'select',
					localdata: []
				},
				{
					dataIndex: 'unit',
					title: '所属单元',
					valueType: 'select',
					localdata: []
				},
				{
					dataIndex: 'oem',
					title: 'OEM编号',
					valueType: 'input'
				}
			]
		};
	},
	// 滚动到底部
	onReachBottom() {
		if ((this.pageNumber - 1) * 10 >= this.total) {
			this.isEnloading = true;
			return;
		}
		this.saveFn(); // 商品列表
	},
	onLoad() {
		this.getDataFn();
	},
	onShow() {},
	created() {},
	methods: {
		search(value) {
			this.searchparms = value;
			this.saveFn(1);
		},
		previewImage(url) {
			this.previewList = [];
			this.previewList.push(url);
			uni.previewImage({
				urls: this.previewList,
				current: 0
			});
		},
		getDataFn() {
			this.classList = []; // 商品分类
			this.categoryList = []; // 零件分类
			this.goodsList = [];
			this.unitList = []; // 所属单元
			this.loading = true;
			// 商品分类
			this.$http.get(classifyList, { pageNumber: 1, pageSize: 9999 }).then((res) => {
				// this.categoryId = this.categoryId ? this.categoryId : res?.data?.rows[0].id;
				res.data.rows.map((el) => {
					this.classList.push({
						value: el.id,
						text: el.name
					});
				});
			});
			this.$http.get(getFilterData).then((res) => {
				res.data.tagList.map((el) => {
					if (el.name == '零件分类') {
						this.categoryListId = el.id;
						el.value.map((a) => {
							this.categoryList.push({
								value: a,
								text: a
							});
						});
					}
					if (el.name == '所属单元') {
						this.unitListId = el.id;
						el.value.map((b) => {
							this.unitList.push({
								value: b,
								text: b
							});
						});
					}
				});
				this.loading = false;
			});
			this.formColumns.forEach((item, index) => {
				if (item.dataIndex === 'categoryId') {
					this.formColumns[index].localdata = this.classList;
				}
				if (item.dataIndex === 'categoryListId') {
					this.formColumns[index].localdata = this.categoryList;
				}
				if (item.dataIndex === 'unit') {
					this.formColumns[index].localdata = this.unitList;
				}
			});
		},

		getGoodsAttr(data) {
			this.sku = data.skuList;
			this.allSaleAttr = {
				...this.handleSkuAttr(data.skuList)
			};
			this.init(data);
			setTimeout(() => {
				this.$refs['GoodsAttr'].show(2, data.choosesku.id);
			}, 300);
		},
		init(data) {
			const allSaleAttr = [...Object.keys(this.allSaleAttr).map((item) => this.allSaleAttr[item])].flat();
			const primeList = this.getPrime(allSaleAttr.length);
			let index = 0;
			const attrMap = {};
			Object.keys(this.allSaleAttr).forEach((key) => {
				this.allSaleAttr[key].forEach((item) => {
					attrMap[key + '@' + item] = primeList[index];
					index++;
				});
			});
			this.attrMap = {
				...attrMap
			};
			const skuMap = [];
			const tempInfo = {
				availableNum: [this.sku[0].availableNum, this.sku[0].availableNum],
				saleUnitPrice: data.miniPrice,
				picsUrl: data.picsUrl[0]
			};
			this.sku.forEach((item) => {
				if (item.availableNum < tempInfo.availableNum[0]) tempInfo.availableNum[0] = item.availableNum;
				if (item.availableNum > tempInfo.availableNum[1]) tempInfo.availableNum[1] = item.availableNum;
				const target = item.saleAttrVals;
				let result = 1;
				target.forEach((item) => {
					result *= attrMap[item.name + '@' + item.val];
				});
				skuMap.push(result);
			});
			this.skuMap = skuMap;
			this.tempSku = tempInfo;
		},
		handleSkuAttr(skuList) {
			const skuAttr = [];
			skuList.forEach((item) => {
				skuAttr.push(...item.saleAttrVals);
			});
			const result = [];
			skuAttr.forEach((item) => {
				if (result[item.name]) {
					result[item.name].indexOf(item.val) === -1 && result[item.name].push(item.val);
				} else {
					result[item.name] = [item.val];
				}
			});
			return result;
		},
		getPrime(num) {
			const isPrime = (number) => {
				const target = number / 2;
				if (target === 2) return false;
				for (let index = 2; index < target; index++) {
					if (number % index === 0) return false;
				}
				return true;
			};
			const arr = [];
			for (let i = 2; arr.length < num; i++) {
				if (isPrime(i)) arr.push(i);
			}
			return arr;
		},
		addCart() {
			if (JSON.stringify(this.currentChoose) == '{}' || !this.currentChoose) {
				uni.showToast({
					title: '请选择商品',
					icon: 'error'
				});
				return;
			}
			let goodsList = JSON.parse(JSON.stringify(this.goodsList));
			goodsList.map((ele) => {
				if (ele.id == this.currentChoose.sku.itemId) {
					let saleAttrValsStr = this.currentChoose.sku.saleAttrVals.map((item) => item.val);
					ele.saleAttrValsStr = saleAttrValsStr.join(',');
					ele.choosesku = this.currentChoose.sku;
				}
			});
			// 当前修改的sku属性已被选中，将选中的数量赋给itemId的SkuNum
			const index = this.checkList.findIndex((item) => item.id === this.currentChoose.sku.id);
			if (index !== -1) {
				goodsList.forEach((item, i) => {
					if (item.id === this.currentChoose.sku.itemId) {
						goodsList[i].SkuNum = this.checkList[index].SkuNum;
					}
				});
			}
			setTimeout(() => {
				this.$refs['GoodsAttr'].hide();
				this.goodsList = JSON.parse(JSON.stringify(goodsList));
				this.$forceUpdate();
			}, 300);
		},

		handleAttrChoose(val) {
			this.currentChoose = val;
		},
		// 查询
		async saveFn(type) {
			if (type) {
				this.goodsList = [];
				this.pageNumber = 1;
			}
			this.isloading = true;
			let tags = [];
			if (this.searchparms.categoryListId) {
				tags.push({
					tagId: this.categoryListId,
					values: [this.searchparms.categoryListId]
				});
			}

			if (this.searchparms.unit) {
				tags.push({
					tagId: this.unitListId,
					values: [this.searchparms.unit]
				});
			}
			let verify = await this.$http.post(consumablesList, {
				categoryId: this.searchparms.categoryId,
				oem: this.searchparms.oem,
				productTreeIdList: this.searchparms.productTreeIdList,
				tags: tags,
				pageNumber: this.pageNumber,
				pageSize: 10
			});
			console.log(verify);
			this.pageNumber++;
			this.isloading = false;
			this.total = verify.data.total;
			verify.data.rows.map((el) => {
				el.SkuNum = 1;
				if (el.skuList[0] && el.skuList[0].saleAttrVals) {
					let saleAttrValsStr = el.skuList[0].saleAttrVals.map((item) => item.val);
					el.saleAttrValsStr = saleAttrValsStr.join(',');
				}

				el.choosesku = el.skuList[0];
			});
			this.goodsList = this.goodsList.concat(verify.data.rows);
		},

		// 加入领料清单
		addInentory() {
			if (this.checkList.length === 0) {
				this.$mHelper.toast('请选择商品');
				return;
			}
			let params = JSON.stringify(this.checkList);
			uni.navigateTo({
				url: `/pages/system/receiveInventory?params=${params}`
			});
		},
		// 确认申领
		subApply() {
			if (this.checkList.length === 0) return;
			const params = this.checkList.map((item) => {
				const target = this.goodsList.find((good) => good.id === item);
				return target;
			});
			let detailList = [];
			params.map((ele) => {
				detailList.push({
					num: ele.SkuNum,
					skuId: ele.choosesku.id
				});
			});
			this.$http.post(applyWare, { detailList: detailList }).then((res) => {
				if (res.code === 200) {
					this.isSuccess = true;
					this.$mHelper.toast('申领耗材成功，请自行前往仓库提货');
					// uni.removeStorageSync("checkList");
					// setTimeout(() => {
					//   this.$mRouter.reLaunch({ route: "/pages/home/<USER>" });
					// }, 1000);
					// uni.switchTab({
					//   url: "/pages/home/<USER>",
					// });
				}
			});
			// uni.setStorageSync("checkList", params);
			// uni.navigateTo({
			// 	url: "/pages/system/wareconfirm",
			// });
		},
		hisApply() {
			uni.navigateTo({
				url: '/pages/system/warehistory'
			});
		},
		async changeBuyNumber(data, type) {
			if (type == 'add') {
				if (data.SkuNum === data.choosesku.availableNum) return;
				data.SkuNum = data.SkuNum + 1;
			} else if (type == 'sub') {
				if (data.SkuNum > 1) {
					data.SkuNum = data.SkuNum - 1;
				}
			}
			// 查找并更新checkList中与data.id匹配的条目
			const foundIndex = this.checkList.findIndex((item) => item.id === data.choosesku.id);
			if (foundIndex !== -1) {
				this.checkList[foundIndex].SkuNum = data.SkuNum;
			}
			this.calcTotalCount();
		},
		isCheck(goodId) {
			for (let i = 0; i < this.checkList.length; i++) {
				if (this.checkList[i].id === goodId) {
					return true; // 找到匹配的id，返回false
				}
			}
			return false; // 没有找到匹配的id，返回true
		},
		checkGood(goodId, skuNum) {
			// console.log(goodId);
			let found = false;
			for (let i = 0; i < this.checkList.length; i++) {
				if (this.checkList[i].id === goodId) {
					found = true;
					this.checkList.splice(i, 1);
					break;
				}
			}
			if (!found) {
				this.checkList.push({
					id: goodId,
					SkuNum: skuNum
				});
			}
			this.calcTotalCount();
			// this.checkList = this.goodsList.forEach((item) => {
			// 	if (item.id !== goodId) {
			// 		return {
			// 			id: goodId,
			// 			SkuNum: item.SkuNum
			// 		};
			// 	}
			// });
			// const index = this.checkList.indexOf(goodId);
			// if (index === -1) {
			// 	this.checkList.push({
			// 		id: goodId,
			// 		SkuNum: skuNum
			// 	});
			// }
			//  else {
			// 	this.checkList.splice(index, 1);
			// }
			// this.checkList = [...this.checkList];
			// this.calcTotalCount();
		},
		// 计算总数
		calcTotalCount() {
			if (this.checkList.length === 0) {
				this.totalCount = 0;
				return;
			}
			const matchedGoods = this.goodsList.filter((good) => this.checkList.some((item) => item.id === good.id));
			const unmatchedItems = this.checkList.filter((item) => !matchedGoods.some((matchedGood) => matchedGood.id === item.id));
			const itemsWithSkuNum = [...matchedGoods, ...unmatchedItems];
			this.totalCount = itemsWithSkuNum.reduce((total, item) => total + item.SkuNum, 0);
		}
		// calcTotalCount() {
		// 	if (this.checkList.length === 0) {
		// 		this.totalCount = 0;
		// 		return;
		// 	}
		// 	const params = this.checkList.map((item) => {
		// 		const target = this.goodsList.find((good) => good.id === item.id);
		// 		if (target) {
		// 			return target;
		// 		} else {
		// 			return item;
		// 		}
		// 	});
		// 	this.totalCount = params.map((item) => item.SkuNum).reduce((a, b) => a + b);
		// }
	}
};
</script>

<style lang="scss" scoped>
// .box1 {
// 	position: fixed;
// 	width: 100%;
// 	top: 0;
// 	left: 0;
// 	right: 0;
// 	background: #fff;
// 	z-index: 9;
// 	padding: 20rpx 22rpx !important;
// 	border-bottom: 5rpx solid #f5f5f5;
// }

// .box2 {
// 	margin-top: 620rpx;
// }

.main {
	width: 100vw;
	height: 100vh;
	padding: 0 22rpx;
	box-sizing: border-box;
	background-color: #fff;

	.input-totast {
		width: 100%;
		color: #e5452f;
		display: flex;

		.totast-content {
			flex: 1;
			font-size: 28rpx !important;
		}
	}

	.pd_body {
		width: 100%;
		// position: relative;

		.pd_input {
			width: 80%;
			font-size: 14px !important;
			border: 1px solid #e5e5e5 !important;
			box-sizing: border-box !important;
			border-radius: 4px !important;
			height: 80rpx !important;
			padding: 0 22rpx !important;
		}
	}

	.service {
		width: 50rpx;
		min-height: 80rpx;
		position: fixed;
		right: 0;
		bottom: 200rpx;
		text-align: center;
		border-radius: 8rpx 0 0 8rpx;
		float: right;
		border: 2rpx solid #e5452f;
		border-right: 0;
		color: #e5452f;
	}

	.footer {
		width: 50%;
		display: flex;
		align-items: center;
		margin: auto;

		.confirm-btn {
			width: 50%;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border: none !important;
			color: #fff;
			border-radius: 37px;
			font-size: 28rpx;
			margin: 0;
		}

		.save {
			background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
		}

		.cancel {
			margin-left: 70rpx;
			background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
		}
	}
}

/deep/.uni-select {
	height: 80rpx !important;
	padding: 0 22rpx !important;
}

/deep/.line {
	display: none;
}

/deep/.uni-data-pickerview {
	.item {
		justify-content: center;
	}
}

/deep/.uni-section-content {
	height: 80rpx !important;
	padding: 0 22rpx !important;
	padding-right: 0rpx !important;
	margin-bottom: 22rpx;
}

/deep/.uni-section__content-title {
	font-size: 26rpx !important;
}

/deep/.uni-input {
	height: 80rpx !important;
	line-height: 40rpx !important;
	border: 1rpx solid #e5e5e5;
	padding: 15rpx;
	border-radius: 10rpx;
}

/deep/.uni-select__input-placeholder {
	font-size: 14px !important;
	color: #999 !important;
}

/deep/.input-value {
	padding: 0 14rpx 0 22rpx !important;
	height: 80rpx;
}

/deep/.selected-area {
	font-size: 14px !important;
	color: #999 !important;
}

/deep/.selected-item-active {
	border-bottom: 2px solid #e5452f;
}

/deep/.selected-item {
	margin: 0 6rpx !important;
	padding: 8rpx 0 !important;
}

/deep/.check {
	border-color: #e5452f !important;
	margin-left: 6px;
	margin-right: 0;
}

.pd-content {
	width: 100%;

	/deep/.uni-section-content {
		display: flex;
		padding-left: 0 !important;
		padding-right: 8rpx !important;
		border-radius: 4rpx;

		.input-content {
			width: 100%;
			display: flex;
			align-items: center;
			padding: 0 22rpx !important;
			border: 1px solid #e5e5e5 !important;
			box-sizing: border-box;
			position: relative;

			.pd_inputs {
				border: none !important;
				flex: 1 !important;
			}

			.close_icon {
				z-index: 10000;
				width: 60rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.socrll-check {
				width: 100%;
				position: absolute;
				top: 90rpx;
				left: 0;
				z-index: 3;

				.uni-select__selector-scroll {
					width: 100%;
					height: 210px;
					padding: 4px 22rpx !important;
					box-sizing: border-box;
					background-color: #ffffff;
					border: 1px solid #ebeef5;
					border-radius: 6px;
					box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

					.socrll-list {
						height: 210px;
						overflow: hidden;
						overflow-y: scroll;
					}

					.socrll-item {
						display: flex;
						cursor: pointer;
						line-height: 35px;
						font-size: 14px;
						text-align: center;
					}
				}

				.no-socrll-list {
					height: 45px;
					padding: 4px 22rpx !important;
					box-sizing: border-box;
					background-color: #ffffff;
					border: 1px solid #ebeef5;
					border-radius: 6px;
					box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
					overflow: hidden;
					overflow-y: scroll;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 28rpx;
					color: #999;
				}

				.socrll-popper__arrow {
					filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
					position: absolute;
					top: -4px;
					left: 23%;
					margin-right: 3px;
					border-top-width: 0;
					border: 1px solid #ebeef5;
					border-bottom-color: #ffffff;
					border-right-color: #ffffff;
					width: 18rpx;
					height: 18rpx;
					background-color: #ffffff;
					transform: rotate(45deg);
				}
			}
		}
	}
}

.icons {
	font-family: iconfont;
	font-size: 32upx;
	font-style: normal;
	color: #999;
}

/deep/.uni-section-header {
	width: 150rpx !important;
	float: left;
	clear: both;
}
</style>
<style scoped lang="scss">
.page {
	// padding-bottom: 200rpx;
	background: #f5f6f8;
	// min-height: 100vh;
	min-height: 100vh;
}

.head {
	position: fixed;
	left: 0;
	top: 0;
	z-index: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 80rpx;

	background-color: #ffffff;

	.title {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;
		font-size: 30rpx;
		color: #212121;
	}

	.edit {
		position: absolute;
		right: 20rpx;
		top: 50%;
		transform: translate(0, -50%);
		background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
		padding: 8rpx 20rpx;
		border-radius: 60px;

		text {
			color: #fff;
			font-size: 26rpx;
		}
	}
}

/* 购物车列表 */
.cart-list {
	width: 100%;
	padding: 20rpx 0;
	// margin-top: 100rpx;

	.list {
		display: flex;
		height: 210rpx;
		background-color: #ffffff;
		box-shadow: 0 0 20rpx #f6f6f6;
		border-radius: 10rpx;

		.check {
			display: flex;
			align-items: center;
			width: 10%;
			height: 80%;
			margin-left: 20rpx;

			// background-color: #fff;

			text {
				font-size: 36rpx;
				color: #333333;
			}

			.icon-checked {
				color: #fe3b0f;
				// box-shadow: 0 0 10rpx  #fe3b0f;
			}
		}

		.goods {
			display: flex;
			align-items: center;
			width: 90%;
			height: 100%;

			.thumb {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 30%;
				height: 100%;

				image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 10rpx;
				}
			}

			.item {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				padding: 10rpx 0;
				width: 70%;
				height: 100%;

				.title {
					display: flex;
					align-items: center;
					width: 100%;
					height: auto;

					text {
						font-size: 26rpx;
						color: #212121;
					}
				}
				.oem {
					text {
						font-size: 24rpx;
						color: #212121;
					}
				}
				.attribute {
					display: flex;
					align-items: center;
					margin-top: 10rpx;

					.attr {
						display: flex;
						align-items: center;
						padding: 0 20rpx;
						height: 40rpx;
						background-color: #f6f6f6;
						border-radius: 10rpx;

						text {
							font-size: 24rpx;
							color: #333333;
						}

						.more {
							display: flex;
							width: 10rpx;
							height: 10rpx;
							border-left: 2rpx solid #333333;
							border-bottom: 2rpx solid #333333;
							transform: rotate(-45deg);
							margin-left: 10rpx;
						}
					}
				}

				.price-num {
					display: flex;
					align-items: center;
					justify-content: space-between;
					height: 45rpx;

					.icon-disable {
						&::after,
						&::before {
							background-color: #ccc;
						}
					}

					.price {
						display: flex;

						.min {
							color: #fe3b0f;
							font-size: 24rpx;
						}

						.max {
							font-size: 28rpx;
							color: #fe3b0f;
							font-weight: bold;
						}
					}

					.num {
						display: flex;
						height: 40rpx;
						margin-right: 20rpx;

						.add {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 60rpx;
							height: 40rpx;
							background-color: #ffffff;

							text {
								color: #212121;
								font-size: 24rpx;
							}
						}

						.number {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 80rpx;
							height: 40rpx;
							background-color: #f6f6f6;
							border-radius: 8rpx;
							text-align: center;

							text {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
				}
			}
		}
	}
}

.background {
	background: #f5f6f8;
}

/* 购物车失效商品列表 */
.lose-efficacy-list {
	width: 100%;
	background-color: #ffffff;
	padding: 0 30rpx;
	margin-top: 30rpx;
	border-radius: 10rpx;
	overflow: hidden;

	.lose-efficacy-title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		height: 80rpx;

		.title {
			display: flex;
			align-items: center;
			height: 100%;

			text {
				font-size: 28rpx;
				color: #222222;
			}
		}

		.empty {
			display: flex;
			align-items: center;
			height: 100%;

			text {
				font-size: 26rpx;
				color: #fe3b0f;
			}
		}
	}

	.list {
		display: flex;
		align-items: center;
		width: 100%;
		height: 185rpx;
		border-bottom: 1px solid #f6f6f6;

		.tag {
			display: flex;
			align-items: center;
			width: 10%;
			height: 100%;

			text {
				padding: 4rpx 10rpx;
				font-size: 24rpx;
				color: #ffffff;
				background-color: rgba(0, 0, 0, 0.3);
				border-radius: 20rpx;
			}
		}

		.goods {
			display: flex;
			align-items: center;
			width: 90%;
			height: 100%;
			background-color: #ffffff;
			border-radius: 10rpx;

			.pictrue {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 30%;
				height: 100%;

				image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 10rpx;
				}
			}

			.item {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				width: 70%;
				height: 160rpx;

				.title {
					width: 100%;

					text {
						font-size: 28rpx;
						color: #999999;
					}
				}

				.explain {
					display: flex;
					align-items: center;

					text {
						font-size: 24rpx;
						color: #222222;
					}
				}
			}
		}
	}
}

/* 为你推荐 */
.recommend-info {
	width: 100%;
	background-color: #f2f2f2;
	cursor: pointer;

	.recommend-title {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;

		.title {
			display: flex;
			align-items: center;

			image {
				width: 416rpx;
				height: 40rpx;
			}
		}
	}

	.goods-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 0 30rpx;

		.list {
			width: 49%;
			height: 540rpx;
			margin-bottom: 20rpx;
			background-color: #ffffff;
			border-radius: 10rpx;
			overflow: hidden;

			.pictrue {
				display: flex;
				justify-content: center;
				width: 100%;

				image {
					height: 350rpx;
				}
			}

			.title-tag {
				// display: flex;
				height: 100rpx;
				padding: 20rpx;

				.tag {
					float: left;
					margin-right: 10rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					white-space: normal;
					font-size: 26rpx;
					line-height: 40rpx;

					text {
						font-size: 24rpx;
						color: #ffffff;
						padding: 4rpx 16rpx;
						background: linear-gradient(to right, #fe3b0f, #fc603a);
						border-radius: 6rpx;
						margin-right: 10rpx;
					}
				}
			}

			.price-info {
				display: flex;
				flex-wrap: wrap;
				align-items: center;
				justify-content: space-between;
				padding: 0 20rpx;
				height: 80rpx;

				.user-price {
					display: flex;
					align-items: center;

					text {
						color: #ff0000;
					}

					.min {
						font-size: 24rpx;
					}

					.max {
						font-size: 32rpx;
					}
				}

				.vip-price {
					display: flex;
					align-items: center;

					image {
						width: 26rpx;
						height: 26rpx;
						margin-right: 10rpx;
					}

					text {
						color: #fcb735;
						font-size: 24rpx;
					}
				}
			}
		}
	}
}

/* 结算 */
.close-account {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 130rpx;
	background-color: #ffffff;
	border-top: 2rpx solid #f6f6f6;

	.check-total {
		display: flex;
		align-items: center;
		width: 50%;
		height: 100%;

		.check {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 40%;
			height: 100%;

			text {
				font-size: 36rpx;
				color: #333333;
			}

			.icon-checked {
				color: #fe3b0f;
				// box-shadow: 0 0 10rpx  #fe3b0f;
			}

			.all {
				font-size: 24rpx;
				margin-left: 10rpx;
			}
		}

		.total {
			display: flex;
			align-items: center;
			width: 60%;
			height: 100%;

			text {
				font-size: 24rpx;
				color: #333333;
			}

			.price {
				font-weight: bold;
				color: #fe3b0f;
			}
		}
	}

	.account {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-right: 4%;
		margin-top: 20rpx;

		.btn-calculate {
			display: flex;
			justify-content: center;
			align-items: center;
			flex: 1;
			margin: 0 30rpx;
			height: 60rpx;
			background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
			border-radius: 60rpx;

			text {
				color: #ffffff;
				font-size: 24rpx;
			}
		}

		.btn-del {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.attention {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 140rpx;
				height: 60rpx;
				border: 2rpx solid #eeeeee;
				border-radius: 60rpx;
				color: #333333;
				font-size: 24rpx;
				margin-right: 20rpx;
			}

			.del {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100rpx;
				height: 60rpx;
				background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
				border-radius: 60rpx;
				color: #ffffff;
				font-size: 24rpx;
			}
		}
	}
}

.icon-subtract {
	position: relative;
	margin: 0 20rpx;

	&::after {
		content: '';
		display: block;
		width: 25rpx;
		height: 5rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}
}

.icon-add {
	position: relative;
	margin: 0 20rpx;

	&::after {
		content: '';
		display: block;
		width: 25rpx;
		height: 5rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}

	&::before {
		content: '';
		display: block;
		width: 5rpx;
		height: 25rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}
}

.check-icon {
	width: 35rpx;
	height: 35rpx;
	border: 1px solid #999999;
	border-radius: 50%;

	&.active {
		border: 1px solid #e5452f;
		color: #e5452f !important;
		line-height: 30rpx;
	}
}

/deep/.uni-stat__select {
	width: 80%;
}

.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 180rpx;
}
</style>
