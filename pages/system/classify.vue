<template>
	<view class="page" ref="page">
		<view class="show_main">
			<view class="filter-top"></view>
			<scroll-view class="uni-select__selector-scroll" scroll-y>
				<view class="scroll-list">
					<view class="list" v-for="(item, index) in infoData" :key="index">
						<view class="title">
							<view class="title_boder"></view>
							{{ item.name }}
						</view>
						<view class="classify">
							<view class="list-item" v-for="(list, index1) in item.listData" :key="index1"
								:class="list.check ? 'active' : ''" @click.stop="handleCheck(list)">
								{{ list.name }}
							</view>
						</view>
					</view>
				</view>
			</scroll-view>

			<view class="footer-btn">
				<button class="del" @click.stop="handleCancel">
					<text>重置</text>
				</button>
				<button class="btn action" @click.stop="handleSave">
					<text class="action">确认</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getCurrentModel
	} from "@/api/order.js";
	export default {
		data() {
			return {
				getMenu: this.$getMenu.menuInfo,
				currentIndex: 0,
				infoData: [],
				productList: [], //	机型筛选列表
				type: [], //知识库类型
			};
		},
		// 下拉刷新
		onPullDownRefresh() {
			uni.stopPullDownRefresh();
		},

		async onLoad(params) {

		},
		onShow() {
			this.infoData = []
			this.productList = [] //	机型筛选列表
			this.type = [] //知识库类型
			this.initData()
		},
		methods: {
			initData() {
				// 品牌机型
				this.$http.get(getCurrentModel).then(res => {
					let productLists = uni.getStorageSync('learnParmes').productList
					if (res.data.length > 0) {
						let obj = {
							name: '品牌机型',
							listData: res.data.map((tag) => {
								return tag.models.map((tages, index) => {
									return {
										name: `${tag.brandName}/${tag.models[index].modelName}`,
										productId: tag.models[index].productId,

										check: !productLists ? false : productLists.includes(tag.models[index].productId) ?
											true : false,
									};
								})
							}),
						};
						let arr = []
						obj.listData.map(item => {
							item.forEach((items, indexs) => {
								arr.push(items)
							})
						})
						obj.listData = arr
						this.infoData.push(obj)

					}
					this.typeofClass()
				})

			},
			// 知识库类型
			typeofClass() {
				this.$http.get(`/api/magina/api/code/dict-item/${3500}/tree`, this.parmes).then(res => {
					let types = uni.getStorageSync('learnParmes').type
					res.data.forEach(item => {
						item.check = !types ? false : types.includes(item.value) ? true : false;
						item.name = item.label
					})
					let obj = {
						name: '知识库类型',
						listData: res.data
					}
					this.infoData = this.infoData.concat(obj);

					console.log(this.infoData, '3333333333333');
				})
			},
			// 点击
			handleCheck(list) {
				list.check = !list.check;
				// 选择的机型
				if (list.productId && !this.productList.includes(list.productId) && list.check) {
					this.productList.push(list.productId)
				} else {
					this.productList = this.productList.filter(item => item !== list.productId);
				}
				// 选择的知识库
				if (list.value && !this.type.includes(list.value) && list.check) {
					this.type.push(list.value)
				} else {
					this.type = this.type.filter(item => item !== list.value);
				}
			},
			handleSave() {
				uni.setStorageSync('learnParmes', {
					productList: this.productList,
					type: this.type
				})
				uni.switchTab({
					url: "/pages/learn/index",
				});
			},
			handleCancel() {
				uni.removeStorageSync('learnParmes')
				this.productList = []; //	机型筛选列表
				this.type = []; //知识库类型
				this.infoData.forEach(item => {
					item.listData.forEach(inject => {
						inject.check = false
					})
				})
			},

		},
	};
</script>

<style scoped lang="scss">
	.show_popup {
		width: 100%;
		height: 100vh;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 100000;
		background-color: rgba(0, 0, 0, 0.5);
	}

	.show_main {
		width: 100%;
		padding: 0 22rpx;
		padding-bottom: 110rpx;
		box-sizing: border-box;
		background-color: #fff;
		float: right;

		.filter-top {
			width: 90%;
			background-color: #fff;
			margin: auto;
		}

		.uni-select__selector-scroll {
			width: 100%;

			.scroll-list {
				height: 100vh;
				overflow: hidden;
				overflow-y: auto;
				padding-bottom: 100rpx;
				box-sizing: border-box;
			}

			.list {
				position: relative;
				width: 100%;
				border-radius: 6rpx;
				margin-bottom: 26rpx;

				.title {
					width: 100%;
					font-size: 29rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #0f0f0f;
					line-height: 39rpx;
					text-align: left;
					display: flex;
					align-items: center;

					.title_boder {
						width: 6rpx;
						height: 20rpx;
						background: linear-gradient(to bottom,
								#e5452f 0%,
								#ee822f 100%) !important;
						margin-right: 12rpx;
						border-radius: 4rpx;
					}
				}

				.classify {
					display: flex;
					justify-content: space-between;
					align-items: center;
					flex-wrap: wrap;

					.list-item {
						box-sizing: border-box;
						width: 48%;
						height: 72rpx;
						line-height: 72rpx;
						text-align: center;

						background: #f6f6f6;
						border-radius: 36rpx;
						margin-top: 27rpx;

						&.active {
							color: #fff;
							background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
						}
					}
				}
			}
		}

		.footer-btn {
			width: 100%;
			height: 100rpx;
			padding: 0 22rpx;
			padding-bottom: 44rpx;
			box-sizing: border-box;
			position: fixed;
			right: 0;
			bottom: 0;
			z-index: 10;
			display: flex;
			align-items: center;
			background-color: #ffffff;

			.del {
				display: flex;
				align-items: center;
				width: 50%;
				height: 75rpx;
				color: #fff;
				background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
				border-radius: 37px 0px 0px 37px;
				text-align: center;
				justify-content: center;

				text {
					padding: 10rpx 30rpx;
					font-size: 31rpx;
					font-weight: bold;
				}
			}

			.btn {
				display: flex;
				align-items: center;
				width: 50%;
				height: 75rpx;
				color: #c0c0c0;
				border: 2rpx solid #c0c0c0;
				border-radius: 0px 37px 37px 0px;
				text-align: center;
				justify-content: center;

				text {
					font-size: 31rpx;
					font-weight: bold;
				}

				&.action {
					background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
					background-color: $base;
					color: #ffffff;
					border: 2rpx solid #ffffff;
					color: #fff;
					border: none;
				}
			}
		}
	}
</style>