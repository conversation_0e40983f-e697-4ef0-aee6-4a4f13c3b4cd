<template>
	<view class="main">
		<!-- 		<view class="box1">
			<FormQuery :form-columns="formColumns" @search="search" />
		</view> -->
		<view class="box2">
			<!-- 购物车列表 -->
			<view class="cart-list" v-if="goodsList.length > 0">
				<view class="list" v-for="goods in goodsList" :key="goods.id">
					<view class="order-info">
						<view class="time">物品编号：{{ goods.articleCode }}</view>
						<view class="time">{{ goods.createdAt }}</view>
					</view>
					<view class="goods">
						<view class="thumb" @tap.stop="previewImage(good.imageFiles[0].url)">
							<image :src="goods.imageFiles[0].url" mode=""></image>
						</view>
						<view class="item">
							<view class="left">
								<view class="title">
									<text class="two-omit">{{ goods.articleName }}</text>
								</view>
								<view class="oem">
									<text class="oem-item">OEM编号：{{ goods.numberOem }}</text>
								</view>
								<view class="attribute">
									<view class="attr">
										<text>{{ goods.manufacturerChannel.label }}</text>
									</view>
								</view>
							</view>
							<view class="price-num">
								<view class="num">
									<view class="number">
										<text>数量：{{ goods.number }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="desc">问题描述：{{ goods.description }}</view>
				</view>
			</view>
			<view v-if="goodsList.length === 0" style="padding-top: 200rpx">
				<rf-empty :info="'暂无问题商品登记记录'"></rf-empty>
			</view>
			<view class="loading" v-if="isEnloading && goodsList.length">{{ isloading ? '加载中...' : goodsList.length < total ? '上拉加载更多' : '' }}</view>
			<view class="loading" v-if="goodsList.length > 0 && goodsList.length == total">—— 到底啦 ——</view>
		</view>
	</view>
</template>

<script>
import { getProblemGoodsList } from '@/api/system.js';
import FormQuery from '@/components/formQuery/index';
export default {
	components: { FormQuery },
	data() {
		return {
			// 是否到底
			isEnloading: false,
			// 分页参数
			pageNumber: 1,
			// 总数
			total: 0,
			// 是否加载完成
			isloading: true,
			goodsList: [],
			// 图片预览
			previewList: [],
			formColumns: [
				{
					dataIndex: 'applyCode',
					title: '领料单号',
					valueType: 'input'
				},

				{
					dataIndex: 'date',
					title: '领料时间',
					valueType: 'datetime',
					inputType: 'daterange'
				},
				{
					dataIndex: 'oemNumber',
					title: 'OEM编号',
					valueType: 'input'
				}
			]
		};
	},
	onLoad() {},
	onShow() {
		this.getData();
	},
	// 滚动到底部
	onReachBottom() {
		if ((this.pageNumber - 1) * 10 >= this.total) {
			this.isEnloading = true;
			return;
		}
		console.log('触底');
		this.getData(); // 商品列表
	},
	// 下拉刷新
	onPullDownRefresh() {
		// this.getData(); // 商品列表
		uni.stopPullDownRefresh(); // 停止下拉刷新
	},
	created() {},
	methods: {
		previewImage(url) {
			this.previewList = [];
			this.previewList.push(url);
			uni.previewImage({
				urls: this.previewList,
				current: 0
			});
		},
		getData() {
			this.isloading = true;
			getProblemGoodsList({
				pageNumber: this.pageNumber,
				pageSize: 10
			}).then((res) => {
				this.pageNumber++;
				this.isloading = false;
				this.isEnloading = false;
				this.goodsList = [...this.goodsList, ...res.data.rows];
				this.total = res.data.total;
			});
		}
	}
};
</script>

<style scoped lang="scss">
.main {
	width: 100vw;
	height: 100vh;
	padding: 0 22rpx;
	box-sizing: border-box;
	background-color: #fff;
	.box2 {
		// margin-top: 420rpx;
	}
}

/* 购物车列表 */
.cart-list {
	width: 100%;
	padding: 20rpx 0;

	.list {
		background-color: #ffffff;
		box-shadow: 0 0 20rpx #f6f6f6;
		border-radius: 10rpx;
		margin: 20rpx 0;
		.desc {
			padding: 20rpx;
		}
		.order-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.goods {
			display: flex;
			align-items: center;
			width: 100%;
			height: 190rpx;

			.thumb {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 30%;
				height: 100%;
				// margin-top: 20rpx;

				image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 10rpx;
				}
			}

			.item {
				padding: 10rpx 0;
				width: 70%;
				height: 100%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				.left {
					.title {
						display: flex;
						align-items: center;
						width: 100%;
						height: auto;

						text {
							font-size: 26rpx;
							color: #212121;
						}
					}
					.oem {
						text {
							font-size: 24rpx;
							color: #212121;
						}
					}
					.attribute {
						display: flex;
						align-items: center;
						margin-top: 10rpx;

						.attr {
							display: flex;
							align-items: center;
							padding: 0 20rpx;
							height: 40rpx;
							background-color: #f6f6f6;
							border-radius: 10rpx;

							text {
								font-size: 24rpx;
								color: #333333;
							}
						}
					}
				}

				.price-num {
					display: flex;
					align-items: center;
					justify-content: space-between;
					height: 45rpx;
					.num {
						display: flex;
						margin-right: 20rpx;
						.number {
							display: flex;
							justify-content: center;
							align-items: center;
							text {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
				}
			}
		}
	}
}

.time {
	font-size: 26rpx;
	color: #827f7f;
	font-weight: 800;
	padding: 10rpx 0;
}

.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 40rpx;
}
</style>
