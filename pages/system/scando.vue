<!--
 * @Description: 
 * @Autor: shh
 * @Date: 2023-03-13 09:33:58
 * @LastEditors: shan<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-18 17:30:17
-->
<template>
  <view class="home-box">
    <view class="scanshopbox">
      <view class="shop-list">
        <view
          class="list"
          :class="{ active: item.customerId === customerId }"
          v-for="(item, index) in infoData"
          :key="index"
          @click="changeCheck(item.customerId)"
        >
          <view class="right">
            <view class="title">{{ item.customerName }}</view>
            <text class="address">{{ item.fullAddress }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import { selectStore, shopInfo } from "@/api/index";

export default {
  components: {},
  data() {
    return {
      infoData: [],
      uid: "",
      isfirst: true,
      customerId: null,
      istoken: uni.getStorageSync("accessToken"),
    };
  },

  filters: {},

  created() {},
  onLoad(params) {
    this.uid = params.uid;
    this.$http.get(`${shopInfo}`).then((res2) => {
      this.infoData = res2.data;
    });
  },
  onShow() {},

  methods: {
    changeCheck(customerId) {
      this.customerId = customerId;
      this.$http
        .get(`${selectStore}`, { customerId: customerId, uid: this.uid })
        .then((res) => {
          setTimeout(() => {
            if (res.data.first) {
              this.$mRouter.push({
                route: `/pages/system/scan?type=1`,
              });
            } else {
              this.$mRouter.push({
                route: `/pages/system/scan?type=5&msg=${res.data.second}`,
              });
            }
          }, 300);
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.home-box {
  width: 100%;
  height: 100vh;
  position: relative;
  background: #fff;
}

.scanshopbox {
  padding: 20px;
  .shop-list {
    padding: 0 4%;

    text-align: left;

    .list {
      position: relative;
      display: flex;
      width: 100%;
      border-radius: 6rpx;
      margin-bottom: 20rpx;
      overflow: hidden;
      padding: 30rpx 20rpx;
      border: 1px solid #ddd;
      border-radius: 20px;
      &.active {
        border: 1px solid #e5452f;
        color: #e5452f !important;
      }
      .left {
        width: 50rpx;

        .check {
          width: 35rpx;
          height: 35rpx;
          border: 1px solid #999999;
          border-radius: 50%;

          &.active {
            border: 1px solid #e5452f;
            color: #e5452f !important;
            line-height: 30rpx;
          }
        }
      }

      .right {
        flex: 1;
        padding: 0 20rpx;

        .title {
          font-size: 29rpx;
          font-family: PingFang SC;
          font-weight: bold;
          color: #0f0f0f;
          line-height: 39rpx;
        }

        .address {
          font-size: 27rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #0f0f0f;
          line-height: 40rpx;
        }
      }
    }
  }
}
</style>
