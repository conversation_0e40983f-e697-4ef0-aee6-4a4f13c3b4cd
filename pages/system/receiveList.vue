<template>
	<view class="main">
		<view class="search" v-if="!customerSeqId">
			<FormQuery :form-columns="formColumns" @search="search" />
		</view>
		<!-- :class="{ margin200: !customerSeqId }" -->
		<view class="container" :class="{ margin20: !customerSeqId }">
			<view class="audit-list" v-for="item in dataList" :key="item.id">
				<view class="head">
					<view class="left">
						<view class="code">领料单号：{{ item.orderNum }}</view>
						<view class="name">客户名称：{{ item.customerName }}</view>
					</view>
					<view class="right">
						<view class="price">
							金额：
							<text style="color: #f37b1d">￥{{ item.actualAmount }}</text>
						</view>
						<view class="btn" v-if="item.orderStatus === 'WAIT_AUDIT'" @click="handleAudit(item.orderNum, 'audit')">去审核</view>
						<view class="btn-view" v-else @click="handleAudit(item.orderNum, 'info')">查看</view>
					</view>
				</view>
				<view class="address">客户地址：{{ item.consigneeFullAddress }}</view>
			</view>
		</view>
		<view v-if="dataList.length === 0">
			<rf-empty :info="'暂无领料审核'"></rf-empty>
		</view>
		<!-- 页面加载-->
		<view class="loading" v-if="isEnloading && dataList.length">{{ isloading ? '加载中...' : dataList.length < total ? '上拉加载更多' : '' }}</view>
		<view class="loading" v-if="dataList.length > 0 && dataList.length == total">—— 到底啦 ——</view>
	</view>
</template>

<script>
import { getCustomerMaterialAuditListApi } from '@/api/custom.js';
import FormQuery from '@/components/formQuery/index';
export default {
	components: {
		FormQuery
	},
	data() {
		return {
			isEnloading: false, // 是否到底
			pageNumber: 1, // 分页参数
			total: 0, // 总数
			customerSeqId: '', // 客户编号
			isloading: true, // 是否加载完成
			dataList: [], // 列表

			searchParams: {
				orderStatusList: 'WAIT_AUDIT'
			},
			formColumns: [
				{
					dataIndex: 'orderStatusList',
					title: '订单状态',
					valueType: 'select',
					default: 'WAIT_AUDIT',
					localdata: [
						{
							text: '待审核',
							value: 'WAIT_AUDIT'
						},
						{
							text: '待发货',
							value: 'WAIT_DELIVER'
						},
						{
							text: '待收货',
							value: 'WAIT_RECEIVE'
						},
						{
							text: '已完成',
							value: 'SUCCESS'
						},
						{
							text: '已取消',
							value: 'CLOSED'
						}
					]
				}
			]
		};
	},
	onLoad(options) {
		this.customerSeqId = options.customerSeqId;
	},
	onShow() {
		this.refresh();
	},
	// 滚动到底部
	onReachBottom() {
		if ((this.pageNumber - 1) * 10 >= this.total) {
			this.isEnloading = true;
			return;
		}
		this.getData(); // 商品列表
	},
	onPullDownRefresh() {
		if (this.isloading) return;
		uni.startPullDownRefresh({
			success: () => {
				this.refresh();
			}
		});
		uni.stopPullDownRefresh();
	},
	created() {},
	methods: {
		search(val) {
			if (val.orderStatusList === 'AUDIT') {
				delete val.orderStatusList;
			}
			this.searchParams = val;
			console.log(this.searchParams);
			this.refresh();
		},
		getData() {
			this.isloading = true;
			const args = {
				...this.searchParams,
				pageNumber: this.pageNumber,
				pageSize: 10,
				customerSeqId: this.customerSeqId,
				orderType: 'APPLY'
			};
			getCustomerMaterialAuditListApi(args).then((res) => {
				this.pageNumber++;
				this.isloading = false;
				this.isEnloading = false;
				this.dataList = [...this.dataList, ...res.data.rows];
				this.total = res.data.total;
			});
		},
		handleAudit(code, type) {
			uni.navigateTo({
				url: `/pages/system/receiveReview?code=${code}&type=${type}`
			});
		},
		refresh() {
			this.pageNumber = 1;
			this.dataList = [];
			this.total = 0;
			this.getData();
		}
	}
};
</script>

<style scoped lang="scss">
.margin200 {
	margin-top: 200rpx;
}
.margin20 {
	margin-top: 20rpx;
}
.main {
	padding: 22rpx;

	.container {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: normal;
		padding: 0px !important;
		.audit-list {
			background: #ffffff;
			border-radius: 24rpx;
			padding: 15rpx;
			margin-bottom: 30rpx;
			.head {
				display: flex;
				justify-content: space-between;
				align-items: center;
				height: 120rpx;
				.left {
					width: 65%;
					height: 100%;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					padding: 15rpx 0;
					.code {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 400;
						color: #333333;
					}
					.name {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 400;
						color: #333333;
					}
				}
				.right {
					width: 35%;
					height: 100%;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					padding: 15rpx 0;
					padding-left: 15rpx;
					.price {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 400;
						color: #333333;
						margin-bottom: 10rpx;
					}
					.btn,
					.btn-view {
						width: 100rpx;
						height: 40rpx;
						line-height: 40rpx;
						text-align: center;
						background: #ff6f29;
						border-radius: 20rpx;
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 400;
						color: #ffffff;
					}
					.btn-view {
						background: #457dec;
					}
				}
			}
		}
	}
}
.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 40rpx;
}
</style>
