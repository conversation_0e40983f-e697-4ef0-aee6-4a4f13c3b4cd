<template>
	<view class="main">
		<view class="box1">
			<FormQuery :form-columns="formColumns" @search="search" />
		</view>

		<view class="box2">
			<!-- 购物车列表 -->
			<view class="cart-list" v-if="goodsList.length > 0">
				<view class="list" v-for="good in goodsList" :key="good.id">
					<view class="goods-info">
						<view class="check" @click="checkGood(good)">
							<text
								class="check-icon"
								:class="{
									'active iconfont icon-duihao': isCheck(good.choosesku.id)
								}"
							></text>
						</view>
						<view class="goods">
							<view class="thumb" @tap.stop="previewImage(good.picsUrl[0].url)">
								<image :src="good.picsUrl[0].url" mode=""></image>
							</view>
							<view class="item">
								<view class="title">
									<text class="two-omit">{{ good.name }}</text>
								</view>
								<view class="oem">
									<text class="oem-item">OEM编号：{{ good.choosesku.invSkuOem }}</text>
								</view>
								<view class="attribute" @tap.stop="getGoodsAttr(good)">
									<view class="attr">
										<text>{{ good.saleAttrValsStr }}</text>
										<text class="more"></text>
									</view>
								</view>
								<view class="price-num">
									<view class="price">
										<text class="desc">问题描述</text>
									</view>
									<view class="num" @tap.stop>
										<text class="add" @tap.stop="changeBuyNumber(good, 'sub')">
											<text class="iconfont icon-subtract"></text>
										</text>
										<view class="number">
											<text>{{ good.SkuNum }}</text>
										</view>
										<text class="add" @tap.stop="changeBuyNumber(good, 'add')">
											<text class="iconfont icon-add" :class="{ 'icon-disable': good.SkuNum === good.choosesku.availableNum }"></text>
										</text>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="desc">
						<textarea :maxlength="500" v-model="good.description" placeholder="请输入该商品出现问题描述"></textarea>
						<view class="max-length">{{ good.description ? String(good.description).length : 0 }}/500</view>
					</view>
				</view>
			</view>
			<view v-if="goodsList.length === 0" style="padding-top: 200rpx">
				<rf-empty :info="'暂未查询到商品'"></rf-empty>
			</view>
			<view class="loading" v-if="!isEnloading && goodsList.length">{{ isloading ? '加载中...' : goodsList.length < total ? '上拉加载更多' : '' }}</view>
			<view class="loading" v-if="goodsList.length > 0 && goodsList.length == total && isEnloading">—— 到底啦 ——</view>

			<!-- 结算 -->
			<view class="close-account">
				<view class="account">
					<view class="btn-calculate" @tap.stop="registerhistory">
						<text>登记记录</text>
					</view>
					<view class="btn-calculate" @tap.stop="confirmProblem">
						<text>确认登记</text>
					</view>
				</view>
			</view>
		</view>
		<goods-attr
			ref="GoodsAttr"
			:allSaleAttr="allSaleAttr"
			:sku="sku"
			@choose="handleAttrChoose"
			@addCart="addCart"
			:attrMap="attrMap"
			:skuMap="skuMap"
			:tempSku="tempSku"
		></goods-attr>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
import { brandSerialList, getFilterData, consumablesList, applyWare, brandModelList, classifyList } from '@/api/index';
import { addProblemGoods } from '@/api/system.js';
// import GoodsAttr from '../system/components/GoodsAttr.vue';
import GoodsAttr from './components/GoodsAttr.vue';
import FormQuery from '@/components/formQuery/index';
export default {
	components: {
		GoodsAttr,
		FormQuery
	},
	data() {
		return {
			isEnloading: false,
			pageNumber: 1,
			total: 0,
			isloading: true,
			searchparms: {},
			productList: [],
			categoryList: [],
			categoryListId: '',
			classList: [],
			categoryId: '',
			unitList: [],
			unitListId: '',
			sku: [],
			allSaleAttr: {},
			attrMap: {},
			skuMap: [],
			loading: true,
			tempSku: {},
			currentChoose: {},
			goodsList: [],
			checkList: [],
			// 申领总数
			totalCount: 0,
			isSuccess: false,
			type: 'register',
			// 图片预览
			previewList: [],
			formColumns: [
				{
					dataIndex: 'product',
					title: '适用机型',
					valueType: 'machine'
				},
				{
					dataIndex: 'categoryId',
					title: '物品分类',
					valueType: 'select',
					localdata: []
				},
				{
					dataIndex: 'categoryListId',
					title: '零件分类',
					valueType: 'select',
					localdata: []
				},
				{
					dataIndex: 'unit',
					title: '所属单元',
					valueType: 'select',
					localdata: []
				},
				{
					dataIndex: 'oem',
					title: 'OEM编号',
					valueType: 'input'
				}
			]
		};
	},
	// 滚动到底部
	onReachBottom() {
		if ((this.pageNumber - 1) * 10 >= this.total) {
			this.isEnloading = true;
			return;
		}
		this.saveFn(); // 商品列表
	},
	onLoad() {
		this.getDataFn();
	},
	methods: {
		// 登记错误商品
		registerWrong() {
			this.type = 'check';
		},
		// 查看问题商品
		checkGoods() {
			this.type = 'register';
		},
		// 确认登记问题商品
		confirmProblem() {
			if (this.checkList.length === 0) {
				uni.showToast({
					title: '请选择商品',
					icon: 'none'
				});
				return;
			}
			let sign = this.checkList.some((item) => !item.description);
			if (sign) {
				uni.showToast({
					title: '请填写选中商品问题描述',
					icon: 'none'
				});
				return;
			}
			uni.showModal({
				title: '提示',
				content: '确认登记商品问题吗？',
				success: (res) => {
					if (res.confirm) {
						let params = [];
						this.checkList.map((item) => {
							params.push({
								articleCode: item.choosesku.articleCode,
								description: item.description,
								number: item.SkuNum
							});
						});
						addProblemGoods(params)
							.then((res) => {
								uni.showToast({
									title: '登记成功',
									icon: 'none'
								});
							})
							.catch((err) => {
								uni.showToast({
									title: err || '系统错误',
									icon: 'none'
								});
							});
					} else if (res.cancel) {
					}
				}
			});
		},
		// 登记记录
		registerhistory() {
			uni.navigateTo({
				url: '/pages/system/registerhistory'
			});
		},
		search(value) {
			this.searchparms = value;
			this.saveFn(1);
		},
		previewImage(url) {
			this.previewList = [];
			this.previewList.push(url);
			uni.previewImage({
				urls: this.previewList,
				current: 0
			});
		},
		getDataFn() {
			this.classList = []; // 商品分类
			this.categoryList = []; // 零件分类
			this.goodsList = [];
			this.unitList = []; // 所属单元
			this.loading = true;
			// 商品分类
			this.$http.get(classifyList, { pageNumber: 1, pageSize: 9999 }).then((res) => {
				// this.categoryId = this.categoryId ? this.categoryId : res?.data?.rows[0].id;
				res.data.rows.map((el) => {
					this.classList.push({
						value: el.id,
						text: el.name
					});
				});
			});
			this.$http.get(getFilterData).then((res) => {
				res.data.tagList.map((el) => {
					if (el.name == '零件分类') {
						this.categoryListId = el.id;
						el.value.map((a) => {
							this.categoryList.push({
								value: a,
								text: a
							});
						});
					}
					if (el.name == '所属单元') {
						this.unitListId = el.id;
						el.value.map((b) => {
							this.unitList.push({
								value: b,
								text: b
							});
						});
					}
				});
				this.loading = false;
			});
			this.formColumns.forEach((item, index) => {
				if (item.dataIndex === 'categoryId') {
					this.formColumns[index].localdata = this.classList;
				}
				if (item.dataIndex === 'categoryListId') {
					this.formColumns[index].localdata = this.categoryList;
				}
				if (item.dataIndex === 'unit') {
					this.formColumns[index].localdata = this.unitList;
				}
			});
		},

		getGoodsAttr(data) {
			this.sku = data.skuList;
			this.allSaleAttr = {
				...this.handleSkuAttr(data.skuList)
			};
			this.init(data);
			setTimeout(() => {
				this.$refs.GoodsAttr.show(2, data.choosesku.id);
			}, 300);
		},
		init(data) {
			const allSaleAttr = [...Object.keys(this.allSaleAttr).map((item) => this.allSaleAttr[item])].flat();
			const primeList = this.getPrime(allSaleAttr.length);
			let index = 0;
			const attrMap = {};
			Object.keys(this.allSaleAttr).forEach((key) => {
				this.allSaleAttr[key].forEach((item) => {
					attrMap[key + '@' + item] = primeList[index];
					index++;
				});
			});
			this.attrMap = {
				...attrMap
			};
			const skuMap = [];
			const tempInfo = {
				availableNum: [this.sku[0].availableNum, this.sku[0].availableNum],
				saleUnitPrice: data.miniPrice,
				picsUrl: data.picsUrl[0]
			};
			this.sku.forEach((item) => {
				if (item.availableNum < tempInfo.availableNum[0]) tempInfo.availableNum[0] = item.availableNum;
				if (item.availableNum > tempInfo.availableNum[1]) tempInfo.availableNum[1] = item.availableNum;
				const target = item.saleAttrVals;
				let result = 1;
				target.forEach((item) => {
					result *= attrMap[item.name + '@' + item.val];
				});
				skuMap.push(result);
			});
			this.skuMap = skuMap;
			this.tempSku = tempInfo;
		},
		handleSkuAttr(skuList) {
			const skuAttr = [];
			skuList.forEach((item) => {
				skuAttr.push(...item.saleAttrVals);
			});
			const result = [];
			skuAttr.forEach((item) => {
				if (result[item.name]) {
					result[item.name].indexOf(item.val) === -1 && result[item.name].push(item.val);
				} else {
					result[item.name] = [item.val];
				}
			});
			return result;
		},
		getPrime(num) {
			const isPrime = (number) => {
				const target = number / 2;
				if (target === 2) return false;
				for (let index = 2; index < target; index++) {
					if (number % index === 0) return false;
				}
				return true;
			};
			const arr = [];
			for (let i = 2; arr.length < num; i++) {
				if (isPrime(i)) arr.push(i);
			}
			return arr;
		},
		addCart() {
			if (JSON.stringify(this.currentChoose) == '{}' || !this.currentChoose) {
				uni.showToast({
					title: '请选择商品',
					icon: 'error'
				});
				return;
			}
			let goodsList = JSON.parse(JSON.stringify(this.goodsList));
			goodsList.map((ele) => {
				if (ele.id == this.currentChoose.sku.itemId) {
					let saleAttrValsStr = this.currentChoose.sku.saleAttrVals.map((item) => item.val);
					ele.saleAttrValsStr = saleAttrValsStr.join(',');
					ele.choosesku = this.currentChoose.sku;
				}
			});
			// 当前修改的sku属性已被选中，将选中的数量赋给itemId的SkuNum
			const index = this.checkList.findIndex((item) => item.id === this.currentChoose.sku.id);
			if (index !== -1) {
				goodsList.forEach((item, i) => {
					if (item.id === this.currentChoose.sku.itemId) {
						goodsList[i].SkuNum = this.checkList[index].SkuNum;
					}
				});
			}
			setTimeout(() => {
				this.$refs['GoodsAttr'].hide();
				this.goodsList = JSON.parse(JSON.stringify(goodsList));
				this.$forceUpdate();
			}, 300);
		},

		handleAttrChoose(val) {
			this.currentChoose = val;
		},
		// 查询
		async saveFn(type) {
			if (type) {
				this.goodsList = [];
				this.pageNumber = 1;
			}
			this.isloading = true;
			let tags = [];
			if (this.searchparms.categoryListId) {
				tags.push({
					tagId: this.categoryListId,
					values: [this.searchparms.categoryListId]
				});
			}

			if (this.searchparms.unit) {
				tags.push({
					tagId: this.unitListId,
					values: [this.searchparms.unit]
				});
			}
			let verify = await this.$http.post(consumablesList, {
				categoryId: this.searchparms.categoryId,
				oem: this.searchparms.oem,
				productTreeIdList: this.searchparms.productTreeIdList,
				tags: tags,
				pageNumber: this.pageNumber,
				pageSize: 10
			});
			console.log(verify);
			this.pageNumber++;
			this.isloading = false;
			this.total = verify.data.total;
			verify.data.rows.map((el) => {
				el.SkuNum = 1;
				if (el.skuList[0] && el.skuList[0].saleAttrVals) {
					let saleAttrValsStr = el.skuList[0].saleAttrVals.map((item) => item.val);
					el.saleAttrValsStr = saleAttrValsStr.join(',');
				}

				el.choosesku = el.skuList[0];
			});
			this.goodsList = this.goodsList.concat(verify.data.rows);
		},

		async changeBuyNumber(data, type) {
			if (type == 'add') {
				if (data.SkuNum === data.choosesku.availableNum) return;
				data.SkuNum = data.SkuNum + 1;
			} else if (type == 'sub') {
				if (data.SkuNum > 1) {
					data.SkuNum = data.SkuNum - 1;
				}
			}
			// 查找并更新checkList中与data.id匹配的条目
			const foundIndex = this.checkList.findIndex((item) => item.id === data.choosesku.id);
			if (foundIndex !== -1) {
				this.checkList[foundIndex].SkuNum = data.SkuNum;
			}
			this.calcTotalCount();
		},
		isCheck(goodId) {
			for (let i = 0; i < this.checkList.length; i++) {
				if (this.checkList[i].choosesku.id === goodId) {
					return true; // 找到匹配的id，返回false
				}
			}
			return false; // 没有找到匹配的id，返回true
		},
		checkGood(goods) {
			// console.log(goodId);
			let found = false;
			for (let i = 0; i < this.checkList.length; i++) {
				if (this.checkList[i].choosesku.id === goods.choosesku.id) {
					found = true;
					this.checkList.splice(i, 1);
					break;
				}
			}
			if (!found) {
				this.checkList.push(goods);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
// .box1 {
// 	position: fixed;
// 	width: 100%;
// 	top: 0;
// 	left: 0;
// 	right: 0;
// 	background: #fff;
// 	z-index: 9;
// 	padding: 20rpx 22rpx !important;
// 	border-bottom: 5rpx solid #f5f5f5;
// }

// .box2 {
// 	margin-top: 620rpx;
// }

.main {
	width: 100vw;
	// height: 100vh;
	padding: 0 22rpx;
	box-sizing: border-box;
	background-color: #fff;
}
</style>
<style scoped lang="scss">
.page {
	background: #f5f6f8;
	min-height: 100vh;
}

/* 购物车列表 */
.cart-list {
	width: 100%;
	padding: 20rpx 0;
	// margin-top: 100rpx;

	.list {
		margin: 20rpx 0;
		.desc {
			width: 100%;
			position: relative;
			border-radius: 28rpx;
			background-color: #fff;
			textarea {
				width: 100%;
				height: 160rpx;
				border: 1rpx solid #e5e5e5;
				padding: 20rpx;
				box-sizing: border-box;
			}
			.max-length {
				position: absolute;
				right: 0;
				bottom: 0;
				font-size: 24rpx;
			}
		}
		.goods-info {
			display: flex;
			height: 210rpx;
			background-color: #ffffff;
			box-shadow: 0 0 20rpx #f6f6f6;
			border-radius: 10rpx;

			.check {
				display: flex;
				align-items: center;
				width: 10%;
				height: 100%;
				margin-left: 20rpx;

				text {
					font-size: 36rpx;
					color: #333333;
				}

				.icon-checked {
					color: #fe3b0f;
				}
			}

			.goods {
				display: flex;
				align-items: center;
				width: 90%;
				height: 100%;

				.thumb {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 30%;
					height: 100%;

					image {
						width: 160rpx;
						height: 160rpx;
						border-radius: 10rpx;
					}
				}

				.item {
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					padding: 10rpx 0;
					width: 70%;
					height: 100%;

					.title {
						display: flex;
						align-items: center;
						width: 100%;
						height: auto;

						text {
							font-size: 26rpx;
							color: #212121;
						}
					}
					.oem {
						text {
							font-size: 24rpx;
							color: #212121;
						}
					}
					.attribute {
						display: flex;
						align-items: center;
						margin-top: 10rpx;

						.attr {
							display: flex;
							align-items: center;
							padding: 0 20rpx;
							height: 40rpx;
							background-color: #f6f6f6;
							border-radius: 10rpx;

							text {
								font-size: 24rpx;
								color: #333333;
							}

							.more {
								display: flex;
								width: 10rpx;
								height: 10rpx;
								border-left: 2rpx solid #333333;
								border-bottom: 2rpx solid #333333;
								transform: rotate(-45deg);
								margin-left: 10rpx;
							}
						}
					}

					.price-num {
						display: flex;
						align-items: center;
						justify-content: space-between;
						height: 45rpx;

						.icon-disable {
							&::after,
							&::before {
								background-color: #ccc;
							}
						}

						.price {
							display: flex;

							.min {
								color: #fe3b0f;
								font-size: 24rpx;
							}

							.max {
								font-size: 28rpx;
								color: #000;
								font-weight: bold;
							}
						}

						.num {
							display: flex;
							height: 40rpx;
							margin-right: 20rpx;

							.add {
								display: flex;
								justify-content: center;
								align-items: center;
								width: 60rpx;
								height: 40rpx;
								background-color: #ffffff;

								text {
									color: #212121;
									font-size: 24rpx;
								}
							}

							.number {
								display: flex;
								justify-content: center;
								align-items: center;
								width: 80rpx;
								height: 40rpx;
								background-color: #f6f6f6;
								border-radius: 8rpx;
								text-align: center;

								text {
									font-size: 24rpx;
									color: #212121;
								}
							}
						}
					}
				}
			}
		}
	}
}

/* 结算 */
.close-account {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 130rpx;
	background-color: #ffffff;
	border-top: 2rpx solid #f6f6f6;
	z-index: 99;

	.account {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-right: 4%;
		margin-top: 20rpx;

		.btn-calculate {
			display: flex;
			justify-content: center;
			align-items: center;
			flex: 1;
			margin: 0 30rpx;
			height: 60rpx;
			background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
			border-radius: 60rpx;

			text {
				color: #ffffff;
				font-size: 24rpx;
			}
		}
	}
}

.icon-subtract {
	position: relative;
	margin: 0 20rpx;

	&::after {
		content: '';
		display: block;
		width: 25rpx;
		height: 5rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}
}

.icon-add {
	position: relative;
	margin: 0 20rpx;

	&::after {
		content: '';
		display: block;
		width: 25rpx;
		height: 5rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}

	&::before {
		content: '';
		display: block;
		width: 5rpx;
		height: 25rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}
}

.check-icon {
	width: 35rpx;
	height: 35rpx;
	border: 1px solid #999999;
	border-radius: 50%;

	&.active {
		border: 1px solid #e5452f;
		color: #e5452f !important;
		line-height: 30rpx;
	}
}

.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 210rpx;
}
</style>
