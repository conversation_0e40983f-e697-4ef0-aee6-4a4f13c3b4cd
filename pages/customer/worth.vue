<template>
	<view class="customer-main">
		<view class="wrapper">
			<view class="main_info">
				<view class="input-content">
					<view class="top-box">
						<view class="tui-card">
							<!-- <view class="tui-card-header-left">打印数量统计</view> -->
							<view class="list-header">
								<view class="cell" style="flex: 2">黑白印量区间</view>
								<view class="cell" style="flex: 1">包月价</view>
								<view class="cell" style="flex: 2">彩色印量区间</view>
								<view class="cell" style="flex: 1">包月价</view>
							</view>
							<view class="list-list" v-for="(item, index) in infoList" :key="index">
								<view class="cell" style="flex: 2">{{ item.startCount }}-{{ item.endCount }}万</view>
								<view class="cell" style="flex: 1">{{ item.price }}元/月</view>
								<view class="cell" style="flex: 2">{{ item.startColorCount }}-{{ item.endColorCount }}万</view>
								<view class="cell" style="flex: 1">{{ item.colorPrice }}元/月</view>
							</view>
							<view v-if="infoList.length === 0">
								<rf-empty :info="'暂无合约记录'"></rf-empty>
							</view>
						</view>
					</view>
				</view>
				<view class="input-content">
					<view class="input-item">
						<text class="title require">开始时间</text>
						<view class="textarea-content">
							<uni-datetime-picker type="datetime" class="data-select" :clear-icon="true" v-model="params.signTime" placeholder="请选择合约开始时间" />
						</view>
					</view>
					<view class="input-item">
						<text class="title require">预付包月费</text>
						<view class="textarea-content">
							<input v-model="params.prepayment" placeholder="请输入预付包月费" />
						</view>
					</view>
				</view>
				<view class="remember" @tap="handleProtocol">
					<text class="iconfont" :class="agreeProtocol ? `text-${themeColor.name} iconradiobox` : 'iconradioboxblank'"></text>
					<view class="protocol">
						<text class="content">请仔细阅读</text>
						<text :class="'text-' + themeColor.name" @tap.stop="showProtocol">《维修包月协议》</text>
					</view>
				</view>
				<view style="text-align: center">
					<button class="confirm-btn" :disabled="btnLoading" :loading="btnLoading" @tap.stop="handleSubmit">确认合同</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { addContractRecordApi, getPriceListApi } from '@/api/custom.js';
export default {
	data() {
		return {
			customerId: '',
			agreeProtocol: false,
			btnLoading: false,
			infoList: [],
			params: {
				signTime: '',
				prepayment: ''
			}
		};
	},
	onLoad(options) {
		this.customerId = options.id;
		this.getContractRecord();
	},
	methods: {
		getContractRecord() {
			getPriceListApi()
				.then((res) => {
					this.infoList = res.data;
				})
				.catch((err) => {
					this.$mHelper.toast(err || '获取合约记录失败');
				});
		},
		handleProtocol() {
			// console.log('记住我');
			this.agreeProtocol = !this.agreeProtocol;
		},
		showProtocol() {
			uni.navigateTo({ url: '/pages/customer/monthProtocol?id=' + this.customerId });
		},
		handleSubmit() {
			if (!this.agreeProtocol) {
				uni.showToast({
					title: '请先阅读并同意协议',
					icon: 'none'
				});
				return;
			}
			const { signTime, prepayment } = this.params;
			if (!signTime) {
				uni.showToast({
					title: '请选择合约开始时间',
					icon: 'none'
				});
				return;
			}
			if (!prepayment) {
				uni.showToast({
					title: '请输入预付包月费',
					icon: 'none'
				});
				return;
			}
			const arg = {
				...this.params,
				customerId: this.customerId,
				contractName: '包月维修合同',
				contractType: 1205
			};
			this.btnLoading = true;
			addContractRecordApi(arg)
				.then((res) => {
					this.$mHelper.toast('提交成功');
					setTimeout(() => {
						uni.navigateTo({
							url: `/pages/customer/agreement?id=${this.customerId}`
						});
					}, 300);
				})
				.catch((err) => {
					this.$mHelper.toast(err || '提交失败');
				})
				.finally(() => {
					this.btnLoading = false;
				});
		}
	}
};
</script>

<style lang="scss" scoped>
.remember {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
	padding: 50rpx 0;

	.content {
		margin: 0 4rpx 0 10rpx;
	}
}
.confirm-btn {
	width: 80%;
	margin: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
	border-radius: 50px;
	color: #fff;
	margin: auto;
}
</style>
