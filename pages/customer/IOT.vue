<template>
	<view class="customer-main">
		<view class="wrapper">
			<view class="main_info">
				<view class="input-content">
					<view class="input-item">
						<text class="title">服务器版本</text>
						<input class="textarea-content" placeholder="请输入服务器版本" v-model="params.serVersion" type="text" />
					</view>
					<view class="input-item">
						<text class="title">客户端版本</text>
						<input class="textarea-content" placeholder="请输入客户端版本" v-model="params.cliVersion" type="text" />
					</view>
					<view class="input-item">
						<text class="title">安装日期</text>
						<view class="textarea-content">
							<uni-datetime-picker type="date" :clear-icon="true" v-model="params.regDate" placeholder="请选择安装日期" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">更新日期</text>
						<view class="textarea-content">
							<uni-datetime-picker type="date" :clear-icon="true" v-model="params.upDate" placeholder="请选择更新日期" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">上报计数器时间</text>
						<view class="textarea-content">
							<u-number-box v-model="params.uploadCounterTime" :inputWidth="60" :min="0"></u-number-box>
							分钟
						</view>
					</view>
					<view class="input-item">
						<text class="title">上报异常时间</text>
						<view class="textarea-content">
							<u-number-box v-model="params.uploadExceptionTime" :inputWidth="60" :min="0"></u-number-box>
							分钟
						</view>
					</view>
					<view class="input-item">
						<text class="title">上报粉量时间</text>
						<view class="textarea-content">
							<u-number-box v-model="params.uploadPowerTime" :inputWidth="60" :min="0"></u-number-box>
							分钟
						</view>
					</view>
					<view class="input-item">
						<text class="title">查询基本信息时间</text>
						<view class="textarea-content">
							<u-number-box v-model="params.uploadMachineMsgTime" :inputWidth="60" :min="0"></u-number-box>
							分钟
						</view>
					</view>
					<view class="input-item">
						<text class="title">检查更新</text>
						<view class="textarea-content">
							<u-number-box v-model="params.checkServiceTime" :inputWidth="60" :min="0"></u-number-box>
							分钟
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="footer">
			<button class="confirm-btn" :disabled="btnLoading" :loading="btnLoading" @tap.stop="confirmSubmit">保存</button>
		</view>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>
<script>
import { getCustomerIotSetting, updateCustomerIotSetting } from '@/api/custom.js';
export default {
	data() {
		return {
			// 提交按钮动画
			btnLoading: false,
			customerId: '',
			loading: true,
			params: {}
		};
	},
	onShow() {},
	async onLoad(options) {
		this.customerId = options.id || '';
		this.initData();
	},
	methods: {
		initData() {
			this.loading = true;
			getCustomerIotSetting(this.customerId)
				.then((res) => {
					this.params = res.data || {};
				})
				.catch((err) => {
					uni.showToast({
						title: err || '系统出出错啦，请稍后再试',
						icon: 'none'
					});
				})
				.finally(() => {
					this.loading = false;
				});
		},
		confirmSubmit() {
			this.loading = true;
			const params = { ...this.params, customerId: this.customerId };
			updateCustomerIotSetting(params)
				.then((r) => {
					uni.showToast({
						title: r.message || '保存成功',
						icon: 'success'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 300);
				})
				.catch((err) => {
					uni.showToast({
						title: err || '保存失败',
						icon: 'none'
					});
				})
				.finally(() => {
					this.loading = false;
				});
		}
	}
};
</script>
<style lang="scss" scoped></style>
