<template>
	<view class="customer-main">
		<view class="wrapper">
			<view class="main_info">
				<view class="input-content" style="padding: 10rpx">
					<view class="top-box">
						<view class="tui-card">
							<!-- <view class="tui-card-header-left">打印数量统计</view> -->
							<view class="list-header">
								<view class="cell" style="flex: 1">合约类型</view>
								<!-- <view class="cell" style="flex: 2">摘要</view> -->
								<view class="cell" style="flex: 1.5">签约时间</view>
								<!-- <view class="cell" style="flex: 0.8">签约人</view> -->
								<view class="cell" style="flex: 0.8">状态</view>
							</view>
							<view class="list-list" v-for="(item, index) in infoList" :key="index">
								<view class="cell" style="flex: 1">{{ item.contractType.label }}</view>
								<!-- <view class="cell cells" style="flex: 2" @tap.stop="showContent(item.remark)">{{ item.remark }}</view> -->
								<view class="cell" style="flex: 1.5">{{ item.signTime }}</view>
								<view class="cell" style="flex: 0.8">{{ item.status.label }}</view>
							</view>
							<view v-if="infoList.length === 0">
								<rf-empty :info="'暂无合约记录'"></rf-empty>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="button-contents" style="gap: 10rpx">
			<button class="btn" @tap.stop="toPage('')">购机签约</button>
			<button class="btn" @tap.stop="toPage('')">租赁签约</button>
			<button class="btn" @tap.stop="toPage('/pages/customer/worth')">维修包月</button>
			<button class="btn" @tap.stop="toPage('')">全保签约</button>
			<button class="btn" @tap.stop="toPage('')">半包签约</button>
		</view>
		<u-modal :show="show" :content="content" closeOnClickOverlay @confirm="show = false" @close="show = false"></u-modal>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
import { getContractRecordApi } from '@/api/custom';
export default {
	data() {
		return {
			// 提交按钮动画
			btnLoading: false,
			customerId: '',
			loading: false,
			params: {},
			customerId: '',
			editType: 'edit',
			infoList: [],
			show: false,
			content: ''
		};
	},
	onLoad(options) {
		this.customerId = options.id;
		this.getIntention();
	},
	methods: {
		getIntention() {
			const params = {
				pageSize: 99,
				pageNumber: 1,
				customerId: this.customerId
			};
			getContractRecordApi(params).then((res) => {
				this.infoList = res.data.rows;
			});
		},
		showContent(content) {
			this.content = '';
			this.content = content;
			this.show = true;
		},
		toPage(page) {
			console.log(page);
			if (page) {
				this.$mRouter.push({
					route: `${page}?id=${this.customerId}`
				});
			} else {
				this.$mHelper.toast('功能正在开发中!!!');
			}
		}
	}
};
</script>

<style lang="scss" scoped></style>
