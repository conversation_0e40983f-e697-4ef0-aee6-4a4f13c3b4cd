<template>
	<view class="main">
		<view class="content" v-if="brandList.length != 0">
			<view class="staff-info" v-for="(item, index) in brandList" :key="index" @tap.stop="edtitMachineInfo(item.id, item.productId)">
				<view class="base-info">
					<img class="staff-head" :src="item.deviceGroupImg.url ? item.deviceGroupImg.url : '../../static/images/top.png'" alt="" />
					<view class="item-right">
						<view class="name-info">
							<view class="staff-name">
								{{ item.deviceGroup.label }}
								<view class="">{{ item.status ? '使用中' : '已停用' }}</view>
							</view>
						</view>
						<view class="edit">
							<view class="">{{ item.productInfo }}</view>
							<view class="del" :class="item.status ? 'isopen' : ''" @click.stop="delInfoFn(item, index)">
								{{ item.status ? '停用该机器' : '启用该机器' }}
							</view>
						</view>
					</view>
				</view>
				<view class="item-btn">
					<view class="btn" @click.stop="handleBtnClick('基础信息', item)">基础信息</view>
					<view class="btn" @click.stop="handleBtnClick('设备数据', item)">设备数据</view>
					<view class="btn" @click.stop="handleBtnClick('选配件信息', item)">选配件信息</view>
					<view class="btn" @click.stop="handleBtnClick('合约信息', item)">合约信息</view>
					<view class="btn" @click.stop="handleBtnClick('装机记录', item)">装机记录</view>
					<view class="btn" @click.stop="handleBtnClick('印量记录', item)">印量记录</view>
					<view class="btn" @click.stop="handleBtnClick('故障记录', item)">故障记录</view>
					<view class="btn" @click.stop="handleBtnClick('保养记录', item)">保养记录</view>
					<view class="btn" @click.stop="handleBtnClick('维修记录', item)">维修记录</view>
					<view class="btn" @click.stop="handleBtnClick('换件记录', item)">换件记录</view>
					<view class="btn" @click.stop="handleBtnClick('PM件提醒', item)">PM件提醒</view>
					<view class="btn" @click.stop="handleBtnClick('保养提醒', item)">保养提醒</view>
					<view class="btn" @click.stop="handleBtnClick('单张成本', item)">单张成本</view>
				</view>
			</view>
			<view class="loading" v-if="brandList.length !== 0 && loading == false">{{ totast }}</view>
		</view>
		<view class="button-content">
			<view class="button button-left" @tap.stop="doScan">绑定机器</view>
			<view class="button button-right" @click="addInfoFn()">新增设备</view>
		</view>
		<!-- 暂无员工信息 -->
		<rf-empty :info="'暂无设备信息'" v-if="brandList.length === 0"></rf-empty>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="loadingInit"></rfLoading>
	</view>
</template>

<script>
import { getCustomerDeviceList, updateDeviceStatus } from '@/api/custom.js';
import { scanQr } from '@/api/index.js';
export default {
	data() {
		return {
			brandList: [], //列表数据
			customerId: '',
			brandListParms: {
				customerId: '',
				pageNumber: 1,
				pageSize: 10
			},
			brandCheck: [],
			// 关联品牌
			brandParms: {
				pageNumber: 1, // 页码
				pageSize: 9999, // 页大小
				desc: false, // 是否倒叙排序 true:倒叙(DESC) false:升序(ASC)
				fullIdPath: null, // 品牌/产品树/系列id全路径（1、2、3级分组）
				name: null, // 	机型/机型全称
				orderBy: null // 排序字段名称
			},
			loading: false,
			loadingInit: false,
			total: 0, //总数
			totast: '' // —— 到底啦 ——
		};
	},
	onLoad(options) {
		// this.brandListParms.customerId = options.id;
		this.customerId = options.id;
		this.refresh();
	},
	// 下拉刷新
	onPullDownRefresh() {
		this.refresh();
		uni.stopPullDownRefresh();
	},
	onReachBottom() {
		if (this.brandList.length == this.total) {
			this.totast = '—— 到底啦 ——';
			this.loading = false;
			return;
		}
		this.brandListParms.pageNumber++;
		this.getCustomersMachine();
	},
	methods: {
		handleBtnClick(type, item) {
			const { customerId } = this;
			const params = encodeURIComponent(JSON.stringify(item));
			const baseRoute = (path) => `/pages/customer/deviceData/${path}?id=${item.id}&customerId=${customerId}&deviceSeqId=${item.deviceSeqId}&productInfo=${item.productInfo}`;
			const routeMap = {
				基础信息: `/pages/customer/customerMachineInfo?id=${item.id}&pruductId=${item.productId}&type=edit`,
				设备数据: ``,
				合约信息: ``,
				装机记录: ``,
				印量记录: ``,
				故障记录: baseRoute('faultRecord'),
				保养记录: baseRoute('maintain'),
				维修记录: baseRoute('repairRecord'),
				换件记录: baseRoute('changeRecord'),
				PM件记录: baseRoute('PM'),
				单张成本: ``
			};
			const route = routeMap[type];
			if (route) {
				uni.navigateTo({
					url: route
				});
			} else {
				uni.showToast({
					title: '功能完善中，敬请期待',
					icon: 'none'
				});
			}
		},
		doScan() {
			uni.scanCode({
				onlyFromCamera: true,
				success: (res) => {
					// debugger;
					console.log('扫描二维码成功,结果:' + res.result);
					console.log(res.result.includes('{'));
					if (res.result.includes('{')) {
						this.uid = JSON.parse(res.result).data;
						if (JSON.parse(res.result).type == 'install') {
							this.$http
								.get(`${scanQr}`, {
									uid: this.uid
								})
								.then((res1) => {
									console.log(res1);
									if (res1.code == 200 && res1.data.first) {
										this.$mRouter.push({
											route: `/pages/system/scando?uid=${this.uid}`
										});
									} else {
										this.$mRouter.push({
											route: `/pages/system/scan?type=5&msg=${res1.data.second}`
										});
									}
								});
						} else {
							// debugger;
							this.$mRouter.push({
								route: `/pages/system/scan?type=2`
							});
						}
					} else {
						debugger;
						this.$mRouter.push({
							route: `/pages/system/scan?type=4`
						});
					}
				},
				error: (res) => {
					debugger;
					this.$mRouter.push({
						route: `/pages/system/scan?type=3`
					});
				}
			});
		},
		// 获取列表
		getCustomersMachine() {
			this.totast = '加载中...';
			getCustomerDeviceList({ ...this.brandListParms, customerId: this.customerId })
				.then((res) => {
					this.loading = true;
					this.brandListParms.pageNumber++;
					this.brandList = [...this.brandList, ...res.data.rows];
					this.total = res.data.total;
				})
				.catch((err) => {
					uni.showToast({
						title: err || '系统出错啦，请稍后再试！',
						icon: 'none'
					});
				})
				.finally(() => {
					this.loadingInit = false;
				});
		},
		// 修改机器信息
		edtitMachineInfo(id, pruductId) {
			uni.navigateTo({
				url: `/pages/customer/customerMachineInfo?id=${id}&pruductId=${pruductId}&type=edit`
			});
		},
		// 去新增
		addInfoFn() {
			let route = `/pages/customer/customerMachineInfo?&type=add&customerId=${this.customerId}`;
			uni.navigateTo({
				url: route
			});
		},
		// 删除
		async delInfoFn(item, index) {
			let params = {
				customerId: item.customerId,
				deleted: 0,
				id: item.id,
				productId: item.productId,
				status: !item.status
			};
			uni.showModal({
				content: `确认${item.status ? '禁用' : '启用'}该设备？`,
				success: (res) => {
					if (res.confirm) {
						this.loadingInit = true;
						updateDeviceStatus(params).then((res) => {
							this.loadingInit = false;
							this.brandList[index].status = !item.status;
							uni.showToast({
								title: item.status ? '已启用' : '已禁用',
								icon: 'none'
							});
						});
					} else {
						console.log('cancel'); //点击取消之后执行的代码
					}
				}
			});
		},
		refresh() {
			this.brandList = [];
			this.brandCheck = [];
			this.brandListParms.pageNumber = 1;
			this.getCustomersMachine();
			// this.brandCheckFn();
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	width: 100%;
	min-height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;
	padding-bottom: 136rpx;
	box-sizing: border-box;

	.content {
		width: 100%;
		min-height: calc(100vh - 136rpx);
		padding: 22rpx 22rpx 0;
		display: flex;
		flex-direction: column;

		.staff-info {
			width: 100%;
			display: flex;
			flex-direction: column;
			background-color: #fff;
			margin-top: 20rpx;
			border-radius: 8rpx;
			padding: 20rpx;
			.base-info {
				width: 100%;
				min-height: 20rpx;
				padding-bottom: 20rpx;
				display: flex;
				align-items: center;
				font-size: 28rpx;
				color: #000;
				margin-bottom: 20rpx;
				.staff-head {
					width: 130rpx;
					height: 130rpx;
					border-radius: 8rpx;
					margin-right: 15rpx;
				}
				.item-right {
					flex: 1;
					min-height: 130rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					.edit {
						display: flex;
						justify-content: space-between;
						align-items: center;
						.del {
							color: #e5452f;
							border: 1px solid #e5452f;
							padding: 4rpx 12rpx;
							font-size: 22rpx;
							border-radius: 10px;
						}
						.isopen {
							color: #000 !important;
							border: 1px solid #000 !important;
						}
					}

					.name-info {
						width: 100%;
						display: flex;
						align-items: center;
						justify-content: space-between;

						.staff-name {
							flex: 1;
							font-size: 32rpx;
							display: flex;
							align-items: center;
							justify-content: space-between;
						}

						.staff-post {
							color: #666;
						}
					}

					.staff-phone {
						width: 100%;
						display: flex;
						align-items: center;
						justify-content: space-between;
					}
				}
			}
			.item-btn {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				gap: 15rpx;
				.btn {
					justify-items: flex-end;
					height: 60rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					background: linear-gradient(-45deg, #f89030 0%, #fd4e19 100%);
					border-radius: 7rpx;
					font-size: 27rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #fff;
					text-align: center;
					line-height: 46rpx;
					// margin-bottom: 12rpx;
					// margin-right: 2%;
					width: 22%;
				}
			}
		}

		.loading {
			height: 80upx;
			line-height: 80upx;
			text-align: center;
			color: #ccc;
			font-size: 24upx;
			width: 100%;
			margin-bottom: 20upx;
		}
	}
}

/deep/.input-value {
	padding: 0 !important;
	// height: 80rpx;
	font-size: 12px;
	height: auto !important;
	min-height: 84rpx !important;
}

/deep/.input-value-border {
	border: none;
}

/deep/.selected-list {
	// display: none !important,
	flex-wrap: wrap;

	.selected-item:nth-of-type(2) {
		display: none !important;
	}

	.selected-item:nth-of-type(3) {
		display: none !important;
	}
}

/deep/.text-color {
	font-size: 14px;
}

/deep/.placeholder {
	display: none !important;
}
.button-content {
	width: 100%;
	height: 155rpx;
	background: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	position: fixed;
	padding: 0 20rpx;
	bottom: 0;
	left: 0;
	z-index: 9999;

	.button {
		width: 360rpx;
		height: 75rpx;
		font-size: 31rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
	}

	.button-left {
		background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
		border-radius: 37rpx 0rpx 0rpx 37rpx;
	}

	.button-right {
		background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
		border-radius: 0rpx 37rpx 37rpx 0rpx;
	}
}
</style>
