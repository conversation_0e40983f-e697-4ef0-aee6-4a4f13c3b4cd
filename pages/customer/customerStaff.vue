<template>
	<view class="main">
		<view class="content" v-if="staffList.length != 0">
			<view class="staff-info" v-for="(item, index) in staffList" :key="index" @tap.stop="editEmployeeInfo(item)">
				<img class="staff-head" src="@/static/images/top.png" alt="" />
				<view class="item-right">
					<view class="name-info">
						<view class="staff-name">{{ item.name }}</view>
						<view class="staff-post">{{ item.role.label }}</view>
					</view>
					<view class="staff-phone">
						<view class="telnumber">
							{{ item.tel }}
						</view>
						<view class="delStaff" @tap.stop="delStaff(item.id)">去掉员工</view>
						<view class="tel_button">
							<view class="del" :class="item.status ? 'isopen' : ''" v-if="item.id != staffId" @click.stop="delInfoFn(item, index)">
								{{ item.status ? '在职' : '离职' }}
							</view>
							<view class="del1" v-else>本人</view>
						</view>
					</view>
				</view>
			</view>
			<view class="loading" v-if="staffList.length !== 0 && loading == false">{{ totast }}</view>
		</view>
		<view class="footer">
			<button class="confirm-btn" @click="addInfoFn(`/pages/customer/customerStaffInfo?type=add&customerId=${staffParms.customerId}`)">增加员工</button>
		</view>
		<!-- 暂无员工信息 -->
		<rf-empty :info="'暂无员工信息'" v-if="staffList.length === 0 && !loading"></rf-empty>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="loadingInit"></rfLoading>
	</view>
</template>

<script>
import { customerMemberApi, editCustomerMemberApi, deleteCustomerMemberApi } from '@/api/custom.js';
export default {
	data() {
		return {
			staffList: [], //列表数据
			staffId: uni.getStorageSync('userInfo').staffId, // 判断是否是本人
			staffParms: {
				customerId: '',
				desc: false,
				orderBy: '',
				pageNumber: 1,
				pageSize: 10
			},
			loadingInit: true,
			loading: false,
			total: 0, //总数
			totast: '' // —— 到底啦 ——
		};
	},
	onShow() {
		// this.$mHelper.loadVerify();
		this.refresh();
	},
	onLoad(options) {
		this.staffParms.customerId = options.id;
	},
	// 下拉刷新
	onPullDownRefresh() {
		this.refresh();
		uni.stopPullDownRefresh();
	},
	onReachBottom() {
		if (this.staffList.length == this.total) {
			this.totast = '—— 到底啦 ——';
			this.loading = false;
			return;
		}
		this.staffParms.pageNumber++;
		this.customersStaff();
	},
	methods: {
		// 获取列表
		customersStaff() {
			this.totast = '加载中...';
			customerMemberApi(this.staffParms)
				.then((res) => {
					if (res.code === 200) {
						this.loading = true;
						this.loadingInit = false;
						res.data.rows.forEach((item) => {
							this.staffList.push(item);
						});
						this.total = +res.data.total;
					}
				})
				.catch((err) => {
					uni.showToast({
						title: err || '系统出错啦，请稍后再试！',
						icon: 'none'
					});
					this.loadingInit = false;
				});
		},
		// 编辑员工信息
		editEmployeeInfo(data) {
			uni.navigateTo({
				url: `/pages/customer/customerStaffInfo?params=${JSON.stringify(data)}&type=edit&`
			});
		},
		// 删除员工
		delStaff(id) {
			uni.showModal({
				title: '提示',
				content: '确定删除该员工吗？',
				success: (res) => {
					if (res.confirm) {
						deleteCustomerMemberApi(id)
							.then((res) => {
								this.refresh();
								uni.showToast({
									title: '删除成功',
									icon: 'none'
								});
							})
							.catch((err) => {
								uni.showToast({
									title: err || '系统出错啦，请稍后再试！',
									icon: 'none'
								});
							});
					}
				}
			});
		},
		// 去新增
		addInfoFn(route) {
			uni.navigateTo({
				url: route
			});
		},
		// 删除
		async delInfoFn(item, index) {
			let parms = {
				createdAt: '',
				customerId: item.customerId,
				deleted: 0,
				id: item.id,
				name: item.name,
				role: item.role.value,
				status: !item.status,
				tel: item.tel,
				updatedAt: ''
			};
			uni.showModal({
				content: `确认${item.status ? '禁用' : '启用'}该员工？`,
				success: (res) => {
					if (res.confirm) {
						this.loadingInit = true;
						editCustomerMemberApi(parms).then((res) => {
							this.loadingInit = false;
							this.staffList[index].status = !item.status;
							uni.showToast({
								title: item.status ? '已启用' : '已禁用',
								icon: 'none'
							});
						});
					} else {
						console.log('cancel'); //点击取消之后执行的代码
					}
				}
			});
		},
		refresh() {
			this.staffList = [];
			this.staffParms.pageNumber = 1;
			this.customersStaff();
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	width: 100%;
	min-height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;
	padding-bottom: 136rpx;
	box-sizing: border-box;

	.content {
		width: 100%;
		min-height: calc(100vh - 136rpx);
		padding: 22rpx 22rpx 0;
		display: flex;
		flex-direction: column;

		.staff-info {
			width: 100%;
			min-height: 20rpx;
			padding: 20rpx;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			background-color: #fff;
			font-size: 28rpx;
			color: #000;
			margin-bottom: 20rpx;

			.staff-head {
				width: 120rpx;
				height: 120rpx;
				border-radius: 8rpx;
				margin-right: 15rpx;
			}

			.item-right {
				flex: 1;
				height: 120rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.name-info {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.staff-name {
						font-size: 32rpx;
					}

					.staff-post {
						color: #666;
					}
				}

				.staff-phone {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.telnumber {
					}
					.delStaff {
						color: #e5452f;
						border: 1px solid #e5452f;
						padding: 4rpx 12rpx;
						font-size: 22rpx;
						border-radius: 10px;
					}
					.tel_button {
						display: flex;
						align-items: center;
						justify-content: flex-end;

						.del {
							color: #e5452f;
							border: 1px solid #e5452f;
							padding: 4rpx 12rpx;
							font-size: 22rpx;
							border-radius: 10px;
						}

						.isopen {
							color: #000 !important;
							border: 1px solid #000 !important;
						}

						.del1 {
							color: #2a2929;
							border: 1px solid #2a2929;
							padding: 4rpx 12rpx;
							font-size: 22rpx;
							border-radius: 10px;
							margin-right: 18rpx;
						}
					}
				}
			}
		}

		.loading {
			height: 80upx;
			line-height: 80upx;
			text-align: center;
			color: #ccc;
			font-size: 24upx;
			width: 100%;
			margin-bottom: 20upx;
		}
	}

	.footer {
		width: 100%;
		padding: 18rpx 0 42rpx;
		display: flex;
		justify-content: center;
		background-color: #fff;
		position: fixed;
		bottom: 0;
		left: 0;

		.confirm-btn {
			width: 80%;
			margin: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
			border-radius: 50px;
			color: #fff;
		}
	}
}
</style>
