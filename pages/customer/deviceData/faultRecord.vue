<template>
	<view class="customer-main">
		<view class="wrapper">
			<view class="main_info">
				<view class="input-content">
					<view class="input-item">
						<text class="title">机器编号</text>
						<view class="textarea-content">
							<view class="text">D0000000001</view>
						</view>
					</view>
					<view class="input-item">
						<text class="title">机器名称</text>
						<view class="textarea-content">
							<view class="text">理光/Pro 8100S</view>
						</view>
					</view>
					<view class="input-item">
						<text class="title" style="max-width: 200rpx">当前黑白计数器</text>
						<view class="textarea-content">
							<view class="text">111324</view>
						</view>
					</view>
					<view class="input-item">
						<text class="title" style="max-width: 200rpx">当前彩色计数器</text>
						<view class="textarea-content">
							<view class="text">625324</view>
						</view>
					</view>
				</view>
				<view class="input-content" style="padding: 10rpx; overflow: hidden">
					<view class="top-box">
						<view class="tui-card" :style="{ width: infoList && infoList.length > 0 ? '150%' : '100%' }">
							<view class="list-header">
								<view class="cell" style="flex: 1.5">故障</view>
								<view class="cell" style="flex: 2">维修内容</view>
								<view class="cell" style="flex: 1.5">维修时间</view>
								<view class="cell" style="flex: 0.8">维修人</view>
							</view>
							<!-- v-for="(item, index) in infoList" :key="index" -->
							<view class="list-list">
								<view class="cell cells" style="flex: 1.5" @tap.stop="showContent()"></view>
								<view class="cell cells" style="flex: 2" @tap.stop="showContent()"></view>
								<view class="cell" style="flex: 1.5">2024-08-21 15:37:50</view>
								<view class="cell" style="flex: 0.8">杨辉</view>
							</view>
							<view v-if="infoList.length === 0">
								<rf-empty :info="'暂无故障记录'"></rf-empty>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<u-modal :show="show" closeOnClickOverlay @confirm="show = false" @close="show = false">
			<view class="text-content">
				{{ content }}
			</view>
		</u-modal>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
export default {
	data() {
		return {
			id: '',
			customerId: '',
			deviceSeqId: '',
			productInfo: '',
			// 是否到底
			isEnloading: false,
			// 分页参数
			pageNumber: 1,
			// 总数
			total: 0,
			loading: false,
			content: '',
			show: false,
			infoList: []
		};
	},
	onLoad(options) {
		this.id = options.id || '';
		this.customerId = options.customerId || '';
		this.deviceSeqId = options.deviceSeqId || '';
		this.productInfo = options.productInfo || '';
	},
	// 滚动到底部
	onReachBottom() {
		if ((this.pageNumber - 1) * 10 >= this.total) {
			this.isEnloading = true;
			return;
		}
		this.getData();
	},
	methods: {
		showContent(content) {
			if (!content) return;
			this.content = '';
			this.content = content;
			this.show = true;
		},
		getData() {
			this.isloading = true;
			const params = {
				pageNumber: this.pageNumber,
				pageSize: 10
			};

			editApi(params).then((res) => {
				this.pageNumber++;
				this.isloading = false;
				this.isEnloading = false;
				this.infoList = [...this.infoList, ...res.data.rows];
				this.total = res.data.total;
			});
		},
		refresh() {
			this.pageNumber = 1;
			this.infoList = [];
			this.total = 0;
			this.getData();
		}
	}
};
</script>

<style lang="scss" scoped>
.text-content {
	user-select: contain;
}
</style>
