<template>
	<view class="customer-main">
		<view class="wrapper">
			<view class="main_info">
				<view class="input-content" style="flex-direction: column">
					<view class="input-item" style="width: 100%">
						<text class="title">客户编号</text>
						<view class="textarea-content" style="background-color: #fff; justify-content: flex-start">{{ seqId }}</view>
					</view>
					<view class="input-item" style="width: 100%">
						<text class="title">客户名称</text>
						<view class="textarea-content" style="background-color: #fff; justify-content: flex-start">{{ shopName }}</view>
					</view>
				</view>
				<view class="input-content">
					<view class="input-item">
						<text class="title">总消费金额</text>
						<view class="content">{{ params.consumeAmount }}</view>
					</view>
					<view class="input-item">
						<text class="title">总印量</text>
						<view class="content">{{ params.totalPrintNum }}</view>
					</view>
					<!-- 		<view class="input-item">
						<text class="title">应消费金额</text>
						<view class="content">{{params.consumeAmount}}</view>
					</view> -->
					<view class="input-item">
						<text class="title">月消费金额</text>
						<view class="content">{{ params.monthAvgConsume }}</view>
					</view>
					<view class="input-item">
						<text class="title">总访问次数</text>
						<view class="content">{{ params.interviewCount }}</view>
					</view>
					<view class="input-item">
						<text class="title">月均访问次数</text>
						<view class="content">{{ params.monthAvgInterview }}</view>
					</view>
					<!-- 			<view class="input-item">
						<text class="title">月消费金额</text>
						<view class="content">{{params.monthAvgConsume}}</view>
					</view> -->
					<view class="input-item">
						<text class="title">月印量</text>
						<view class="content">{{ params.monthAvgPrint }}</view>
					</view>
					<view class="input-item">
						<text class="title">商城订单数</text>
						<view class="content">{{ params.orderCount }}</view>
					</view>
					<view class="input-item">
						<text class="title">商城订单金额</text>
						<view class="content">{{ params.orderAmount }}</view>
					</view>
					<view class="input-item">
						<text class="title">维修次数</text>
						<view class="content">{{ params.workCount }}</view>
					</view>
					<view class="input-item">
						<text class="title">维修金额</text>
						<view class="content">{{ params.workAmount }}</view>
					</view>
					<!-- 		<view class="input-item">
						<text class="title">月均商城金额</text>
						<view class="content">{{params.monthAvgConsume}}</view>
					</view> -->
					<!-- 			<view class="input-item">
						<text class="title">月均维修金额</text>
						<view class="content">{{params.consumeAmount}}</view>
					</view> -->
					<view class="input-item">
						<text class="title">总故障次数</text>
						<view class="content">{{ params.totalRepair }}</view>
					</view>
					<view class="input-item">
						<text class="title">月均故障次数</text>
						<view class="content">{{ params.monthAvgFault }}</view>
					</view>
					<view class="input-item">
						<text class="title">总搜索次数</text>
						<view class="content">{{ params.searchCount }}</view>
					</view>
					<view class="input-item">
						<text class="title">月均搜索次数</text>
						<view class="content">{{ params.monthAvgSearch }}</view>
					</view>
				</view>
			</view>
		</view>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
import { getCustomerValueApi } from '@/api/custom.js';
export default {
	data() {
		return {
			customerId: '',
			shopName: '',
			seqId: '',
			params: {},
			loading: true
		};
	},
	onLoad(options) {
		const { id, shopName, seqId } = options;
		this.customerId = id;
		this.shopName = shopName;
		this.seqId = seqId;
		this.getCustomerValue();
	},
	methods: {
		getCustomerValue() {
			getCustomerValueApi(this.customerId)
				.then((res) => {
					this.params = res.data;
				})
				.catch((err) => {
					this.$mHelper.toast(err || '系统出错啦，请稍后重试！');
				})
				.finally(() => {
					this.loading = false;
				});
		}
	}
};
</script>

<style scoped lang="scss">
.input-content {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
}
.input-item {
	width: 50%;
	.title {
		max-width: 150rpx !important;
	}
	.content {
		margin-right: 50rpx;
	}
}
.textarea-content {
	width: 100%;
	height: 100rpx;
	background-color: #f5f5f5;
	border-radius: 10rpx;
	padding: 10rpx;
}
</style>
