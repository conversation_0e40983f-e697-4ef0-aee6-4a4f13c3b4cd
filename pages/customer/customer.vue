<template>
	<view class="page">
		<mSearch placeholder="请输入店招名或手机号查询" @search="confirmSearch" @clear="clear" />
		<CustomerList :listData="listData" @showClick="showClick" style="margin-top: 50rpx"></CustomerList>
		<view class="loading" v-if="listData.length !== 0 && loading == false">{{ totast }}</view>
		<view class="footer">
			<button class="confirm-btn" @click="addCustomer">新增客户</button>
		</view>
		<rf-empty :info="'暂无客户信息'" v-if="listData.length === 0"></rf-empty>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
import mSearch from '@/components/rf-search/rf-search.vue';
import CustomerList from './components/customerList.vue';
import { getCustomersListApi } from '@/api/custom.js';
export default {
	components: {
		CustomerList,
		mSearch
	},
	data() {
		return {
			listData: [],
			confirm: [],
			payment: [],
			loading: true,
			pageNumber: 1,
			total: 0,
			totast: '', // —— 到底啦 ——
			keyword: ''
		};
	},
	onShow() {
		this.loading = false;
		this.refresh();
	},
	// 监听下拉刷新事件
	onPullDownRefresh() {
		this.refresh();
		uni.stopPullDownRefresh();
	},
	// TODO：待完善
	onReachBottom() {
		if (this.listData.length == this.total) {
			this.totast = '—— 到底啦 ——';
			this.loading = false;
			return;
		}
		this.getCustomerListInfo();
	},
	methods: {
		// 新增客户
		addCustomer() {
			uni.navigateTo({
				url: './customerInfo?type=add'
			});
		},
		/**
		 * @description 搜索
		 * @param {String} keyword
		 */
		confirmSearch(keyword) {
			this.keyword = keyword;
			this.listData = [];
			this.pageNumber = 1;
			this.getCustomerListInfo();
		},
		clear(keyword) {
			this.keyword = keyword;
			this.listData = [];
			this.pageNumber = 1;
			this.getCustomerListInfo();
		},
		async getCustomerListInfo() {
			getCustomersListApi({
				pageNumber: this.pageNumber,
				pageSize: 10,
				name: this.keyword
			})
				.then((res) => {
					this.pageNumber++;
					this.listData = [...this.listData, ...res.data.rows];
					this.total = res.data.total;
				})
				.catch((err) => {
					this.$mHelper.toast(err || '请求失败');
				})
				.finally(() => {
					this.loading = false;
				});
		},

		showClick(val) {
			this.isTotastShow = true;
			this.isTotastShowVal = val.message;
		},
		refresh() {
			this.listData = [];
			this.pageNumber = 1;
			this.getCustomerListInfo();
		}
	}
};
</script>

<style lang="scss" scoped>
.page {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	background-color: #f6f6f6;
	min-height: 100vh;
	padding: 20rpx;
}
.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	margin-bottom: 20upx;
}
.footer {
	width: 100%;
	padding: 18rpx 0 42rpx;
	display: flex;
	justify-content: center;
	background-color: #fff;
	position: fixed;
	bottom: 0;
	left: 0;

	.confirm-btn {
		width: 80%;
		margin: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
		border-radius: 50px;
		color: #fff;
	}
}
.model-box {
	width: 100%;

	.desc {
		font-size: 27rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #0f0f0f;
		line-height: 48rpx;
		margin: 50rpx 0 30rpx;
		text-align: center;
	}

	.peice {
		width: 100%;
		height: 50rpx;
		line-height: 50rpx;
		padding: 0 10rpx;
		box-sizing: border-box;
		font-size: 27rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #0f0f0f;
	}

	.btn {
		width: 100%;
		margin-top: 28rpx;
		text-align: center;
		height: 74rpx;
		line-height: 74rpx;
		background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
		border-radius: 37rpx;
		font-size: 30rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #ffffff;
	}

	.rfLoading {
		position: fixed;
		top: 0;
		left: 0;
		z-index: 100000000000000;
	}
}
</style>
