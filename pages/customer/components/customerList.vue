<template>
	<view class="device-list-box">
		<view class="list-item" v-for="(item, index) in listData" :key="index" @tap.stop="goCustomerInfo(item)">
			<!-- 左 -->
			<view class="list-left">
				<image
					class="img"
					:src="item.shopRecruitmentImg && item.shopRecruitmentImg[0].url ? item.shopRecruitmentImg[0].url : '../../../static/images/top.png'"
					mode="scaleToFill"
				></image>
			</view>
			<!-- 右 -->
			<view class="list-right">
				<view class="item">
					<view class="name">
						店招名：
						<text>{{ item.name }}</text>
					</view>
					<view class="num">
						手机号：
						<text>{{ item.legalPersonTel || '' }}</text>
					</view>
					<view class="address">
						店铺地址：
						<text>{{ item.address }}</text>
					</view>
				</view>
			</view>

			<!-- 按钮 -->
			<!-- 			<view class="item item-btn">
				<view class="newBtn" @click.stop="handleBtnClick('baseInfo', item)">基础信息</view>
				<view class="newBtn" @click.stop="handleBtnClick('staff', item)">员工信息</view>
				<view class="newBtn" @click.stop="handleBtnClick('business', item)">商务信息</view>
				<view class="newBtn" @click.stop="handleBtnClick('deviceGroup', item)">设备组</view>
				<view class="newBtn" @click.stop="handleBtnClick('IOT', item)">物联网</view>
				<view class="newBtn" @click.stop="handleBtnClick('userTag', item)">用户标签</view>
				<view class="newBtn" @click.stop="handleBtnClick('visitLog', item)">拜访记录</view>
			</view> -->
		</view>
	</view>
</template>

<script>
export default {
	props: {
		listData: {
			type: Array,
			default: []
		}
	},
	data() {
		return {
			routeMap: {
				baseInfo: `/pages/customer/customerInfo`,
				staff: `/pages/customer/customerStaff`,
				business: `/pages/customer/customerBusinessInfo`,
				deviceGroup: `/pages/customer/customerMachine`,
				IOT: `/pages/customer/IOT`,
				userTag: `/pages/customer/userTag`,
				visitLog: `/pages/customer/visitLog`
			}
		};
	},
	methods: {
		goCustomerInfo(item) {
			const params = encodeURIComponent(JSON.stringify(item));
			uni.navigateTo({
				url: `/pages/customer/clientInfo?params=${params}`
			});
		},
		handleBtnClick(type, item) {
			const customerId = item.id;
			const route = this.routeMap[type];
			if (route) {
				this.$mRouter.push({
					route: `${route}?id=${customerId}`
				});
			} else {
				uni.showToast({
					title: '功能完善中，敬请期待',
					icon: 'none'
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.device-list-box {
	width: 100%;
	margin-top: 30rpx;
	padding-bottom: 120upx;
	box-sizing: border-box;

	.list-item {
		width: 100%;
		background-color: #fff;
		margin-bottom: 20rpx;
		box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
		padding: 20rpx;
		box-sizing: border-box;
		display: flex;
		flex-wrap: wrap;
		border-radius: 20rpx;

		.list-left {
			width: 210rpx;
			height: 210rpx;
			flex-grow: 1;
			.img {
				height: 210rpx;
				display: inline-block;
				width: 100%;
				border-radius: 13rpx;
				margin-right: 20rpx;
			}
		}

		.list-right {
			padding-left: 26rpx;
			flex-grow: 1;
			width: calc(100% - 210rpx);
			min-height: 210rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			gap: 50upx;

			.item {
				width: 100%;
				display: flex;
				flex-direction: column;
				gap: 10rpx;
				min-height: 50rpx;
			}

			.name,
			.address {
				font-size: 30rpx;
				font-weight: 500;
				color: #333;
				/* 最多显示两行，多余用省略号展示 */
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
			}
			.address {
				font-size: 28rpx;
			}
			.brand {
				flex: 1;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				font-size: 28rpx;
				color: #535353;
				margin-right: 30rpx;
				font-weight: bold;
			}
		}

		.item-btn {
			display: flex;
			flex: 1;
			flex-wrap: wrap;
			margin-top: 20rpx;
			.newBtn {
				height: 60rpx;
				display: flex;
				justify-content: center;
				justify-items: flex-end;
				align-items: center;
				background: linear-gradient(-45deg, #f89030 0%, #fd4e19 100%);
				border-radius: 7rpx;
				font-size: 27rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #fff;
				text-align: center;
				line-height: 46rpx;
				margin-bottom: 12rpx;
				margin-right: 2%;
				padding: 0 2%;
				min-width: 150rpx;
			}
		}
	}
}
</style>
