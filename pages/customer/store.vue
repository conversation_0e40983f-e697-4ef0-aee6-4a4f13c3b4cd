<template>
	<view class="main">
		<view class="box1">
			<FormQuery :form-columns="formColumns" :is-info="true" :seq-id="seqId" :shop-name="shopName" @search="search" />
		</view>
		<view class="box2">
			<!-- 购物车列表 -->
			<view class="cart-list" v-if="goodsList.length > 0">
				<view class="list" v-for="good in goodsList" :key="good.id">
					<view class="goods">
						<view class="thumb" @tap.stop="previewImage(good.skuInfo.picUrl[0].url)">
							<image :src="good.skuInfo.picUrl[0].url" mode=""></image>
						</view>
						<view class="item">
							<view class="title">
								<text class="two-omit">{{ good.articleName }}</text>
							</view>
							<view class="oem">
								<text class="oem-item">OEM编号：{{ good.oemNumber }}</text>
							</view>
							<view class="attribute">
								<view class="attr">
									<text>{{ good.skuInfo.saleAttrVals.map((item) => item.name + ':' + item.val) }}</text>
								</view>
							</view>
							<view class="price-num">
								<view class="price">
									<text class="min">￥</text>
									<text class="max">{{ good.saleUnitPrice }}</text>
								</view>
								<view class="num" @tap.stop>
									<view class="number">
										<text>{{ good.num }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view v-if="goodsList.length === 0">
				<rf-empty :info="'暂未查询到耗材商品哦'"></rf-empty>
			</view>
			<view class="loading" v-if="!isEnloading && goodsList.length">{{ isloading ? '加载中...' : goodsList.length < total ? '上拉加载更多' : '' }}</view>
			<view class="loading" v-if="goodsList.length > 0 && goodsList.length == total && isEnloading">—— 到底啦 ——</view>

			<!-- 结算 -->
			<view class="close-account">
				<view class="account">
					<view class="btn-calculate" @click="handlePage('/pages/customer/warehousing')">
						<text>入库记录</text>
					</view>
					<view class="btn-calculate" @click="handlePage('/pages/customer/usedRecord')">
						<text>用料记录</text>
					</view>
					<view class="btn-calculate" @click="handlePage('/pages/customer/returnRecord')">
						<text>退料记录</text>
					</view>
					<view class="btn-calculate" @click="handlePage(`/pages/system/receiveList?customerSeqId=${seqId}`)">
						<text>领料审核</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
import { getFilterData, classifyList } from '@/api/index';
import { getCustomerMaterialListApi } from '@/api/custom.js';
import FormQuery from '@/components/formQuery/index';
export default {
	components: { FormQuery },
	data() {
		return {
			seqId: '',
			shopName: '',
			customerId: '',
			// 是否到底
			isEnloading: false,
			// 分页参数
			pageNumber: 1,
			// 总数
			total: 0,
			// 是否加载完成
			isloading: true,
			searchparms: {},
			categoryList: [],
			categoryListId: '',
			classList: [],
			categoryId: '',
			unitList: [],
			unitListId: '',
			loading: true,
			goodsList: [],
			// 图片预览
			previewList: [],
			formColumns: [
				{
					dataIndex: 'product',
					title: '适用机型',
					valueType: 'machine'
				},
				{
					dataIndex: 'categoryId',
					title: '物品分类',
					valueType: 'select',
					localdata: []
				},
				{
					dataIndex: 'categoryListId',
					title: '零件分类',
					valueType: 'select',
					localdata: []
				},
				{
					dataIndex: 'unit',
					title: '所属单元',
					valueType: 'select',
					localdata: []
				},
				{
					dataIndex: 'oem',
					title: 'OEM编号',
					valueType: 'input'
				}
			]
		};
	},
	// 滚动到底部
	onReachBottom() {
		if ((this.pageNumber - 1) * 10 >= this.total) {
			this.isEnloading = true;
			return;
		}
		this.saveFn(); // 商品列表
	},
	onLoad(options) {
		this.customerId = options.id;
		this.seqId = options.seqId;
		this.shopName = options.shopName;
		this.getDataFn();
		this.saveFn(1);
	},
	methods: {
		search(value) {
			this.searchparms = value;
			this.saveFn(1);
		},
		previewImage(url) {
			this.previewList = [];
			this.previewList.push(url);
			uni.previewImage({
				urls: this.previewList,
				current: 0
			});
		},
		getDataFn() {
			this.classList = []; // 商品分类
			this.categoryList = []; // 零件分类
			this.goodsList = [];
			this.unitList = []; // 所属单元
			this.loading = true;
			// 商品分类
			this.$http.get(classifyList, { pageNumber: 1, pageSize: 9999 }).then((res) => {
				res.data.rows.map((el) => {
					this.classList.push({
						value: el.id,
						text: el.name
					});
				});
			});
			this.$http.get(getFilterData).then((res) => {
				res.data.tagList.map((el) => {
					if (el.name == '零件分类') {
						this.categoryListId = el.id;
						el.value.map((a) => {
							this.categoryList.push({
								value: a,
								text: a
							});
						});
					}
					if (el.name == '所属单元') {
						this.unitListId = el.id;
						el.value.map((b) => {
							this.unitList.push({
								value: b,
								text: b
							});
						});
					}
				});
				this.loading = false;
			});
			this.formColumns.forEach((item, index) => {
				if (item.dataIndex === 'categoryId') {
					this.formColumns[index].localdata = this.classList;
				}
				if (item.dataIndex === 'categoryListId') {
					this.formColumns[index].localdata = this.categoryList;
				}
				if (item.dataIndex === 'unit') {
					this.formColumns[index].localdata = this.unitList;
				}
			});
		},
		// 查询
		async saveFn(type) {
			try {
				if (type) {
					this.goodsList = [];
					this.pageNumber = 1;
				}
				this.isloading = true;
				let tags = [];
				if (this.searchparms.categoryListId) {
					tags.push({
						tagId: this.categoryListId,
						values: [this.searchparms.categoryListId]
					});
				}

				if (this.searchparms.unit) {
					tags.push({
						tagId: this.unitListId,
						values: [this.searchparms.unit]
					});
				}
				const param = {
					customerId: this.customerId,
					categoryId: this.searchparms.categoryId,
					oem: this.searchparms.oem,
					productIdList: this.searchparms.productTreeIdList,
					tags: tags,
					pageNumber: this.pageNumber,
					pageSize: 10
				};
				let verify = await getCustomerMaterialListApi(param);
				this.pageNumber++;
				this.isloading = false;
				this.total = verify.data.total;
				this.goodsList = this.goodsList.concat(verify.data.rows);
				if (this.goodsList.length == this.total) {
					this.isEnloading = true;
				}
			} catch (e) {
				this.isloading = false;
				this.$mHelper.toast(e || '系统出错啦，请稍后再试');
			}
		},
		handlePage(route) {
			console.log(route);
			if (route) {
				this.$mRouter.push({
					route: `${route}?id=${this.customerId}&shopName=${this.shopName}&seqId=${this.seqId}`
				});
			} else {
				this.$mHelper.toast('功能正在开发中!');
			}
		}
	}
};
</script>

<style scoped lang="scss">
// .box2 {
// 	margin-top: 780rpx;
// }

.main {
	width: 100vw;
	height: 100vh;
	padding: 0 22rpx;
	box-sizing: border-box;
	background-color: #fff;

	.input-totast {
		width: 100%;
		color: #e5452f;
		display: flex;

		.totast-content {
			flex: 1;
			font-size: 28rpx !important;
		}
	}
}
/* 购物车列表 */
.cart-list {
	width: 100%;
	padding: 20rpx 0;
	// margin-top: 100rpx;

	.list {
		display: flex;
		height: 210rpx;
		background-color: #ffffff;
		box-shadow: 0 0 20rpx #f6f6f6;
		border-radius: 10rpx;

		.goods {
			display: flex;
			align-items: center;
			width: 90%;
			height: 100%;

			.thumb {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 30%;
				height: 100%;

				image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 10rpx;
				}
			}

			.item {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				padding: 10rpx 0;
				width: 70%;
				height: 100%;

				.title {
					display: flex;
					align-items: center;
					width: 100%;
					height: auto;

					text {
						font-size: 26rpx;
						color: #212121;
					}
				}
				.oem {
					text {
						font-size: 24rpx;
						color: #212121;
					}
				}
				.attribute {
					display: flex;
					align-items: center;
					margin-top: 10rpx;

					.attr {
						display: flex;
						align-items: center;
						padding: 0 20rpx;
						height: 40rpx;
						background-color: #f6f6f6;
						border-radius: 10rpx;

						text {
							font-size: 24rpx;
							color: #333333;
						}

						.more {
							display: flex;
							width: 10rpx;
							height: 10rpx;
							border-left: 2rpx solid #333333;
							border-bottom: 2rpx solid #333333;
							transform: rotate(-45deg);
							margin-left: 10rpx;
						}
					}
				}

				.price-num {
					display: flex;
					align-items: center;
					justify-content: space-between;
					height: 45rpx;

					.icon-disable {
						&::after,
						&::before {
							background-color: #ccc;
						}
					}

					.price {
						display: flex;

						.min {
							color: #fe3b0f;
							font-size: 24rpx;
						}

						.max {
							font-size: 28rpx;
							color: #fe3b0f;
							font-weight: bold;
						}
					}

					.num {
						display: flex;
						height: 40rpx;
						margin-right: 20rpx;

						.add {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 60rpx;
							height: 40rpx;
							background-color: #ffffff;

							text {
								color: #212121;
								font-size: 24rpx;
							}
						}

						.number {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 80rpx;
							height: 40rpx;
							background-color: #f6f6f6;
							border-radius: 8rpx;
							text-align: center;

							text {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
				}
			}
		}
	}
}

.background {
	background: #f5f6f8;
}

/* 结算 */
.close-account {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 130rpx;
	background-color: #ffffff;
	border-top: 2rpx solid #f6f6f6;

	.account {
		display: flex;
		align-items: center;
		justify-content: space-between;
		// padding-right: 4%;
		margin-top: 20rpx;
		padding: 0 40rpx;
		gap: 40rpx;

		.btn-calculate {
			display: flex;
			justify-content: center;
			align-items: center;
			flex: 1;
			// margin: 0 30rpx;
			height: 60rpx;
			background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
			border-radius: 60rpx;

			text {
				color: #ffffff;
				font-size: 24rpx;
			}
		}
	}
}

.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 210rpx;
}
</style>
