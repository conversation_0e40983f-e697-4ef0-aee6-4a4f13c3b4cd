<template>
	<view class="customer-main">
		<view class="wrapper">
			<view class="main_info">
				<view class="input-content">
					<view class="input-item">
						<text class="title require">姓名</text>
						<view class="textarea-content">
							<input placeholder="请输入您的姓名" v-model="params.name" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title require">手机号</text>
						<view class="textarea-content">
							<input placeholder="请输入您的手机号" v-model="params.tel" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title require">角色</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="params.role.value" :localdata="roleRange" placeholder="请选择角色"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">账号状态</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="params.status" :localdata="statusRange" placeholder="请选择账号状态"></uni-data-select>
						</view>
					</view>
				</view>

				<view class="input-content">
					<view class="input-item">
						<text class="title">微信ID</text>
						<view class="textarea-content">
							<input placeholder="请输入您的姓名" v-model="params.vxId" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">微信昵称</text>
						<view class="textarea-content">
							<input placeholder="请输入您的姓名" v-model="params.vxNikeName" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">答疑群</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="params.vxGroupName.value" :localdata="vxGroupNameRange" placeholder="请选择答疑群"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">维修能力</text>
						<view class="textarea-content">
							<input placeholder="请输入您的姓名" v-model="params.repairPower" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">评价分数</text>
						<view class="textarea-content">
							<input placeholder="请输入您的姓名" v-model="params.scoreNum" type="text" />
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="footer">
			<button class="confirm-btn" :disabled="btnLoading" :loading="btnLoading" @tap.stop="confirmSubmit">{{ editType === 'edit' ? '保存' : '新增' }}</button>
		</view>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>
<script>
// import { updateEmployeeApi } from '@/api/system';
import { dictTreeByCodeApi, editCustomerMemberApi, addCustomerMemberApi } from '@/api/custom.js';
export default {
	data() {
		return {
			// 提交按钮动画
			btnLoading: false,
			customerId: '',
			loading: true,
			params: {
				role: { value: '', text: '' },
				vxGroupName: { value: '', text: '' }
			},
			roleRange: [],
			statusRange: [
				{ value: 1, text: '在职' },
				{ value: 0, text: '离职' }
			],
			vxGroupNameRange: [],
			editType: 'edit'
		};
	},
	onShow() {},
	async onLoad(options) {
		this.editType = options.type || 'edit';
		this.customerId = options.customerId || '';
		this.params = options.params ? JSON.parse(options.params) : this.params;
		this.params.status = this.params.status ? 1 : 0;
		this.baseData();
	},
	methods: {
		async baseData() {
			this.loading = true;
			const dictApis = [dictTreeByCodeApi(500), dictTreeByCodeApi(2600)];
			try {
				const [roleRangeRes, vxGroupNameRangeRes] = await Promise.all(dictApis);
				this.roleRange = roleRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
				this.vxGroupNameRange = vxGroupNameRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
			} catch (err) {
				console.log(err);
				uni.showToast({
					title: err || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		confirmSubmit() {
			let { editType, params, customerId } = this;
			this.loading = true;
			if (params.name.length === 0) {
				uni.showToast({ title: '请输入姓名', icon: 'none' });
				this.loading = false;
				return;
			}
			const editApt = editType === 'edit' ? editCustomerMemberApi : addCustomerMemberApi;
			params = editType === 'edit' ? { ...params } : { ...params, customerId };
			editApt(params)
				.then((r) => {
					uni.showToast({
						title: r.message || '保存成功',
						icon: 'success'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 300);
				})
				.catch((err) => {
					uni.showToast({
						title: err || '保存失败',
						icon: 'none'
					});
				})
				.finally(() => {
					this.loading = false;
				});
		}
	}
};
</script>
<style lang="scss" scoped></style>
