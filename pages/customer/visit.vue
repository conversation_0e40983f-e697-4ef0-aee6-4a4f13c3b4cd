<template>
	<view class="customer-main">
		<view class="wrapper">
			<view class="main_info" style="padding-bottom: 0">
				<view class="input-content">
					<view class="input-item">
						<view class="title">客户编号</view>
						<view class="textarea-content">
							<view class="text">{{ seqId }}</view>
						</view>
					</view>
					<view class="input-item">
						<view class="title">客户名称</view>
						<view class="textarea-content">
							<view class="text">{{ shopName }}</view>
						</view>
					</view>
				</view>

				<view class="input-content" style="padding: 10rpx">
					<view class="top-box">
						<view class="tui-card">
							<view class="list-header">
								<view class="cell" style="flex: 1.5">访问手机号</view>
								<view class="cell" style="flex: 2">访问页面</view>
								<view class="cell" style="flex: 1">访问时长</view>
								<view class="cell" style="flex: 1.5">访问时间</view>
							</view>
							<view class="list-list" v-for="(item, index) in infoList" :key="index">
								<view class="cell" style="flex: 1.5">{{ item.staffTel }}</view>
								<view class="cell" style="flex: 2" @tap.stop="showContent(item.path)">{{ item.path }}</view>
								<view class="cell" style="flex: 1">{{ item.duration }}</view>
								<view class="cell" style="flex: 1.5" @tap.stop="showContent(item.finishedAt)">{{ item.finishedAt }}</view>
							</view>
							<view v-if="infoList.length === 0">
								<rf-empty :info="'暂无访问记录'"></rf-empty>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<u-modal :show="show" :content="content" closeOnClickOverlay @confirm="show = false" @close="show = false"></u-modal>
		<view class="loading" v-if="infoList.length > 0 && infoList.length == params.total">—— 到底啦 ——</view>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
import { getCustomerVisitRecordApi } from '@/api/custom';
export default {
	data() {
		return {
			seqId: '',
			shopName: '',
			customerId: '',
			loading: true,
			infoList: [],
			params: {
				pageSize: 20,
				pageNumber: 1,
				total: 0,
				customerId: ''
			},
			show: false,
			content: ''
		};
	},
	onLoad(options) {
		this.params.customerId = options.id;
		this.seqId = options.seqId;
		this.shopName = options.shopName;
		this.getIntention();
	},

	// 滚动到底部
	onReachBottom() {
		if ((this.params.pageNumber - 1) * 10 >= this.params.total) {
			return;
		}
		this.getIntention(); // 商品列表
	},
	methods: {
		getIntention() {
			this.loading = true;
			getCustomerVisitRecordApi(this.params)
				.then((res) => {
					this.params.pageNumber++;
					this.infoList = [...this.infoList, ...res.data.rows];
					this.params.total = +res.data.total;
				})
				.catch((error) => {
					uni.showToast({
						icon: 'none',
						title: error || '系统出错啦，请稍后再试！'
					});
				})
				.finally(() => {
					this.loading = false;
				});
		},
		showContent(content) {
			this.content = '';
			this.content = content;
			this.show = true;
		}
	}
};
</script>

<style lang="scss" scoped></style>
