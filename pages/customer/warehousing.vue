<template>
	<view class="main">
		<view class="box1">
			<FormQuery :form-columns="formColumns" :is-info="true" :seq-id="seqId" :shop-name="shopName" @search="search" />
		</view>
		<view class="box2">
			<!-- 购物车列表 -->
			<view class="cart-list" v-if="goodsList.length > 0">
				<view class="list" v-for="goods in goodsList" :key="goods.id">
					<view class="order-info">
						<view class="time">领料单号：{{ goods.operateCode }}</view>
						<view class="time">{{ goods.createdAt }}</view>
					</view>
					<view class="goods">
						<view class="thumb" @tap.stop="previewImage(goods.skuInfo.picUrl[0].url)">
							<image :src="goods.skuInfo.picUrl[0].url" mode=""></image>
						</view>
						<view class="content">
							<view class="item">
								<view class="title">
									<text class="two-omit">{{ goods.articleName }}</text>
								</view>
								<view class="oem">
									<text class="oem-item">OEM编号：{{ goods.oemNumber }}</text>
								</view>
								<view class="attribute" v-if="goods.skuInfo.saleAttrVals">
									<view class="attr">
										<text>{{ goods.skuInfo.saleAttrVals ? goods.skuInfo.saleAttrVals.map((item) => item.name + ':' + item.val) : '' }}</text>
									</view>
								</view>
								<view class="price-num">
									<view class="price">
										<text class="min">￥</text>
										<text class="max">{{ goods.saleUnitPrice }}</text>
									</view>
								</view>
							</view>
							<view class="item-r">
								<view class="num" @tap.stop>
									<view class="number">
										<text>{{ goods.alterEnd }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view v-if="goodsList.length === 0" style="padding-top: 200rpx">
				<rf-empty :info="'暂无入库记录'"></rf-empty>
			</view>
			<view class="loading" v-if="isEnloading && goodsList.length">{{ isloading ? '加载中...' : goodsList.length < total ? '上拉加载更多' : '' }}</view>
			<view class="loading" v-if="goodsList.length > 0 && goodsList.length == total">—— 到底啦 ——</view>
		</view>
	</view>
</template>

<script>
import FormQuery from '@/components/formQuery/index';
import { getCustomerMaterialInListApi } from '@/api/custom.js';
export default {
	components: { FormQuery },
	data() {
		return {
			seqId: '',
			shopName: '',
			customerId: '',
			// 是否到底
			isEnloading: false,
			// 分页参数
			pageNumber: 1,
			// 总数
			total: 0,
			// 是否加载完成
			isloading: true,
			goodsList: [],
			// 图片预览
			previewList: [],
			formColumns: [
				{
					dataIndex: 'operateType',
					title: '领料类型',
					valueType: 'select',
					localdata: [
						{
							text: '商城',
							value: 'MALL'
						},
						{
							text: '外购',
							value: 'PURCHASE'
						}
					]
				},
				{
					dataIndex: 'operateCode',
					title: '领料单号',
					valueType: 'input'
				},
				{
					dataIndex: 'date',
					title: '领料时间',
					valueType: 'datetime',
					inputType: 'daterange'
				},
				{
					dataIndex: 'oem',
					title: 'OEM编号',
					valueType: 'input'
				}
			],
			searchParams: {}
		};
	},
	onLoad(options) {
		this.customerId = options.id;
		this.seqId = options.seqId;
		this.shopName = options.shopName;
	},
	onShow() {
		this.refresh();
	},
	// 滚动到底部
	onReachBottom() {
		if ((this.pageNumber - 1) * 10 >= this.total) {
			this.isEnloading = true;
			return;
		}
		this.getData(); // 商品列表
	},
	onPullDownRefresh() {
		if (this.isloading) return;
		uni.startPullDownRefresh({
			success: () => {
				this.refresh();
			}
		});
		uni.stopPullDownRefresh();
	},
	created() {},
	methods: {
		search(val) {
			this.searchParams = val;
			if (this.searchParams.date) {
				this.searchParams.startDate = val.date[0];
				this.searchParams.endDate = val.date[1];
				delete this.searchParams.date;
			}
			this.refresh();
		},
		previewImage(url) {
			this.previewList = [];
			this.previewList.push(url);
			uni.previewImage({
				urls: this.previewList,
				current: 0
			});
		},
		getData() {
			this.isloading = true;
			let params = {
				...this.searchParams,
				pageNumber: this.pageNumber,
				pageSize: 10,
				customerId: this.customerId
			};
			getCustomerMaterialInListApi(params).then((res) => {
				this.pageNumber++;
				this.isloading = false;
				this.isEnloading = false;
				this.goodsList = [...this.goodsList, ...res.data.rows];
				this.total = res.data.total;
			});
		},
		refresh() {
			this.pageNumber = 1;
			this.goodsList = [];
			this.total = 0;
			this.getData();
		}
	}
};
</script>
<style scoped lang="scss">
.main {
	width: 100%;
	height: 100vh;
	padding: 0 22rpx;
	box-sizing: border-box;
	background-color: #fff;

	// .box2 {
	// 	margin-top: 670rpx;
	// }
	.input-totast {
		width: 100%;
		color: #e5452f;
		display: flex;

		.totast-content {
			flex: 1;
			font-size: 28rpx !important;
		}
	}

	.service {
		width: 50rpx;
		min-height: 80rpx;
		position: fixed;
		right: 0;
		bottom: 200rpx;
		text-align: center;
		border-radius: 8rpx 0 0 8rpx;
		float: right;
		border: 2rpx solid #e5452f;
		border-right: 0;
		color: #e5452f;
	}
}
.icons {
	font-family: iconfont;
	font-size: 32upx;
	font-style: normal;
	color: #999;
}
/* 购物车列表 */
.cart-list {
	width: 100%;
	padding: 20rpx 0;

	.list {
		background-color: #ffffff;
		box-shadow: 0 0 20rpx #f6f6f6;
		border-radius: 10rpx;
		.order-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.goods {
			display: flex;
			align-items: center;
			width: 100%;
			height: 210rpx;
			margin: 10rpx 0;
			.thumb {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 30%;
				height: 100%;
				// margin-top: 20rpx;

				image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 10rpx;
				}
			}

			.content {
				flex: 1;
				display: flex;
				align-items: center;
				.item {
					flex: 2;
					padding: 10rpx 0;
					width: 70%;
					height: 100%;

					.title {
						display: flex;
						align-items: center;
						width: 100%;
						height: auto;

						text {
							font-size: 26rpx;
							color: #212121;
						}
					}
					.oem {
						text {
							font-size: 24rpx;
							color: #212121;
						}
					}
					.attribute {
						display: flex;
						align-items: center;
						margin-top: 10rpx;

						.attr {
							display: flex;
							align-items: center;
							padding: 5rpx 20rpx;
							// height: 40rpx;
							background-color: #f6f6f6;
							border-radius: 10rpx;

							text {
								font-size: 24rpx;
								color: #333333;
							}

							.more {
								display: flex;
								width: 10rpx;
								height: 10rpx;
								border-left: 2rpx solid #333333;
								border-bottom: 2rpx solid #333333;
								transform: rotate(-45deg);
								margin-left: 10rpx;
							}
						}
					}

					.price-num {
						display: flex;
						align-items: center;
						justify-content: space-between;
						height: 45rpx;

						.price {
							display: flex;
							align-items: center;

							.min {
								color: #fe3b0f;
								font-size: 24rpx;
							}

							.max {
								font-size: 28rpx;
								color: #fe3b0f;
								font-weight: bold;
							}
						}
					}
				}
				.item-r {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					gap: 20rpx;
					.num {
						display: flex;
						height: 40rpx;

						.add {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 60rpx;
							height: 40rpx;
							background-color: #ffffff;

							text {
								color: #212121;
								font-size: 24rpx;
							}
						}

						.number {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 80rpx;
							height: 40rpx;
							background-color: #f6f6f6;
							border-radius: 8rpx;
							text-align: center;

							text {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
					.cancel-text {
						font-size: 24rpx;
					}
					.cancel-btn {
						width: 50%;
						height: 45rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						border: none !important;
						color: #fff;
						border-radius: 37px;
						font-size: 25rpx;
						margin: 0;
						background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
					}
				}
			}
		}
	}
}

.background {
	background: #f5f6f8;
}
.time {
	font-size: 24rpx;
	color: #827f7f;
	font-weight: 800;
	padding: 10rpx 0;
}

.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	padding-bottom: 40rpx;
}
</style>
