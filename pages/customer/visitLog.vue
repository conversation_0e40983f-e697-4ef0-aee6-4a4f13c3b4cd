<template>
	<view class="main">
		<view class="content">
			<FormQuery :form-columns="formColumns" @search="search" />
			<!-- 			<view class="staff-info" v-for="(item, index) in visiitLogList" :key="index" @tap.stop="editEmployeeInfo(item)">
				<img class="staff-head" :src="item.callImgs && item.callImgs.length > 0 ? item.callImgs[0].url : '../../static/images/top.png'" alt="" />
				<view class="item-right">
					<view class="name-info">
						<view class="staff-name">{{ item.reachShopName }}</view>
						<view class="staff-post">查看</view>
					</view>
					<view class="staff-phone">
						<view class="telnumber">
							{{ item.callGoal.label }}
						</view>
						<view class="tel_button">
							<view class="del" @click.stop="delInfoFn(item.id)">删除</view>
						</view>
					</view>
				</view>
			</view> -->
			<view class="top-box">
				<view class="tui-card">
					<view class="list-header">
						<view class="cell" style="flex: 2">拜访时间</view>
						<view class="cell">拜访方式</view>
						<view class="cell">拜访人</view>
						<view class="cell">操作</view>
					</view>
					<view class="list-list" v-for="(item, index) in visiitLogList" :key="index">
						<view class="cell" style="flex: 2">{{ item.reachShopTime }}</view>
						<view class="cell">{{ item.callType.label }}</view>
						<view class="cell">{{ item.operatName }}</view>
						<view class="cell" @tap.stop="editEmployeeInfo(item)">查看</view>
					</view>
					<view v-if="visiitLogList.length === 0">
						<rf-empty :info="'暂无拜访记录'"></rf-empty>
					</view>
				</view>
			</view>
			<view class="loading" v-if="visiitLogList.length !== 0 && loading == false">{{ totast }}</view>
		</view>
		<view class="footer">
			<button class="confirm-btn" @click="addInfoFn(`/pages/customer/visitInfo?type=add&customerId=${logParms.customerId}&seqId=${seqId}&shopName=${shopName}`)">
				新增拜访记录
			</button>
		</view>
		<!-- 暂无员工信息 -->
		<rf-empty :info="'暂无员工信息'" v-if="visiitLogList.length === 0 && !loading"></rf-empty>
		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="loadingInit"></rfLoading>
	</view>
</template>

<script>
import { getVisitRecordApi, editCustomerMemberApi, deleteVisitRecordApi } from '@/api/custom.js';
import FormQuery from '@/components/formQuery/index';
export default {
	components: { FormQuery },
	data() {
		return {
			seqId: '',
			shopName: '',
			visiitLogList: [], //列表数据
			staffId: uni.getStorageSync('userInfo').staffId, // 判断是否是本人
			logParms: {
				customerId: '',
				desc: false,
				orderBy: '',
				pageNumber: 1,
				pageSize: 10
			},
			loadingInit: true,
			loading: false,
			total: 0, //总数
			totast: '', // —— 到底啦 ——
			formColumns: [
				{
					dataIndex: 'reachShopTime',
					title: '拜访时间',
					valueType: 'datetime',
					inputType: 'daterange'
				},
				{
					dataIndex: 'optUserName',
					title: '拜访人',
					valueType: 'input'
				}
			]
		};
	},

	onLoad(options) {
		this.logParms.customerId = options.id;
		this.seqId = options.seqId;
		this.shopName = options.shopName;
		this.refresh();
	},
	// 下拉刷新
	onPullDownRefresh() {
		this.refresh();
		uni.stopPullDownRefresh();
	},
	onReachBottom() {
		if (this.staffList.length == this.total) {
			this.totast = '—— 到底啦 ——';
			this.loading = false;
			return;
		}
		this.logParms.pageNumber++;
		this.customersStaff();
	},
	methods: {
		// 获取列表
		customersStaff() {
			this.totast = '加载中...';
			getVisitRecordApi(this.logParms)
				.then((res) => {
					if (res.code === 200) {
						this.loading = true;
						this.loadingInit = false;
						this.logParms.pageNumber++;
						this.visiitLogList = [...this.visiitLogList, ...res.data.rows];
						this.total = +res.data.total;
					}
				})
				.catch((err) => {
					uni.showToast({
						title: err || '系统出错啦，请稍后再试！',
						icon: 'none'
					});
					this.loadingInit = false;
				});
		},
		search(params) {
			console.log(params);
			if (params.reachShopTime) {
				this.logParms.reachShopTime = params.reachShopTime[0];
				this.logParms.reachShopTimeEnd = params.reachShopTime[1];
			} else {
				this.logParms.reachShopTime = '';
				this.logParms.reachShopTimeEnd = '';
			}
			this.logParms.optUserName = params.optUserName;
			this.refresh();
		},
		// 编辑员工信息
		editEmployeeInfo(data) {
			uni.navigateTo({
				url: `/pages/customer/visitInfo?params=${JSON.stringify(data)}&type=edit&seqId=${this.seqId}&shopName=${this.shopName}&id=${this.logParms.customerId}`
			});
		},
		// 去新增
		addInfoFn(route) {
			uni.redirectTo({
				url: route
			});
		},
		// 删除
		async delInfoFn(id) {
			uni.showModal({
				content: `确认删除该条拜访记录？`,
				success: (res) => {
					if (res.confirm) {
						this.loadingInit = true;
						deleteVisitRecordApi(id)
							.then((res) => {
								this.refresh();
								this.loadingInit = false;
								uni.showToast({
									title: '删除成功',
									icon: 'none'
								});
							})
							.catch((err) => {
								uni.showToast({
									title: err || '系统出错啦，请稍后再试！',
									icon: 'none'
								});
							});
					} else {
						console.log('cancel'); //点击取消之后执行的代码
					}
				}
			});
		},
		refresh() {
			this.visiitLogList = [];
			this.logParms.pageNumber = 1;
			this.customersStaff();
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	width: 100%;
	min-height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;
	padding-bottom: 136rpx;
	box-sizing: border-box;

	.content {
		width: 100%;
		min-height: calc(100vh - 136rpx);
		padding: 22rpx 22rpx 0;
		display: flex;
		flex-direction: column;
		.top-box {
			width: 100%;
			display: flex;
			overflow: auto;
			background: #fff;
			margin-top: 20rpx;
			.tui-card {
				flex: 1;
				.list-header {
					width: 100%;
					margin: auto;
					display: flex;
					background: #fff6f3;
					.cell {
						line-height: 80rpx;
						flex: 1;
						text-align: center;
						color: #333;
						font-size: 27rpx;
					}
				}

				.tui-card-header,
				.tui-card-header-left {
					width: 100%;
					font-size: 60rpx;
					line-height: 80rpx;
					text-align: center;
				}

				.tui-card-header {
					font-size: 40rpx;
					font-weight: bold;
					line-height: 60rpx;
				}

				.tui-card-header-left {
					text-align: left;
					font-size: 28rpx;
					line-height: 80rpx;
					font-weight: bold;
					display: flex;
					align-items: center;
					justify-content: space-between;
				}
			}
			.list-list {
				width: 100%;
				margin: auto;
				display: flex;
				align-items: center;
				justify-content: center;
				line-height: 80rpx;
				background: #fff;

				.cell {
					line-height: 80rpx;
					flex: 1;
					text-align: center;
					color: #666;
					font-size: 27rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
				.cells {
					line-height: 40rpx !important;
					display: -webkit-box;
					-webkit-line-clamp: 3; /* 显示的行数 */
					-webkit-box-orient: vertical; /* 设置垂直布局 */
					white-space: normal !important;
					overflow: hidden; /* 隐藏超出的内容 */
					text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
				}
				.text {
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}
		}

		.staff-info {
			width: 100%;
			min-height: 20rpx;
			padding: 20rpx;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			background-color: #fff;
			font-size: 28rpx;
			color: #000;
			margin-bottom: 20rpx;
			margin-top: 300rpx;

			.staff-head {
				width: 120rpx;
				height: 120rpx;
				border-radius: 8rpx;
				margin-right: 15rpx;
			}

			.item-right {
				flex: 1;
				height: 120rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.name-info {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.staff-name {
						font-size: 32rpx;
					}

					.staff-post {
						color: #666;
					}
				}

				.staff-phone {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					.tel_button {
						display: flex;
						align-items: center;
						justify-content: flex-end;

						.del {
							color: #e5452f;
							border: 1px solid #e5452f;
							padding: 4rpx 0;
							font-size: 22rpx;
							padding: 4rpx 12rpx;
							border-radius: 10px;
						}
					}
				}
			}
		}

		.loading {
			height: 80upx;
			line-height: 80upx;
			text-align: center;
			color: #ccc;
			font-size: 24upx;
			width: 100%;
			margin-bottom: 20upx;
		}
	}

	.footer {
		width: 100%;
		padding: 18rpx 0 42rpx;
		display: flex;
		justify-content: center;
		background-color: #fff;
		position: fixed;
		bottom: 0;
		left: 0;

		.confirm-btn {
			width: 80%;
			margin: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
			border-radius: 50px;
			color: #fff;
		}
	}
}
</style>
