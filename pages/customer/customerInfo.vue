<template>
	<view class="customer-main">
		<view class="wrapper">
			<view class="main_info">
				<view class="input-content">
					<view class="input-item" v-if="editType === 'edit'">
						<text class="title">客户编号</text>

						<view class="textarea-content">
							<!-- <input disabled v-model="customerInfo.seqId" type="text" /> -->
							<view class="text">
								{{ customerInfo.seqId }}
							</view>
						</view>
					</view>
					<view class="input-item">
						<text class="title require">店招名</text>
						<view class="textarea-content">
							<input placeholder="请输入您的店招名" v-model="customerInfo.shopRecruitment" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">分店号</text>
						<view class="textarea-content">
							<input placeholder="请输入分店号" v-model="customerInfo.subbranch" type="text" />
						</view>
					</view>
					<view class="input-item" v-if="editType === 'edit'">
						<text class="title">客户来源</text>
						<view class="textarea-content" style="text-align: left; justify-content: flex-start">
							<!-- <uni-data-select class="data-select" disabled v-model="customerInfo.source.value" :localdata="sourceRange" placeholder="请选择客户来源"></uni-data-select> -->
							<view class="text">{{ customerInfo.source ? customerInfo.source.label : '' }}</view>
						</view>
					</view>
					<view class="input-item">
						<text class="title">客户类型</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="customerInfo.type.value" :localdata="TypeRange" placeholder="请选择客户类型"></uni-data-select>
						</view>
					</view>

					<view class="input-item" v-if="editType === 'edit'">
						<text class="title">经营状态</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="customerInfo.businessStatus.value" :localdata="businessStatusRange" placeholder="请选择经营状态"></uni-data-select>
						</view>
					</view>

					<view class="input-item" v-if="editType === 'edit'">
						<text class="title">客户状态</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="customerInfo.status.value" :localdata="statusRange" placeholder="请选择客户状态"></uni-data-select>
						</view>
					</view>
					<view class="input-item" v-if="editType === 'edit'">
						<text class="title">行业属性</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="customerInfo.industryAttr.value" :localdata="industryAttrRange" placeholder="请选择行业属性"></uni-data-select>
						</view>
					</view>
				</view>
				<view class="input-content">
					<view class="input-item">
						<text class="title">法人姓名</text>
						<view class="textarea-content">
							<input v-model="customerInfo.legalPerson" placeholder="请输入法人姓名" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">法人电话</text>
						<view class="textarea-content">
							<input type="number" maxlength="11" auto-height="true" v-model="customerInfo.legalPersonTel" placeholder="请输入法人电话" />
						</view>
					</view>

					<view class="input-item">
						<text class="title">客户等级</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="customerInfo.membershipLevel.value" :localdata="membershipLevelRange" placeholder="请选择客户等级"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">关联集团</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="customerInfo.groupId" :localdata="groupIdRange" placeholder="请选择关联集团"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">店铺定位</text>
						<view class="textarea-content">
							<input class="input-range" style="text-align: center" v-model="customerInfo.longitude" placeholder="请输入经度" />
							<text>-</text>
							<input class="input-range" style="text-align: center" v-model="customerInfo.latitude" placeholder="请输入纬度" />
						</view>
					</view>
					<view class="input-item">
						<text class="title require">所在地区</text>
						<view class="textarea-content" @tap.stop="openMap">
							<textarea auto-height="true" style="width: 85%" disabled :maxlength="-1" placeholder="请选择所在地区" v-model="customerInfo.address" />
							<view class="loc" style="width: 15%">
								<text class="iconfont icon-dingwei"></text>
								<view class="text">定位</view>
							</view>
							<!-- 				<uni-data-picker
								ref="regionRef"
								style="flex: 1"
								:localdata="addressItems"
								:data="regionData"
								placeholder="请选择您店铺的所属地区"
								popup-title="请选择您店铺的所属地区"
								@change="onchange"
								:map="{ text: 'name', value: 'code' }"
							></uni-data-picker> -->
						</view>
					</view>
					<view class="input-item">
						<text class="title">店铺地址</text>
						<view class="textarea-content">
							<input v-model="customerInfo.precisionAddress" placeholder="请输入您店铺的详细地址" />
						</view>
					</view>
					<!-- 				<view class="input-item upload-file">
						<text class="title">店招照片</text>
						<view class="image">
							<view class="more">
								<FileUpload ref="updadeRef" @submit="handleSubmit" :limit="2" :fileList="shopInfoImgList"></FileUpload>
							</view>
						</view>
					</view> -->
				</view>
			</view>
		</view>
		<view class="footer">
			<button class="confirm-btn" :disabled="btnLoading" :loading="btnLoading" @tap="editCustomerInfo">{{ editType === 'add' ? '新增' : '保存' }}</button>
		</view>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>
<script>
import uniDataPicker from '@/components/uni-data-picker/uni-data-picker.vue';
import FileUpload from '@/components/file-upload/index.vue';
import { dictTreeByCodeApi, getCustomerDetail, getAreaInfo, updateCustomerInfo, getCustomerLevelApi, getCustomerGroupApi, addCustomerApi } from '@/api/custom.js';
export default {
	components: { FileUpload, uniDataPicker },
	data() {
		return {
			// 地址选项
			addressItems: [],
			code: null,
			customerInfo: {
				source: { value: '', label: '' },
				type: { value: '', label: '' },
				businessStatus: { value: '', label: '' },
				industryAttr: { value: '', label: '' },
				membershipLevel: { value: 'normal', label: '' },
				status: { value: '', label: '' }
			}, // 客户信息
			// 提交按钮动画
			btnLoading: false,
			loading: true,
			regionData: [],
			shopInfoImgList: [],
			sourceRange: [], //来源
			TypeRange: [], // 客户类型
			type: '',
			businessStatusRange: [], // 经营状态
			statusRange: [], // 客户状态
			businessStatus: '', // 经营状态值
			industryAttrRange: [], // 行业属性
			legalNativePlaceRange: [
				{
					value: 1,
					text: '本地籍'
				},
				{
					value: 2,
					text: '湖南籍'
				},
				{
					value: 3,
					text: '外地籍'
				}
			],
			membershipLevelRange: [], // 客户等级
			groupIdRange: [], // 关联集团
			industryAttr: '',
			customerId: '',
			editType: 'edit'
		};
	},
	async onLoad(options) {
		this.customerId = options.id || '';
		this.editType = options.type || 'edit';
		await this.getAddressInfo();

		if (this.editType === 'edit') {
			await this.initData();
			setTimeout(async () => {
				await this.handleRegion();
			}, 300);
		} else {
			this.loading = false;
			this.customerInfo.industryAttr.value = '301';
			this.customerInfo.source.value = '2306';
			this.customerInfo.businessStatus.value = '201';
			this.customerInfo.status.value = '101';
		}
		await this.baseData();
	},
	methods: {
		async baseData() {
			this.loading = true;
			const dictApis = [
				dictTreeByCodeApi(2300),
				dictTreeByCodeApi(400),
				dictTreeByCodeApi(200),
				dictTreeByCodeApi(100),
				dictTreeByCodeApi(300),
				getCustomerLevelApi(),
				getCustomerGroupApi()
			];
			try {
				const [sourceRangeRes, typeRangeRes, businessStatusRangeRes, statusRangeRes, industryAttrRangeRes, membershipLevelRangeRes, groupIdRangeRes] = await Promise.all(dictApis);
				this.sourceRange = sourceRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
				this.TypeRange = typeRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
				this.businessStatusRange = businessStatusRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
				this.statusRange = statusRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
				this.industryAttrRange = industryAttrRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));

				this.membershipLevelRange = membershipLevelRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
				this.groupIdRange = groupIdRangeRes.data.map((item) => ({
					value: item.id,
					text: item.name
				}));
			} catch (err) {
				uni.showToast({
					title: err || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		async initData() {
			this.loading = true;
			getCustomerDetail(this.customerId)
				.then((res) => {
					this.customerInfo = res.data;
					this.shopInfoImgList = this.customerInfo.shopRecruitmentImg;
					this.code = customerDetailRes.data.regionCode; // 地区编码。
				})
				.catch((err) => {
					uni.showToast({
						title: err || '系统出错啦，请稍后再试！',
						icon: 'none'
					});
				})
				.finally(() => {
					this.loading = false;
				});
			// try {
			// 	// 获取客户信息
			// 	const customerDetailRes = await getCustomerDetail(this.customerId);
			// 	this.customerInfo = customerDetailRes.data || {};
			// 	this.shopInfoImgList = this.customerInfo.shopRecruitmentImg || [];
			// 	this.code = customerDetailRes.data.regionCode || ''; // 地区编码。
			// } catch (err) {
			// 	uni.showToast({
			// 		title: err || '系统出错啦，请稍后再试！',
			// 		icon: 'none'
			// 	});
			// } finally {
			// 	this.loading = false;
			// }
		},

		openMap() {
			const { customerInfo } = this;
			wx.chooseLocation({
				longitude: parseFloat(customerInfo?.longitude) || 104.063077, // 经度，非必填，默认当前位置经度
				latitude: parseFloat(customerInfo?.latitude) || 30.595044, // 纬度，非必填，默认当前位置纬度
				success: (res) => {
					this.$set(customerInfo, 'address', res.address);
					this.$set(customerInfo, 'location', {
						longitude: res.longitude,
						latitude: res.latitude
					});
					this.customerInfo.longitude = res.longitude;
					this.customerInfo.latitude = res.latitude;
				},
				fail: (err) => {
					uni.showToast({
						title: '获取位置失败',
						icon: 'none'
					});
				}
			});
		},
		handleSelectChange(e, type) {
			console.log(e, type);
		},
		getAddressInfo() {
			getAreaInfo().then((res) => {
				this.addressItems = res.data;
			});
		},
		handleRegion() {
			const regionMap = this.buildRegionMap(this.addressItems);
			if (this.code) {
				const region = this.findRegionPath(this.code, regionMap);
				this.$refs.regionRef.inputSelected = region;
			}
		},
		/**
		 * @param {Object} code
		 * @param {Object} map
		 */
		findRegionPath(code, map) {
			return map.get(code.toString()) || null;
		},
		/**
		 * @description 构建区域映射表
		 * @param {Array} regions 区域数据
		 * @param {Array} path 路径
		 * @param {Map} map 映射表
		 */
		buildRegionMap(regions, path = [], map = new Map()) {
			for (const region of regions) {
				const currentPath = [...path, { text: region.name, value: region.code.toString() }];
				map.set(region.code.toString(), currentPath);
				if (region.children) {
					this.buildRegionMap(region.children, currentPath, map);
				}
			}
			return map;
		},
		// 选择地区
		onchange(e) {
			let code = e.detail.value[e.detail.value.length - 1].value;
			if (code) {
				this.customerInfo.regionCode = code;
			}
		},
		editCustomerInfo() {
			const { shopRecruitment, address, legalPersonTel } = this.customerInfo;
			if (shopRecruitment.length < 2) {
				uni.showToast({
					title: '请输入店招名',
					icon: 'none'
				});
				return;
			}
			if (address.length < 2) {
				uni.showToast({
					title: '请输入店铺地址',
					icon: 'none'
				});
				return;
			}
			let obj = {
				...this.customerInfo,
				legalPersonTel: legalPersonTel ? legalPersonTel.replace(/\s/g, '') : ''
				// shopRecruitmentImg: this.shopInfoImgList
			};
			const handleApiRequest = (apiCall, successMessage) => {
				this.btnLoading = true;
				apiCall
					.then((res) => {
						this.$mHelper.toast(successMessage);
						setTimeout(() => {
							uni.navigateTo({
								url: '/pages/customer/customer'
							});
						}, 300);
					})
					.catch((err) => {
						uni.showToast({
							title: err || '系统出错啦，请稍后再试！',
							icon: 'none'
						});
					})
					.finally(() => {
						this.btnLoading = false;
					});
			};

			if (this.editType === 'add') {
				handleApiRequest(addCustomerApi(obj), '添加成功');
			} else if (this.editType === 'edit') {
				handleApiRequest(updateCustomerInfo({ ...obj, id: this.customerId }), '修改成功');
			}
		},
		// 图片上上传
		handleSubmit(data) {
			this.shopInfoImgList = data;
		}
	}
};
</script>
<style lang="scss" scoped>
.loc {
	display: flex;
	flex-direction: column;
	align-items: center;
	line-height: 40rpx;
	.iconfont {
		font-size: 45upx;
	}
	.text {
		font-size: 25upx;
		text-align: center !important;
	}
}

/deep/.uni-data-pickerview {
	.item {
		justify-content: center;
	}

	.check {
		margin-left: 6px;
		margin-right: 0;
		border: 2px solid #e5452f !important;
		border-left: 0 !important;
		border-top: 0 !important;
	}
}
/deep/.selected-item-active {
	border-bottom: 2px solid #e5452f !important;
}

/deep/.uni-data-tree-dialog {
	top: 60% !important;
}

/deep/.active-roles .uni-data-pickerview .selected-area {
	display: none;
}
</style>
