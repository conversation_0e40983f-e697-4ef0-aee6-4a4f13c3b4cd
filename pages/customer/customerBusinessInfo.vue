<template>
	<view class="customer-main">
		<view class="wrapper">
			<view class="main_info">
				<view class="input-content">
					<view class="input-item">
						<text class="title">营业执照名称</text>
						<view class="textarea-content">
							<input type="text" v-model="params.license" placeholder="请输入您的营业执照名称" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">统一信用代码</text>
						<view class="textarea-content">
							<input type="text" v-model="params.creditCode" placeholder="请输入您的统一信用代码" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">开票类型</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="params.openBillType" :localdata="openBillTypeListRange" placeholder="请选择开票类型"></uni-data-select>
						</view>
					</view>
					<!-- 	
					<view class="input-item">
						<text class="title">开户名</text>
						<view class="textarea-content">
							<input type="number" v-model="params.account" placeholder="请输入您的开户名" />
						</view>
					</view> -->
					<view class="input-item">
						<text class="title">开户银行</text>
						<view class="textarea-content">
							<input type="text" v-model="params.bank" placeholder="请输入您的开户银行" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">开户网点</text>
						<view class="textarea-content">
							<input type="text" v-model="params.bankClient" placeholder="请输入您的开户网点" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">银行账号</text>
						<view class="textarea-content">
							<input type="number" v-model="params.bankAccount" @input="handleInput" @blur="onBlur" placeholder="请输入您的银行账号" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">财务姓名</text>
						<view class="textarea-content">
							<input type="text" v-model="params.finance" placeholder="请输入您的财务姓名" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">财务电话</text>
						<view class="textarea-content">
							<input type="text" v-model="params.financeTel" placeholder="请输入您的财务电话" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">收票邮箱</text>
						<view class="textarea-content">
							<input type="text" v-model="params.billEmail" placeholder="请输入您的收票邮箱" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">开票地址</text>
						<view class="textarea-content">
							<input type="text" v-model="params.invoiceAddr" placeholder="请输入您的开户名" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">结算方式</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="params.settleMethod" :localdata="settleMethodRange" placeholder="请选择结算方式"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">财务核算</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="params.financeAccount" :localdata="financeAccountRange" placeholder="请选择财务核算"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">销售人员</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="params.salesmanId" :localdata="salesmanIdRange" placeholder="请选择销售人员"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">技术支持</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="params.businessmanId" :localdata="businessmanIdRange" placeholder="请选择技术支持"></uni-data-select>
						</view>
					</view>

					<!-- 				<view class="input-item">
						<text class="title">入住平台时间</text>
						<view class="textarea-content">
							<uni-datetime-picker type="datetime" :clear-icon="true" v-model="params.regPlatTime" />
						</view>
					</view> -->
					<view class="input-item upload-file">
						<text class="title">银行卡照片</text>
						<view class="image">
							<view class="more">
								<FileUpload ref="updadeRef" @submit="handleBankCard" :limit="1" :fileList="bankCardImgList"></FileUpload>
							</view>
						</view>
					</view>
					<view class="input-item upload-file">
						<text class="title">营业执照</text>
						<view class="image">
							<view class="more">
								<FileUpload ref="updadeRef" @submit="handleLicense" :limit="3" :fileList="licenseImgList"></FileUpload>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="footer">
			<view class="confirm-btn" :disabled="btnLoading" :loading="btnLoading" @tap="editInvoiceInfo">保存</view>
		</view>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>
<script>
import FileUpload from '@/components/file-upload/index.vue';
import { getCustomerBusinessInfo, updateCustomerBusinessInfo, getRoleByCode } from '@/api/custom.js';
import BIN from 'bankcardinfo';
export default {
	components: { FileUpload },
	data() {
		return {
			// 提交按钮动画
			btnLoading: false,
			loading: true,
			bankCardImgList: [],
			licenseImgList: [],
			customerId: '', // 设备ID
			params: {},
			formData: {
				cardNumber: ''
			},
			bankName: '未知银行',
			openBillTypeListRange: [
				{
					value: 1,
					text: '增值税普通发票'
				},
				{
					value: 2,
					text: '增值税专用发票'
				}
			],
			salesmanIdRange: [],
			businessmanIdRange: [],
			settleMethodRange: [
				{
					value: 1,
					text: '现结'
				},
				{
					value: 2,
					text: '月付'
				},
				{
					value: 3,
					text: '第三方支付'
				},
				{
					value: 4,
					text: '预充抵扣'
				}
			],
			financeAccountRange: [
				{
					value: 1,
					text: '单店私账支付'
				},
				{
					value: 2,
					text: '多店私账支付'
				},
				{
					value: 3,
					text: '单店对公支付'
				},
				{
					value: 4,
					text: '多店对公支付'
				}
			]
		};
	},
	async onLoad(options) {
		this.customerId = options.id;
		await this.baseData();
		await this.initData(); // 初始化数据
	},
	methods: {
		async baseData() {
			const dictApis = [getRoleByCode('xiaoshou', { pageNumber: 1, pageSize: 9999 }), getRoleByCode('engineer', { pageNumber: 1, pageSize: 9999 })];
			try {
				const [salesmanIdRangeRes, businessmanIdRangeRes] = await Promise.all(dictApis);
				this.salesmanIdRange = salesmanIdRangeRes.data.rows.map((item) => ({
					value: item.id,
					text: item.name
				}));

				this.businessmanIdRange = businessmanIdRangeRes.data.rows.map((item) => ({
					value: item.id,
					text: item.name
				}));
			} catch (err) {
				uni.showToast({
					title: err || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		handleInput(e) {
			const _this = this;
			let cardNumber = e.detail.value;
			BIN.getBankBin(cardNumber)
				.then((res) => {
					_this.params.bank = res.bankName || '';
				})
				.catch((err) => {
					console.log(err); // 处理错误信息，例如提示用户输入正确的银行卡号等。
					this.params.bank = '';
				});
		},
		onBlur() {
			const { bank } = this.params;
			if (bank == undefined || bank == null || bank == '') {
				uni.showModal({
					title: '提示',
					content: '请确认输入的银行卡号正确，以便自动识别银行信息。如果没有识别出银行信息，请输入开户银行！'
				});
			}
		},

		initData() {
			this.loading = true;
			getCustomerBusinessInfo(this.customerId)
				.then((res) => {
					this.params = res.data;
					if (Object.keys(res.data.bankCardImg).length > 0) {
						this.bankCardImgList.push(res.data.bankCardImg);
					}
					this.licenseImgList = res.data.licenseImg;
				})
				.catch((err) => {
					// uni.showToast({
					// 	title: err.message || '系统错误',
					// 	icon: 'none'
					// });
				})
				.finally(() => {
					this.loading = false;
				});
		},
		editInvoiceInfo() {
			this.loading = true;
			// const { bankAccount } = this.params;
			// if (license.length === 0) {
			// 	uni.showToast({ title: '请输入您的营业执照名称', icon: 'none' });
			// 	this.loading = false;
			// 	return;
			// }
			// if (creditCode.length === 0) {
			// 	uni.showToast({ title: '请输入您的统一信用代码', icon: 'none' });
			// 	this.loading = false;
			// 	return;
			// }
			// if (bank.length === 0) {
			// 	uni.showToast({ title: '请输入您开户银行', icon: 'none' });
			// 	this.loading = false;
			// 	return;
			// }
			// if (bankClient.length === 0) {
			// 	uni.showToast({ title: '请输入您开户网点地址', icon: 'none' });
			// 	this.loading = false;
			// 	return;
			// }
			// const regBankAccount = /^[1-9]\d{9,29}$/;
			// if (!regBankAccount.test(bankAccount)) {
			// 	uni.showToast({ title: '请输入正确的银行账号', icon: 'none' });
			// 	this.loading = false;
			// 	return;
			// }

			const params = {
				...this.params,
				customerId: this.customerId, // 客户ID
				bankCardImg: this.bankCardImgList[0],
				licenseImg: this.licenseImgList
			};
			updateCustomerBusinessInfo(params)
				.then((res) => {
					uni.showToast({ title: '修改成功', icon: 'none' });
					setTimeout(() => {
						uni.navigateBack();
					}, 300);
				})
				.catch((err) => {
					console.log(err);
					uni.showToast({ title: err.message || '修改失败', icon: 'none' });
				})
				.finally(() => {
					this.loading = false;
				});
		},
		// 图片上上传
		handleBankCard(data) {
			this.bankCardImgList = data;
		},
		// 图片上上传
		handleLicense(data) {
			this.licenseImgList = data;
		}
	}
};
</script>
<style lang="scss" scoped></style>
