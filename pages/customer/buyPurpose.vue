<template>
	<view class="customer-main">
		<view class="wrapper">
			<view class="main_info">
				<view class="input-content">
					<view class="input-item">
						<text class="title">客户编号</text>
						<view class="textarea-content">
							<input disabled v-model="seqId" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">店招名</text>
						<view class="textarea-content">
							<input disabled v-model="shopName" type="text" />
						</view>
					</view>
				</view>
				<view class="input-content" style="padding: 10rpx">
					<view class="top-box">
						<view class="tui-card">
							<!-- <view class="tui-card-header-left">打印数量统计</view> -->
							<view class="list-header">
								<view class="cell" style="flex: 1">购买类型</view>
								<view class="cell" style="flex: 2">期望购买内容</view>
								<view class="cell" style="flex: 1.5">登记时间</view>
								<view class="cell" style="flex: 0.8">登记人</view>
							</view>
							<view class="list-list" v-for="(item, index) in infoList" :key="index">
								<view class="cell" style="flex: 1">{{ item.buyType.label }}</view>
								<view class="cell cells" style="flex: 2" @tap.stop="showContent(item.content)">
									{{ item.content }}
								</view>
								<view class="cell" style="flex: 1.5">{{ item.createdAt }}</view>
								<view class="cell" style="flex: 0.8">{{ item.followUpName }}</view>
							</view>
							<view v-if="infoList.length === 0">
								<rf-empty :info="'暂无购买意向'"></rf-empty>
							</view>
						</view>
					</view>
				</view>
				<view class="input-content">
					<view class="input-item">
						<text class="title require">购买类型</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="params.buyType" :localdata="buyType" placeholder="请选择购买类型"></uni-data-select>
						</view>
					</view>
					<view class="textarea-item">
						<text class="title require">期望购买内容</text>
						<view class="textarea-content">
							<textarea auto-height="true" :maxlength="500" placeholder="请输入期望购买内容" v-model="params.content" />
							<view class="max-length">{{ params.content ? params.content.length : 0 }}/500</view>
						</view>
					</view>

					<view class="input-item">
						<text class="title require">期望购买时间</text>
						<view class="textarea-content">
							<uni-datetime-picker type="datetime" class="data-select" :clear-icon="true" v-model="params.intentionTime" placeholder="请选择期望购买时间" />
						</view>
					</view>
					<!-- 				<view class="input-item">
						<text class="title">跟进人</text>
						<view class="textarea-content">
							<input placeholder="请输入跟进人姓名" v-model="params.followUpName" type="text" />
						</view>
					</view> -->
				</view>
			</view>
		</view>
		<view class="button-content">
			<button class="button button-left" :disabled="btnLoading" :loading="btnLoading" @tap.stop="refresh">刷新</button>
			<button class="button button-right" :disabled="btnLoading" :loading="btnLoading" @tap.stop="confirmSubmit">提交数据</button>
		</view>
		<u-modal :show="show" :content="content" closeOnClickOverlay @confirm="show = false" @close="show = false"></u-modal>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
import { getCustomerIntentionApi, addCustomerIntentionApi, dictTreeByCodeApi } from '@/api/custom';
export default {
	data() {
		return {
			seqId: '',
			shopName: '',
			// 提交按钮动画
			btnLoading: false,
			customerId: '',
			loading: true,
			params: {},
			customerId: '',
			buyType: [],
			editType: 'edit',
			infoList: [],
			show: false,
			content: ''
		};
	},
	onLoad(options) {
		const { id, seqId, shopName } = options;
		this.customerId = id;
		this.seqId = seqId;
		this.shopName = shopName;
		this.getIntention();
		this.baseData();
	},
	methods: {
		getIntention() {
			this.loading = true;
			const params = {
				pageSize: 99,
				pageNumber: 1,
				customerId: this.customerId
			};
			getCustomerIntentionApi(params)
				.then((res) => {
					this.infoList = res.data.rows;
				})
				.catch((error) => {
					uni.showToast({
						title: error || '系统出错啦，请稍后再试！',
						icon: 'none'
					});
				})
				.finally(() => {
					this.loading = false;
				});
		},
		async baseData() {
			const dictApis = [dictTreeByCodeApi(4700)];
			try {
				const [buyTypeRes] = await Promise.all(dictApis);
				this.buyType = buyTypeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
			} catch (err) {
				uni.showToast({
					title: err || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		showContent(content) {
			this.content = '';
			this.content = content;
			this.show = true;
		},
		confirmSubmit() {
			const { params } = this;
			if (!params.buyType) {
				uni.showToast({
					title: '请选择购买类型',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			if (!params.content) {
				uni.showToast({
					title: '请输入期望购买内容',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			if (!params.intentionTime) {
				uni.showToast({
					title: '请选择期望购买时间',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			this.btnLoading = true;
			const arg = {
				...this.params,
				customerId: this.customerId
			};

			addCustomerIntentionApi(arg)
				.then((res) => {
					this.refresh();
					this.params = {};
					uni.showToast({
						title: '提交成功',
						icon: 'none',
						duration: 1000
					});
				})
				.finally(() => {
					this.btnLoading = false;
				});
		},
		refresh() {
			this.getIntention();
		}
	}
};
</script>

<style lang="scss" scoped></style>
