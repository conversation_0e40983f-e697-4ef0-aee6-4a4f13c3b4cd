<template>
	<view class="customer-main">
		<view class="wrapper">
			<view class="main_info">
				<view class="input-content">
					<view class="input-item" v-if="editType === 'edit'">
						<text class="title">机器编号</text>
						<view class="textarea-content">
							<!-- <input disabled v-model="params.productId" type="text" /> -->
							<view class="text">
								{{ params.productId }}
							</view>
						</view>
					</view>
					<view class="input-item" v-if="editType === 'edit'">
						<text class="title">设备组名称</text>
						<view class="textarea-content">
							<view class="text">{{ params.deviceGroup.label }}</view>
						</view>
					</view>
					<view class="input-item" v-if="editType === 'edit'">
						<text class="title">品牌机型</text>
						<view class="textarea-content">
							<view class="text">{{ params.productInfo }}</view>
						</view>
					</view>

					<view class="input-item" v-if="editType === 'add'">
						<text class="title">设备组名称</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" placeholder="请选择设备组名称" v-model="params.deviceGroup" :localdata="customerDeviceGroupList"></uni-data-select>
						</view>
					</view>
					<view class="input-item" v-if="editType === 'add'">
						<text class="title">机器型号</text>
						<view class="textarea-content">
							<view class="select-input-content">
								<input class="pd_input" type="text" v-model="parms2.name" @input="range2Fn" placeholder="请输入（如: C7780,V80,7502,C1070）" />
								<uni-icons v-if="parms2.name" type="close" size="17" class="close_icon" color="#999" @click.stop="clear"></uni-icons>
								<uni-icons :type="show ? 'arrowup' : 'arrowdown'" class="pd_icon" color="#999" size="14"></uni-icons>
								<view class="socrll-check" v-if="show">
									<view class="socrll-popper__arrow"></view>
									<scroll-view class="uni-select__selector-scroll" v-if="range2.length > 0">
										<view class="socrll-list">
											<view class="socrll-item" v-for="(item, index) in range2" :key="index" @click="sureCheckFn(item)">{{ item.brand }}/{{ item.name }}</view>
										</view>
									</scroll-view>
									<view class="no-socrll-list" v-else>暂无该机型</view>
								</view>
							</view>
						</view>
					</view>

					<view class="input-item">
						<text class="title">启用状态</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" placeholder="请选择启用状态" v-model="params.status" :localdata="StatusList"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">设备新旧</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" placeholder="请选择设备新旧" v-model="params.deviceOn.value" :localdata="deviceOnList"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">设备状态</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" placeholder="请选择设备状态" v-model="params.deviceStatus.value" :localdata="deviceStatusList"></uni-data-select>
						</view>
					</view>

					<view class="input-item">
						<text class="title">维修状态</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" placeholder="请选择维修状态" v-model="params.fixStatus.value" :localdata="fixStatusList"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">服务类型</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" placeholder="请选择服务类型" v-model="params.serType" :localdata="serTypeList"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">合约类型</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" placeholder="请选择合约类型" v-model="params.treatyType.value" :localdata="treatyTypeList"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">安装客户端</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" placeholder="是否安装客户端" v-model="params.regCliState" :localdata="regCliStateList"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">是否统计</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" placeholder="是否统计" v-model="params.enableStatistics" :localdata="enableStatisticsList"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">移动端是否可见</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" placeholder="移动端是否可见" v-model="params.dataShowState" :localdata="dataShowStateList"></uni-data-select>
						</view>
					</view>
					<view class="input-item upload-file">
						<text class="title">设备组照片</text>
						<view class="image">
							<view class="more">
								<FileUpload ref="updadeRef" @submit="handleSubmit" :limit="1" :fileList="machineInfoImgList"></FileUpload>
							</view>
						</view>
					</view>
				</view>

				<view class="input-content">
					<view class="input-item">
						<text class="title">入住黑白计数器</text>
						<view class="textarea-content">
							<input placeholder="请输入入住黑白计数器" v-model="params.blackWhiteCounter" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">入住彩色计数器</text>
						<view class="textarea-content">
							<input placeholder="请输入入住彩色计数器" v-model="params.colorCounter" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">统计黑白计数器</text>
						<view class="textarea-content">
							<input placeholder="请输入统计黑白计数器" v-model="params.statisticsBlackWhiteCounter" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">统计彩色计数器</text>
						<view class="textarea-content">
							<input placeholder="请输入统计彩色计数器" v-model="params.statisticsColoursCounter" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">黑白打印单价</text>
						<view class="textarea-content">
							<input placeholder="请输入黑白打印单价" v-model="params.blackWhitePrice" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">彩色打印单价</text>
						<view class="textarea-content">
							<input placeholder="请输入彩色打印单价" v-model="params.colorPrice" type="text" />
						</view>
					</view>
				</view>
				<view class="input-content">
					<view class="input-item">
						<text class="title">统计操作人员</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" placeholder="请选择统计操作人员" v-model="params.statisticsOperatName" :localdata="statisticsOperatNameList"></uni-data-select>
						</view>
					</view>

					<view class="input-item">
						<text class="title">签约操作人员</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" placeholder="请选择签约操作人员" v-model="params.signOperatName" :localdata="signOperatNameList"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">负责工程师</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" placeholder="请选择负责工程师" v-model="params.operatId" :localdata="operatIdList"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">统计开始时间</text>
						<view class="textarea-content">
							<uni-datetime-picker class="time-select" placeholder="请选择统计开始时间" type="datetime" v-model="params.statisticsStartDate" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">签约时间</text>
						<view class="textarea-content">
							<uni-datetime-picker class="time-select" placeholder="请选择签约时间" type="datetime" v-model="params.signDate" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">签约黑白计数器</text>
						<view class="textarea-content">
							<input placeholder="请输入签约黑白计数器" v-model="params.signBlackWhiteCounter" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">签约彩色计数器</text>
						<view class="textarea-content">
							<input placeholder="请输入签约彩色计数器" v-model="params.signColoursCounter" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">纸张类型</text>
						<view class="textarea-content">
							<input placeholder="请输入纸张类型" v-model="params.paperType" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">产地版本</text>
						<view class="textarea-content">
							<input placeholder="请输入产地版本" v-model="params.placeOrigin" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">供电电压</text>
						<view class="textarea-content">
							<input placeholder="请输入供电电压" v-model="params.supplyVoltage" type="text" />
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="footer">
			<view class="confirm-btn" :disabled="btnLoading" :loading="btnLoading" @tap="editMachineInfo">{{ editType === 'edit' ? '修改' : '新增' }}</view>
		</view>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>
<script>
import FileUpload from '@/components/file-upload/index.vue';
import {
	dictTreeByCodeApi,
	getCustomerDeviceDetail,
	updateCustomerDeviceInfo,
	getCustomerDeviceGroupList,
	getCustomerDeviceModelList,
	addCustomerDeviceGroup
} from '@/api/custom.js';
import { getWorkerList } from '@/api/system.js';
import { engineerList } from '@/api/workOrder.js';
export default {
	components: { FileUpload },
	data() {
		return {
			// 提交按钮动画
			btnLoading: false,
			loading: false,
			machineInfoImgList: [],
			editType: 'edit',
			id: '',
			pruductId: '', // 设备ID
			customerId: '',
			params: {
				status: 1,
				deviceOn: { text: '', value: '1102' },
				deviceStatus: { text: '', value: '901' },
				fixStatus: { text: '', value: '1501' },
				serType: '1',
				treatyType: { text: '', value: '1201' }
			},
			customerDeviceGroupList: [], // 设备组
			// 字典项
			deviceStatusList: [], // 设备状态
			StatusList: [
				{
					value: 1,
					text: '启用'
				},
				{
					value: 0,
					text: '禁用'
				}
			], // 启用状态
			deviceOnList: [], // 设备新旧
			treatyTypeList: [], // 合约类型
			fixStatusList: [], // 维修状态
			serTypeList: [
				{
					text: '散修',
					value: '1'
				},
				{
					text: '全保',
					value: '2'
				},
				{
					text: '租赁',
					value: '3'
				},
				{
					text: '半保',
					value: '4'
				},
				{
					text: '签约',
					value: '5'
				},
				{
					text: '数据',
					value: '6'
				}
			], // 服务类型
			regCliStateList: [
				{
					text: '是',
					value: '1'
				},
				{
					text: '否',
					value: '0'
				}
			], // 是否安装客户端
			enableStatisticsList: [
				{
					value: 1,
					text: '是'
				},
				{
					value: 0,
					text: '否'
				}
			],
			dataShowStateList: [
				{
					value: 1,
					text: '是'
				},
				{
					value: 0,
					text: '否'
				}
			], // 移动端是否可见
			operatIdList: [], // 维修工程师列表
			statisticsOperatNameList: [], // 统计操作人员
			signOperatNameList: [], // 签约操作人员
			// 关联品牌
			parms2: {
				pageNumber: 1,
				pageSize: 9999,
				desc: false,
				fullIdPath: null,
				name: null,
				orderBy: null
			},
			range2: [],
			show: false
		};
	},
	onShow() {
		// const { params } = this;
		// if (this.editType === 'add') {
		// 	params.status = 1;
		// 	params.deviceOn.value = '1102';
		// 	params.deviceStatus.value = '901';
		// 	params.fixStatus.value = '1501';
		// 	params.serType.value = '1';
		// 	params.treatyType.value = '1201';
		// }
	},
	async onLoad(options) {
		this.id = options.id; // 获取设备ID参数
		this.pruductId = options.pruductId;
		this.customerId = options.customerId;
		this.editType = options.type || 'edit';

		if (this.editType === 'edit') {
			await this.initData();
		}
		if (this.pruductId) {
			await this.getEngineerList();
		}
		if (this.customerId) {
			await this.getDeviceGroupList();
		}
		await this.baseData();
	},
	methods: {
		async getDeviceGroupList() {
			try {
				const deviceGrouplistRes = await getCustomerDeviceGroupList(this.customerId);
				this.customerDeviceGroupList = deviceGrouplistRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
			} catch (err) {
				uni.showToast({
					title: err || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		async getEngineerList() {
			try {
				const engineerListRes = await engineerList(this.pruductId);
				this.operatIdList = engineerListRes.data.map((item) => ({
					value: item.id,
					text: item.name
				}));
			} catch (err) {
				uni.showToast({
					title: err || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		async baseData() {
			const dictApis = [dictTreeByCodeApi(900), dictTreeByCodeApi(1100), dictTreeByCodeApi(1200), dictTreeByCodeApi(1500), getWorkerList({ pageNumber: 1, pageSize: 10000 })];
			try {
				const [deviceStatusListRes, deviceOnListRes, treatyTypeListRes, fixStatusListRes, getWorkerListRes] = await Promise.all(dictApis);
				this.deviceStatusList = deviceStatusListRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));

				this.deviceOnList = deviceOnListRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
				this.treatyTypeList = treatyTypeListRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
				this.fixStatusList = fixStatusListRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
				this.statisticsOperatNameList = getWorkerListRes.data.map((item) => ({
					value: item.name,
					text: item.name
				}));
				this.signOperatNameList = getWorkerListRes.data.map((item) => ({
					value: item.name,
					text: item.name
				}));
			} catch (err) {
				uni.showToast({
					title: err || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		async initData() {
			this.loading = true;
			try {
				// 获取客户信息
				const customerMachineDetailRes = await getCustomerDeviceDetail(this.id);
				this.params = customerMachineDetailRes.data || {};
				this.params.status = this.params.status ? 1 : 0;
				if (Object.keys(customerMachineDetailRes.data.deviceGroupImg).length > 0) {
					this.machineInfoImgList.push(customerMachineDetailRes.data.deviceGroupImg);
				}
				this.params.enableStatistics = this.params.enableStatistics ? 1 : 0;
			} catch (err) {
				uni.showToast({
					title: err || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		editMachineInfo() {
			this.loading = true;
			let params = {
				...this.params,
				deviceGroupImg: this.machineInfoImgList[0]
			};

			const editApi = this.editType === 'edit' ? updateCustomerDeviceInfo : addCustomerDeviceGroup;
			params = this.editType === 'edit' ? params : { ...params, customerId: this.customerId };
			editApi(params)
				.then((res) => {
					uni.showToast({ title: '修改成功', icon: 'none' });
					setTimeout(() => {
						uni.navigateBack();
					}, 300);
				})
				.catch((err) => {
					uni.showToast({ title: err || '修改失败', icon: 'none' });
				})
				.finally(() => {
					this.loading = false;
				});
		},
		clear() {
			// HM修改 收起键盘
			uni.hideKeyboard();
			this.parms2.name = '';
			this.show = false;
		},
		range2Fn(val) {
			this.range2 = [];
			if (!val.detail.value) {
				this.show = false;
				return;
			}
			if (val.detail.value.length < 3) {
				this.show = false;
				uni.showToast({
					title: '最少输入3位字符',
					icon: 'none'
				});
				return;
			}
			getCustomerDeviceModelList(this.parms2).then((res) => {
				this.range2 = res.data.rows;
			});
			this.show = true;
		},
		// 确认机型
		sureCheckFn(item) {
			this.$set(this.params, 'productId', item.id);
			this.parms2.name = item.name;
			this.show = false;
		},
		// 选择机器使用状态
		selectdeviceStatus() {
			uni.showActionSheet({
				itemList: this.deviceStatusList, // 设备状态列表数据
				success: (res) => {
					if (res.tapIndex !== undefined) {
						this.deviceStatus = this.deviceStatusList[res.tapIndex]; // 获取用户选择的设备状态值
						this.deviceStatusValue = this.deviceStatusListValue[res.tapIndex]; // 获取用户选择的设备状态值对应的布尔值
					}
				}
			});
		},
		// 图片上上传
		handleSubmit(data) {
			this.machineInfoImgList = data;
		}
	}
};
</script>
<style lang="scss" scoped>
/deep/.uni-data-pickerview {
	.item {
		justify-content: center;
	}

	.check {
		margin-left: 6px;
		margin-right: 0;
		border: 2px solid #e5452f !important;
		border-left: 0 !important;
		border-top: 0 !important;
	}
}

/deep/.selected-item-active {
	border-bottom: 2px solid #e5452f !important;
}

/deep/.uni-data-tree-dialog {
	top: 60% !important;
}

/deep/.active-roles .uni-data-pickerview .selected-area {
	display: none;
}
</style>
