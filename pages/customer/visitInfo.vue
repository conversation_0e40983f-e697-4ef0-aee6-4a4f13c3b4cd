<template>
	<view class="customer-main">
		<view class="wrapper">
			<view class="main_info">
				<view class="input-content">
					<view class="input-item">
						<text class="title">客户编号</text>
						<view class="textarea-content">
							<view class="text">
								{{ seqId }}
							</view>
						</view>
					</view>
					<view class="input-item">
						<text class="title">客户编号</text>
						<view class="textarea-content">
							<view class="text">
								{{ shopName }}
							</view>
						</view>
					</view>
					<view class="input-item">
						<text class="title">拜访目的</text>
						<view class="textarea-content">
							<uni-data-select
								class="data-select"
								:disabled="editType === 'edit'"
								v-model="params.callGoal.value"
								:localdata="callGoalRange"
								placeholder="请选择拜访目的"
							></uni-data-select>
						</view>
					</view>

					<!-- 		<view class="input-item">
						<text class="title">拜访方式</text>
						<view class="textarea-content">
							<uni-data-select
								class="data-select"
								:disabled="editType === 'edit'"
								v-model="params.callType.value"
								:localdata="callTypeRange"
								placeholder="请选择拜访方式"
							></uni-data-select>
						</view>
					</view> -->
				</view>

				<view class="input-content">
					<!-- <view class="input-item">
						<text class="title">员工名称</text>
						<view class="textarea-content">
							<uni-data-select
								class="data-select"
								:disabled="editType === 'edit'"
								v-model="params.operatName"
								:localdata="operatNameRange"
								placeholder="请选择拜访角色"
							></uni-data-select>
						</view>
					</view> -->
					<!-- 				<view class="input-item">
						<text class="title">到店时间</text>
						<view class="textarea-content">
							<uni-datetime-picker type="date" :disabled="editType === 'edit'" :clear-icon="true" v-model="params.reachShopTime" placeholder="请选择到店时间" />
						</view>
					</view> -->
					<view class="input-item">
						<text class="title">受访角色</text>
						<view class="textarea-content">
							<uni-data-select
								class="data-select"
								:disabled="editType === 'edit'"
								v-model="params.reachShopRole.value"
								:localdata="reachShopRoleRange"
								placeholder="请选择受访角色"
							></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">受访人名字</text>
						<view class="textarea-content">
							<input :disabled="editType === 'edit'" placeholder="请输入受访人名字" v-model="params.reachShopName" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">受访人电话</text>
						<view class="textarea-content">
							<input :disabled="editType === 'edit'" placeholder="请输入受访人电话" v-model="params.reachShopTel" type="text" />
						</view>
					</view>
					<view class="textarea-item">
						<text class="title">拜访内容</text>
						<view class="textarea-content">
							<textarea auto-height="true" :maxlength="500" :disabled="editType === 'edit'" placeholder="请输入您的拜访内容" v-model="params.remark" />
							<view class="max-length">{{ params.remark ? params.remark.length : 0 }}/500</view>
						</view>
					</view>
					<view class="textarea-item">
						<text class="title">下次注意事项</text>
						<view class="textarea-content">
							<textarea auto-height="true" :maxlength="500" :disabled="editType === 'edit'" placeholder="请输入下次注意事项" v-model="params.nextNoticeRemark" />
							<view class="max-length">{{ params.nextNoticeRemark ? params.nextNoticeRemark.length : 0 }}/500</view>
						</view>
					</view>
					<view class="input-item">
						<text class="title">跟进状态</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="params.callState.value" :localdata="callStateRange" placeholder="请选择跟进状态"></uni-data-select>
						</view>
					</view>
					<view class="input-item upload-file">
						<text class="title">拜访照片</text>
						<view class="image">
							<view class="more">
								<FileUpload
									ref="updadeRef"
									:disabled="editType === 'edit'"
									:deletable="!editType === 'edit'"
									@submit="handleSubmit"
									:limit="4"
									:fileList="visitImgList"
								></FileUpload>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="footer" v-if="editType === 'add'">
			<button class="confirm-btn" :disabled="btnLoading" :loading="btnLoading" @tap.stop="confirmSubmit">新增</button>
		</view>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>
<script>
import FileUpload from '@/components/file-upload/index.vue';
import { dictTreeByCodeApi, addVisitRecordApi, editVisitRecordApi, getStaffListApi } from '@/api/custom.js';
import { getWorkerList } from '@/api/system.js';
export default {
	components: { FileUpload },
	data() {
		return {
			seqId: '',
			shopName: '',
			// 提交按钮动画
			btnLoading: false,
			customerId: '',
			loading: true,
			params: {
				callGoal: { text: '', value: '' },
				reachShopRole: { text: '', value: '' },
				callType: { text: '', value: '' },
				callState: { text: '', value: '' }
			},
			callGoalRange: [], // 拜访目的
			callTypeRange: [], // 拜访方式
			reachShopRoleRange: [], // 拜访角色
			staffRange: [], // 受访人名称
			callStateRange: [], // 跟进状态
			operatNameRange: [], // 员工名称
			editType: 'edit',
			visitImgList: []
		};
	},
	onShow() {},
	async onLoad(options) {
		this.editType = options.type || 'edit';
		this.customerId = options.customerId;
		this.seqId = options.seqId;
		this.shopName = options.shopName;
		this.params = options.params ? JSON.parse(options.params) : this.params;
		this.visitImgList = this.params.callImgs ? this.params.callImgs : [];
		if (this.editType === 'add') {
			this.params.callType.value = '3130';
		}
		this.baseData();
		// this.getStaffList();
	},
	methods: {
		async baseData() {
			this.loading = true;
			const dictApis = [dictTreeByCodeApi(2400), dictTreeByCodeApi(3100), dictTreeByCodeApi(500), dictTreeByCodeApi(3700), getWorkerList({ pageNumber: 1, pageSize: 10000 })];
			try {
				const [callGoalRangeRes, callTypeRangeRes, reachShopRoleRangeRes, callStateRangeRes, getWorkerListRes] = await Promise.all(dictApis);
				this.callGoalRange = callGoalRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));

				this.callTypeRange = callTypeRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
				this.reachShopRoleRange = reachShopRoleRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
				this.callStateRange = callStateRangeRes.data.map((item) => ({
					value: item.value,
					text: item.label
				}));
				this.operatNameRange = getWorkerListRes.data.map((item) => ({
					value: item.name,
					text: item.name
				}));
			} catch (err) {
				console.log(err);
				uni.showToast({
					title: err || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		// getStaffList() {
		// 	getStaffListApi(this.customerId).then((res) => {
		// 		res.data.map((item) => {
		// 			this.staffRange.push({
		// 				value: item.id,
		// 				text: item.name
		// 			});
		// 		});
		// 	});
		// },
		confirmSubmit() {
			let { params, customerId } = this;
			this.loading = true;
			// const editApt = editType === 'edit' ? editVisitRecordApi : addVisitRecordApi;
			params = {
				...params,
				callImgs: this.visitImgList,
				customerId
			};
			// params = editType === 'edit' ? { ...params } : { ...params, customerId };
			addVisitRecordApi(params)
				.then((r) => {
					uni.showToast({
						title: r.message || '保存成功',
						icon: 'success'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 300);
				})
				.catch((err) => {
					uni.showToast({
						title: err || '保存失败',
						icon: 'none'
					});
				})
				.finally(() => {
					this.loading = false;
				});
		},
		handleSubmit(data) {
			this.visitImgList = data;
		}
	}
};
</script>
<style lang="scss" scoped>
.textarea-content {
	textarea {
		height: 100% !important;
	}
}
</style>
