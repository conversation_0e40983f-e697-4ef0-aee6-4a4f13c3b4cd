<template>
	<view class="customer-main">
		<view class="wrapper">
			<view class="main_info">
				<view class="input-content">
					<view class="input-item">
						<text class="title">客户编号</text>
						<view class="textarea-content">
							<input placeholder="请输入客户编号" disabled v-model="seqId" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">店招名</text>
						<view class="textarea-content">
							<input placeholder="请输入店招名" disabled v-model="shopName" type="text" />
						</view>
					</view>
				</view>
				<view class="input-content">
					<view class="input-item">
						<text class="title">老板籍贯</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="params.nativePlace" :localdata="nativePlaceRange" placeholder="请选择法人籍贯"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">店面大小</text>
						<view class="textarea-content">
							<!-- <input auto-height="true" v-model="params.siteArea" placeholder="请输入店面大小" /> -->
							<uni-data-select class="data-select" v-model="params.siteArea" :localdata="siteAreaRange" placeholder="请选择店面大小"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">店面人数</text>
						<view class="textarea-content">
							<!-- <input auto-height="true" v-model="params.personnelNum" placeholder="请输入店面人数" /> -->
							<uni-data-select class="data-select" v-model="params.personnelNum" :localdata="personnelNumRange" placeholder="请选择店面人数"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">客户形态</text>
						<view class="textarea-content">
							<!-- <uni-data-select class="data-select" v-model="params.customerCost.value" :localdata="customerCostRange" placeholder="请选择客户价值"></uni-data-select> -->
							<uni-data-select class="data-select" v-model="params.customerCost" :localdata="customerCostRange" placeholder="请选择客户形态"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">月黑白印量</text>
						<view class="textarea-content">
							<!-- <input auto-height="true" v-model="params.monthBlackWhiteNum" placeholder="请输入月黑白印量" /> -->
							<uni-data-select class="data-select" v-model="params.monthBlackWhiteNum" :localdata="monthBlackWhiteNumRange" placeholder="请选择月黑白印量"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">月彩色印量</text>
						<view class="textarea-content">
							<!-- <input auto-height="true" v-model="params.monthColoursNum" placeholder="请输入月彩色印量" /> -->
							<uni-data-select class="data-select" v-model="params.monthColoursNum" :localdata="monthColoursNumRange" placeholder="请选择月彩色印量"></uni-data-select>
						</view>
					</view>

					<view class="input-item">
						<text class="title">营业额</text>
						<view class="textarea-content">
							<!-- <input placeholder="请输入营业额" v-model="params.turnover" type="text" /> -->
							<uni-data-select class="data-select" v-model="params.turnover" :localdata="turnoverRange" placeholder="请选择营业额"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">图文占比</text>
						<view class="textarea-content">
							<!-- <input placeholder="请输入营业额" v-model="params.pictureRatio" type="text" /> -->
							<uni-data-select class="data-select" v-model="params.pictureRatio" :localdata="pictureRatioRange" placeholder="请选择图文占比"></uni-data-select>
						</view>
					</view>

					<view class="input-item">
						<text class="title">同行加工</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="params.peerProcessNum" :localdata="peerProcessNumRange" placeholder="请选择同行加工"></uni-data-select>
							<!-- <input placeholder="请输入营业额" v-model="params.peerProcessNum" type="text" /> -->
						</view>
					</view>
					<view class="input-item">
						<text class="title">实际机器数</text>
						<view class="textarea-content">
							<!-- <input placeholder="请输入实际机器数" v-model="params.realMachineNum" type="text" /> -->
							<uni-data-select class="data-select" v-model="params.realMachineNum" :localdata="realMachineNumRange" placeholder="请选择实际机器数"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">是否有新机</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="params.hasNew" :localdata="isTrue" placeholder="请选择是否有新机"></uni-data-select>
							<!-- <uni-data-select class="data-select" v-model="params.hasNew" :localdata="hasNewRange" placeholder="请选择是否有新机"></uni-data-select> -->
						</view>
					</view>
					<view class="input-item">
						<text class="title">后道设备数量</text>
						<view class="textarea-content">
							<!-- <input placeholder="请输入实际机器数" v-model="params.deviceNum" type="text" /> -->
							<uni-data-select class="data-select" v-model="params.deviceNum" :localdata="deviceNumRange" placeholder="请选择后备设备数量"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">主要购买渠道</text>
						<view class="textarea-content">
							<SelectCheckBox
								style="width: 100%"
								v-model="params.purchaseChannel"
								:collapse-tags-num="5"
								placeholder="请选择主要购买渠道"
								multiple
								data-key="text"
								data-value="value"
								:localdata="purchaseChannelRange"
							/>
							<!-- <uni-data-select class="data-select" v-model="params.purchaseChannel" :localdata="purchaseChannelRange" placeholder="请选择主要购买渠道"></uni-data-select> -->
							<!-- <input placeholder="请输入主要购买渠道" v-model="params.purchaseChannel" type="text" /> -->
						</view>
					</view>
					<view class="input-item">
						<text class="title">耗材价格优势</text>
						<view class="textarea-content">
							<!-- <input placeholder="请输入耗材价格反馈" v-model="params.consumableAdvantage" type="text" /> -->
							<uni-data-select class="data-select" v-model="params.consumableAdvantage" :localdata="consumableAdvantageRange" placeholder="请选择耗材价格优势"></uni-data-select>
						</view>
					</view>
					<!-- 				<view class="input-item">
						<text class="title">耗材价格反馈</text>
						<view class="textarea-content">
							<input placeholder="请输入耗材价格反馈" v-model="params.consumableCompetitorMoney" type="text" />
						</view>
					</view> -->
					<view class="input-item">
						<text class="title">自修能力</text>
						<view class="textarea-content">
							<!-- <input placeholder="请输入自修能力" v-model="params.selfStudy" type="text" /> -->
							<uni-data-select class="data-select" v-model="params.selfStudy" :localdata="selfStudyRange" placeholder="请选择自修能力"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">当前服务方式</text>
						<view class="textarea-content">
							<SelectCheckBox
								style="width: 100%"
								v-model="params.serviceType"
								:collapse-tags-num="5"
								placeholder="请选择当前服务方式"
								multiple
								data-key="text"
								data-value="value"
								:localdata="serviceTypeRange"
							/>
							<!-- <uni-data-select class="data-select" v-model="params.serviceType" :localdata="serviceTypeRange" placeholder="请选择服务类型"></uni-data-select> -->
							<!-- <input placeholder="请输入服务类型" v-model="params.serviceType" type="text" /> -->
						</view>
					</view>
					<view class="input-item">
						<text class="title">以直客为主？</text>
						<view class="textarea-content">
							<uni-data-select class="data-select" v-model="params.normalMaster" :localdata="isTrue" placeholder="是否以直客为主"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">印品质要求</text>
						<view class="textarea-content">
							<!-- <input placeholder="请输入印品质要求" v-model="params.demandDegree" type="text" /> -->
							<uni-data-select class="data-select" v-model="params.demandDegree" :localdata="demandDegreeRange" placeholder="请选择印品质要求"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">是否有设计院等大型客户</text>
						<view class="textarea-content">
							<!-- <input placeholder="请输入印品质要求" v-model="params.demandDegree" type="text" /> -->
							<uni-data-select class="data-select" v-model="params.largScaleCustomer" :localdata="isTrue" placeholder="是否有大型客户"></uni-data-select>
						</view>
					</view>
					<view class="input-item">
						<text class="title">耗材竞争对手</text>
						<view class="textarea-content">
							<input placeholder="请输入耗材竞品" v-model="params.consumableCompetitor" type="text" />
						</view>
					</view>

					<view class="input-item">
						<text class="title">服务竞争对手</text>
						<view class="textarea-content">
							<input placeholder="请输入服务竞品" v-model="params.serCompetitor" type="text" />
						</view>
					</view>
					<view class="input-item">
						<text class="title">入驻平台时间</text>
						<view class="textarea-content">
							<!-- <input placeholder="入驻平台时间" disabled v-model="params.createdAt" type="text" /> -->
							<view class="text">{{ params.custCreatedAt }}</view>
						</view>
					</view>
					<view class="input-item upload-file">
						<text class="title">店招照片</text>
						<view class="image">
							<view class="more">
								<FileUpload ref="updadeRef" @submit="handleSubmitShopInfoImgList" :limit="3" :fileList="shopRecruitmentImg"></FileUpload>
							</view>
						</view>
					</view>
					<view class="input-item upload-file">
						<text class="title">店铺周围照片</text>
						<view class="image">
							<view class="more">
								<FileUpload ref="updadeRef" @submit="handleSubmitShopOutsideImg" :limit="4" :fileList="shopOutsideImg"></FileUpload>
							</view>
						</view>
					</view>
					<view class="input-item upload-file">
						<text class="title">店内照片</text>
						<view class="image">
							<view class="more">
								<FileUpload ref="updadeRef" @submit="handleSubmitShopInsideImg" :limit="5" :fileList="shopInsideImg"></FileUpload>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="footer">
			<button class="confirm-btn" :disabled="btnLoading" :loading="btnLoading" @tap.stop="confirmSubmit">保存</button>
		</view>
		<!--页面加载动画-->
		<rfLoading isFullScreen :active="loading"></rfLoading>
	</view>
</template>
<script>
import { getCustomerStatistics, getUserTagInfo, updateUserTag, dictTreeByCodeApi } from '@/api/custom.js';
import FileUpload from '@/components/file-upload/index.vue';
import SelectCheckBox from './components/selectCheckBox.vue';
export default {
	components: { FileUpload, SelectCheckBox },
	data() {
		return {
			seqId: '',
			shopName: '',
			// 提交按钮动画
			btnLoading: false,
			customerId: '',
			loading: true,
			params: {},
			nativePlaceRange: [], // 老板籍贯
			siteAreaRange: [], // 店铺大小
			personnelNumRange: [], // 店铺人数
			customerCostRange: [], // 客户形态
			monthBlackWhiteNumRange: [], // 月黑白印量
			monthColoursNumRange: [], //月彩色印量
			turnoverRange: [], // 营业额
			pictureRatioRange: [], //图文占比
			isTrue: [], // 是/否
			realMachineNumRange: [], // 实际机器数量
			deviceNumRange: [], // 后道数量
			purchaseChannelRange: [], //主要购买渠道
			consumableAdvantageRange: [], // 耗材价格优势
			selfStudyRange: [], // 自修能力
			serviceTypeRange: [], //当前服务方式
			demandDegreeRange: [], // 印品需求
			peerProcessNumRange: [], // 同行加工
			legalNativePlaceRange: [
				{
					value: '本地籍',
					text: '本地籍'
				},
				{
					value: '湖南籍',
					text: '湖南籍'
				},
				{
					value: '外地籍',
					text: '外地籍'
				}
			], // 老板籍贯
			peerProcessRange: [
				{
					value: '1',
					text: '是'
				},
				{
					value: '0',
					text: '否'
				}
			],
			normalMasterRange: [
				{
					value: 1,
					text: '是'
				},
				{
					value: 0,
					text: '否'
				}
			],
			hasNewRange: [
				{
					value: 1,
					text: '是'
				},
				{
					value: 0,
					text: '否'
				}
			],
			shopRecruitmentImg: [],
			shopOutsideImg: [],
			shopInsideImg: []
		};
	},
	onShow() {},
	async onLoad(options) {
		const { id, seqId, shopName } = options;
		this.seqId = seqId;
		this.shopName = shopName;
		this.customerId = id;

		await this.initData();
		await this.baseData();
	},
	methods: {
		async baseData() {
			this.loading = true;

			// 定义字典 API 与对应数据属性的映射
			const dictMapping = {
				nativePlaceRange: 5900,
				siteAreaRange: 4900,
				personnelNumRange: 5100,
				customerCostRange: 3800,
				monthBlackWhiteNumRange: 4800,
				monthColoursNumRange: 5800,
				turnoverRange: 5200,
				pictureRatioRange: 5300,
				isTrue: 'isTrue',
				realMachineNumRange: 6300,
				deviceNumRange: 6400,
				purchaseChannelRange: 4600,
				consumableAdvantageRange: 5400,
				selfStudyRange: 5500,
				serviceTypeRange: 5600,
				demandDegreeRange: 5700,
				peerProcessNumRange: 6200
			};

			try {
				const dictApis = Object.values(dictMapping).map((code) => dictTreeByCodeApi(code));
				const responses = await Promise.all(dictApis);
				Object.keys(dictMapping).forEach((key, index) => {
					this[key] = responses[index].data.map((item) => ({
						value: item.value,
						text: item.label
					}));
				});
			} catch (err) {
				uni.showToast({
					title: err || '系统出错啦，请稍后再试！',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		initData() {
			this.loading = true;
			getUserTagInfo(this.customerId)
				.then((res) => {
					Object.keys(res.data).forEach((key) => {
						res.data[key] = res.data[key].label ? res.data[key].value : res.data[key];
						if (JSON.stringify(res.data[key]) === '{}') {
							res.data[key] = '';
						}
					});
					this.params = res.data || {};
					this.shopRecruitmentImg = res.data.shopRecruitmentImg || [];
					this.shopOutsideImg = res.data.shopOutsideImg || [];
					this.shopInsideImg = res.data.shopInsideImg || [];
				})
				.catch((err) => {
					uni.showToast({
						title: err || '系统出出错啦，请稍后再试',
						icon: 'none'
					});
				})
				.finally(() => {
					this.loading = false;
				});
		},
		// 图片上上传
		handleSubmit(data, type) {
			console.log(type);
			// this.params[type] = data;
		},
		handleSubmitShopInfoImgList(data) {
			this.shopRecruitmentImg = data;
		},
		handleSubmitShopOutsideImg(data) {
			this.shopOutsideImg = data;
		},
		handleSubmitShopInsideImg(data) {
			this.shopInsideImg = data;
		},
		confirmSubmit() {
			this.loading = true;
			const { params, shopRecruitmentImg, shopOutsideImg, shopInsideImg, customerId } = this;
			const arg = {
				...params,
				shopRecruitmentImg,
				shopOutsideImg,
				shopInsideImg,
				customerId
			};
			updateUserTag(arg)
				.then((r) => {
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 300);
				})
				.catch((err) => {
					uni.showToast({
						title: err || '保存失败',
						icon: 'none'
					});
				})
				.finally(() => {
					this.loading = false;
				});
		}
	}
};
</script>
<style lang="scss" scoped></style>
