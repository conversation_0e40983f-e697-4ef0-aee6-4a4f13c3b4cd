<template>
	<view class="main">
		<view class="content">
			<view class="item-info">
				店编号：
				<view class="info" v-if="customerInfo.seqId">{{ customerInfo.seqId }}</view>
			</view>
			<view class="item-info">
				店招名：
				<view class="info" v-if="customerInfo.name">{{ customerInfo.name }}</view>
			</view>
			<view class="item-info" v-for="(item, index) in listData" :key="index" @click="toInfoManger(index)">
				{{ item.text }}
				<view class="info">
					<text class="iconfont icon-jiantou"></text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			customerInfo: {},
			listData: [
				{
					text: '基础信息',
					path: '/pages/customer/customerInfo'
				},
				{
					text: '员工信息',
					path: '/pages/customer/customerStaff'
				},
				{
					text: '商务信息',
					path: '/pages/customer/customerBusinessInfo'
				},
				{
					text: '机器信息',
					path: '/pages/customer/customerMachine'
				},
				{
					text: '联网设置',
					path: '/pages/customer/IOT'
				},
				{
					text: '用户标签',
					path: '/pages/customer/userTag'
				},
				{
					text: '拜访记录',
					path: '/pages/customer/visitLog'
				},
				{
					text: '购买意向',
					path: '/pages/customer/buyPurpose'
				},
				{
					text: '合约记录',
					path: '/pages/customer/agreement'
				},
				{
					text: '客户报价',
					path: ''
				},
				{
					text: '访问记录',
					path: '/pages/customer/visit'
				},
				{
					text: '搜索记录',
					path: '/pages/customer/searchInfo'
				},
				{
					text: '耗材仓库',
					path: '/pages/customer/store'
				},
				{
					text: '订单&开票',
					path: ''
				},
				{
					text: '充值记录',
					path: ''
				},
				{
					text: '应付账单',
					path: ''
				},
				{
					text: '客户价值',
					path: '/pages/customer/cost'
				}
			]
		};
	},
	onLoad(option) {
		this.customerInfo = JSON.parse(decodeURIComponent(option.params));
	},
	methods: {
		// 跳转信息管理
		toInfoManger(index) {
			let route = this.listData[index].path;
			const { id, name, seqId } = this.customerInfo;
			if (route) {
				this.$mRouter.push({
					route: `${route}?id=${id}&shopName=${name}&seqId=${seqId}`
				});
			} else {
				this.$mHelper.toast('功能正在开发中');
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	width: 100%;
	// height: 100vh;
	display: flex;
	flex-direction: column;

	.content {
		width: 100%;
		// height: calc(100% + 100rpx);
		padding: 0 24rpx;
		padding-bottom: 50rpx;
		box-sizing: border-box;
		margin-top: 22rpx;
		background-color: #ffffff;
		position: relative;

		.item-info {
			width: 100%;
			min-height: 90rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #323333;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1rpx solid #f3f3f3;

			.info {
				font-weight: bold;
				color: #333333;
				display: flex;
				align-items: center;

				.total {
					color: #ff541e;
					margin: 0 8rpx;
				}

				input {
					text-align: right;
				}
			}

			.content-input {
				height: 60rpx;

				input {
					height: 60rpx;
					text-align: right;
					border: 2rpx solid #f6f6f6;
					padding: 0 16rpx;
				}
			}

			.changeshigu {
				display: flex;
				align-items: center;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ff541e;

				::v-deep.u-icon {
					margin-left: 8rpx;
				}
			}
		}

		.item-info:active {
			background-color: rgba(199, 199, 199, 0.1);
		}

		.item-info:last-child {
			border: none !important;
		}
	}

	.iconfont {
		font-size: 20px;
		margin: 0 8rpx;
	}

	.color {
		color: #f37b1d;
	}
}
</style>
