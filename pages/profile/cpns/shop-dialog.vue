<template>
  <view class="cu-modal bottom-modal" :class="{ show: isShow }">
    <view class="cu-dialog">
      <view class="modal-title">
        <text>店铺名称</text>
        <text class="close iconfont iconclose-circle" @click.stop="hide"></text>
      </view>
      <view class="shop-list">
        <view class="list" v-for="(item, index) in infoData" :key="index">
          <view class="left" @click="changeCheck(item, index)">
            <view
              :class="
                currentIndex === index
                  ? 'check active iconfont icon-duihao'
                  : 'check'
              "
            ></view>
          </view>
          <view class="right">
            <view class="title">{{ item.customerName }}</view>
            <text class="address">{{ item.fullAddress }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { shopInfo } from "@/api";
export default {
  props: {
    // infoData: {
    // 	type: 'Array',
    // 	default: ()=>[{
    // 			id: 1,
    // 			title: "店招名展示全部内容嘻嘻哈哈巴扎黑",
    // 			address: "XXX省XXX市XXX县XXXXXXXXX",
    // 		},
    // 		{
    // 			id: 2,
    // 			title: "店招名展示全部内容嘻嘻哈哈巴扎黑",
    // 			address: "XXX省XXX市XXX县XXXXXXXXXX",
    // 		},
    // 		{
    // 			id: 3,
    // 			title: "店招名展示全部内容嘻嘻哈哈巴扎黑",
    // 			address: "XXX省XXX市XXX县XXXXXXXXXXXXXX",
    // 		},
    // 	],
    // }
  },
  onShow() {},
  data() {
    return {
      isShow: false,
      isPage: false,
      currentIndex: 0,
      infoData: [],
    };
  },
  methods: {
    async show() {
      let res = await this.$http.get(`${shopInfo}`);
      let customerId = uni.getStorageSync("customerId");
      this.infoData = res.data;
      let index = this.infoData.findIndex(
        (item) => item.customerId == customerId
      );
      console.log(index);
      if (index > -1) {
        this.currentIndex = index;
      }
      // console.log(this.infoData);
      this.isPage = true;
      setTimeout(() => {
        this.isShow = true;
      }, 300);
    },

    hide() {
      this.isShow = false;
      setTimeout(() => {
        this.isPage = false;
      }, 300);
    },
    changeCheck(item, index) {
	  this.isShow = false;
      this.currentIndex = index;
      uni.setStorageSync("userInfo", item);
      uni.setStorageSync("customerId", item.customerId);
      this.$emit("changeCheck", item);
    },
  },
};
</script>

<style scoped lang="scss">
.cu-dialog {
  width: 100%;
  height: 70%;
  border-radius: 30rpx 30rpx 0 0 !important;
  overflow: hidden;
  box-sizing: border-box;

  .modal-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 80rpx;
    position: relative;

    text {
      font-size: 32rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #0f0f0f;
      line-height: 39rpx;
    }

    .close {
      position: absolute;
      right: 35rpx;
      top: 25rpx;
      font-size: 40rpx;
      width: 45rpx;
      height: 45rpx;
    }
  }

  .shop-list {
    padding: 0 4%;
    margin: 10rpx auto;
    text-align: left;

    .list {
      position: relative;
      display: flex;
      width: 100%;
      border-radius: 6rpx;
      margin-bottom: 20rpx;
      overflow: hidden;
      padding: 30rpx 20rpx;

      .left {
        width: 50rpx;

        .check {
          width: 35rpx;
          height: 35rpx;
          border: 1px solid #999999;
          border-radius: 50%;

          &.active {
            border: 1px solid #e5452f;
            color: #e5452f !important;
            line-height: 30rpx;
          }
        }
      }

      .right {
        flex: 1;
        padding: 0 20rpx;

        .title {
          font-size: 29rpx;
          font-family: PingFang SC;
          font-weight: bold;
          color: #0f0f0f;
          line-height: 39rpx;
        }

        .address {
          font-size: 27rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #0f0f0f;
          line-height: 40rpx;
        }
      }
    }
  }
}
</style>
