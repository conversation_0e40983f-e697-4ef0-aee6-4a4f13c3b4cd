<template>
  <view class="user">
    <!--头部-->
    <view class="user-section">
      <img class="bg" :src="userBg" />
      <view class="user-info-box">
        <view
          class="portrait-box"
          @tap="!userInfo ? navTo('/pages/user/login') : null"
        >
          <image class="portrait" src="@/static/images/top.png" />

          <!-- <image v-if="!userInfo" class="portrait" :src="topHeader" /> -->
          <text class="username" v-if="userInfo">
            {{ userInfo.customerName || "店招名" }}
          </text>
          <text v-if="!userInfo" class="username"> 登录 </text>
        </view>
        <view class="change" v-if="userInfo">
          <view
            class="img"
            style="
              display: inline-block;
              float: right;
              margin: -10px 0 0 10px;
              width: auto;
            "
            @tap="gosetting"
          >
            <text class="iconfont iconsettings"></text>
          </view>
          <text class="btn" @tap="handleSwitchShop">切换店铺</text>
        </view>
      </view>
      <!-- <view class="vip-card-box" v-if="userInfo && userInfo.staff">
        <view class="b-btn" @tap="navTo('/pages/user/account/level')">
          积分：{{ userInfo.houseCount * integral || 0 }}
        </view>
      </view> -->
    </view>

    <!-- 内容区-->

    <view
      class="cover-container"
      :style="[
        {
          transform: coverTransform,
          transition: coverTransition,
        },
      ]"
      @touchstart="coverTouchstart"
      @touchmove="coverTouchmove"
      @touchend="coverTouchend"
    >
      <!--我的订单-->
      <view class="promotion-center">
        <view class="title">订单</view>
        <view class="list">
          <view class="list-box">
            <view
              class="item"
              @click="navTo(item.path)"
              v-for="item in listData"
              :key="item.text"
            >
              <image class="img" :src="item.url" mode="heightFix"></image>
              <text class="text">{{ item.text }}</text>
              <rf-badge
                v-if="item.count && item.count > 0"
                type="error"
                size="small"
                class="badge"
                :text="item.count > 99 ? '99+' : item.count"
              ></rf-badge>
            </view>
          </view>
        </view>
      </view>
      <!--维修工单-->
      <view class="promotion-center">
        <view class="title">维修工单</view>
        <view class="list">
          <view class="list-box">
            <view
              class="item"
              @click="navTo(item.path)"
              v-for="item in workOrderList"
              :key="item.text"
              v-if="item.role == 501 || item.role == 505 || item.role == ''"
            >
              <image class="img" :src="item.url" mode="heightFix"></image>
              <text class="text">{{ item.text }}</text>
            </view>
          </view>
        </view>
      </view>
      <!--我的功能-->
      <view class="promotion-center">
        <view class="title">功能</view>
        <view class="list">
          <view class="list-box">
            <view
              class="item"
              @click="navTo(item.path)"
              v-for="item in featureList"
              :key="item.text"
              v-if="item.role == 501 || item.role == 505 || item.role == ''"
            >
              <image class="img" :src="item.url" mode="heightFix"></image>
              <text class="text">{{ item.text }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 店铺选择 -->
    <shopDialog ref="shopRef" @changeCheck="changeCheck" />
    <!--页面加载动画-->
    <rfLoading isFullScreen :active="loading"></rfLoading>
  </view>
</template>
<script>
/**
 * @des 个人中心
 *
 * @<NAME_EMAIL>
 * @date 2020-01-10 11:41
 * @copyright 2019
 */
import {
  PATH,
  followList,
  shopInfo,
  topHeader,
  topHeader0,
  topHeader1,
  noticeDetil,
  pubalList,
} from "@/api/index";

import { orderCount } from "@/api/order";

import listCell from "@/components/rf-list-cell";
import { mapMutations } from "vuex";
import rfBadge from "@/components/rf-badge/rf-badge";
import $mAssetsPath from "@/config/assets.config";
import shopDialog from "./cpns/shop-dialog.vue";

let startY = 0,
  moveY = 0,
  pageAtTop = true;
export default {
  components: {
    listCell,
    rfBadge,
    shopDialog,
  },
  data() {
    return {
      newPub: 0,
      topHeader: topHeader,
      topHeader1: topHeader1,
      topHeader0: topHeader0,
      settingList: this.$mConstDataConfig.settingList,
      orderSectionList: this.$mConstDataConfig.orderSectionList,
      amountList: this.$mConstDataConfig.amountList,
      orderStatics: {},
      isOpenLiveStreaming: this.$mSettingConfig.isOpenLiveStreaming,
      headImg: this.$mAssetsPath.headImg,
      staffHeadImg: "",
      vipCardBg: this.$mAssetsPath.vipCardBg,
      arc: this.$mAssetsPath.arc,
      userBg: this.$mAssetsPath.userBg,
      coverTransform: "translateY(0px)",
      coverTransition: "0s",
      moving: false,
      userInfo: {},
      loading: true,
      appName: this.$mSettingConfig.appName,
      hasLogin: null,
      listData: [
        {
          text: "全部订单",
          url: "/static/images/user/fullOrder.png",
          path: "/pages/order/orderList?type=0",
        },
        {
          text: "待付款",
          url: "/static/images/user/due.png",
          path: "/pages/order/orderList?type=1",
        },
        {
          text: "待收货",
          url: "/static/images/user/receiveGoods.png",
          path: "/pages/order/orderList?type=2",
        },
        {
          text: "已完成",
          url: "/static/images/user/finished.png",
          path: "/pages/order/orderList?type=3",
        },
        {
          text: "售后",
          url: "/static/images/user/afterSale.png",
          path: "/pages/order/afterSaleOrder",
        },
      ],
      featureList: [
        {
          text: "设备管理",
          url: "/static/images/user/deviceManage.png",
          path: "/pages/system/register",
          role: "",
        },
        {
          text: "耗材仓库",
          url: "/static/images/user/consumableWarehouse.png",
          path: "/pages/system/machine",
          role: "",
        },
        {
          text: "登记员工",
          url: "/static/images/user/registeredEmployee.png",
          path: "/pages/system/machine",
          role: "",
        },
        {
          text: "登记机器",
          url: "/static/images/user/registerMachine.png",
          path: "/pages/system/machine",
          role: "",
        },
        {
          text: "联系客服",
          url: "/static/images/user/customerService.png",
          path: "/pages/system/machine",
          role: "",
        },
      ],
      // 工单列表
      workOrderList: [
        {
          text: "待接单",
          url: "/static/images/user/pendingOrder.png",
          path: "/pages/workOrder/orderList?type=1",
          role: "",
        },
        {
          text: "维修中",
          url: "/static/images/user/maintainOrder.png",
          path: "/pages/workOrder/orderList?type=2",
          role: "",
        },
        {
          text: "待确认",
          url: "/static/images/user/confirmOrder.png",
          path: "/pages/workOrder/orderList?type=3",
          role: "",
        },
        {
          text: "已完成",
          url: "/static/images/user/completedOrder.png",
          path: "/pages/workOrder/orderList?type=4",
          role: "",
        },
        {
          text: "申诉",
          url: "/static/images/user/appealOrder.png",
          path: "/pages/workOrder/orderList?type=5",
          role: "",
        },
      ],
    };
  },
  filters: {
    filterMemberLevel(val) {
      if (!val) return "普通用户";
      return val.name;
    },
    marketingTypeTag(marketingType) {
      let tag;
      switch (marketingType) {
        case "discount":
          tag = $mAssetsPath.discountTag;
          break;
        case "bargain":
          tag = $mAssetsPath.bargainTag;
          break;
        case "group_buy":
          tag = $mAssetsPath.groupTag;
          break;
        case "wholesale":
          tag = $mAssetsPath.wholesaleTag;
          break;
      }
      return tag;
    },
  },
  computed: {},
  // 用户点击分享
  onShareAppMessage() {
    let shareParams = {
      title: "本印猫",
      desc: "一家提供一站式耗材、零件采购、维修服务的文印平台",
      path: "/pages/home/<USER>",
    };
    return shareParams;
  },

  async onShow() {
    // await this.$mHelper.loadVerify();
    await this.initData();
    // await this.getOrderCount();
  },
  created() {},
  // #ifndef MP
  onNavigationBarButtonTap(e) {
    const index = e.index;
    if (index === 0) {
      this.navTo("/pages/system/set");
    } else if (index === 1) {
      // #ifdef APP-PLUS
      // eslint-disable-next-line
      const pages = getCurrentPages();
      const page = pages[pages.length - 1];
      const currentWebview = page.$getAppWebview();
      currentWebview.hideTitleNViewButtonRedDot({
        index,
      });
      // #endif
      this.$mRouter.switchTab({
        route: "/pages/notify/notify",
      });
    }
  },
  //下拉
  onPullDownRefresh() {
    this.initData();
    uni.stopPullDownRefresh();
  },
  // #endif
  methods: {
    ...mapMutations(["login"]),
    // 数据初始化
    async initData() {
      this.newPub = 0;
      this.hasLogin = uni.getStorageSync("accessToken") || "123";
      if (this.hasLogin) {
        await this.getMemberInfo();
        // await this.getCollectionList();
        this.loading = false;
      } else {
        this.userInfo = {};
        this.loading = false;
        this.resetSectionData();
      }
      setTimeout(() => {
        uni.stopPullDownRefresh();
      }, 1000);

      setTimeout(() => {
        uni.stopPullDownRefresh();
      }, 1000);
    },
    // 获取用户的店铺信息
    async getMemberInfo() {
      await this.$http.get(`${shopInfo}`).then((res) => {
        console.log(res);
        if (res.data) {
          const customerId = uni.getStorageSync("customerId");
          const userInfo =
            (customerId &&
              res.data.find((item) => item.customerId === customerId)) ||
            res.data[0];
          this.userInfo = userInfo || null;
          this.featureList[0].role = userInfo.role;
        }
        uni.setStorageSync("userInfo", this.userInfo);
        uni.setStorageSync("customerId", this.userInfo.customerId);
      });
    },
    // 获取关注列表
    async getCollectionList() {
      await this.$http
        .get(`${followList}?btype=1&pageNo=1&pageSize=10000`)
        .then((res) => {
          this.amountList[0].value = res.totalCount;
        });
      await this.$http
        .get(`${followList}?btype=2&pageNo=1&pageSize=10000`)
        .then((res) => {
          this.amountList[1].value = res.totalCount;
        });
      await this.$http
        .get(`${followList}?btype=3&pageNo=1&pageSize=10000`)
        .then((res) => {
          this.amountList[2].value = res.totalCount;
        });
    },
    // 清空个人中心的各模块状态
    resetSectionData() {
      this.userInfo = null;
      this.amountList[0].value = 0;
      this.amountList[1].value = 0;
      this.amountList[2].value = 0;
    },
    // 订单统计
    async orderCountStatisic() {
      await this.$http.get(`${orderCountStatisic}`).then((r) => {
        this.orderStatics = r;
        this.setCartNum(r.cartNum);
        this.amountList[1].value = r.couponNum || 0;
        this.orderSectionList[0].num = r.toPayCount;
        this.orderSectionList[1].num = r.toDeliverCount;
        this.orderSectionList[2].num = r.toReceiptCount;
        this.orderSectionList[3].num = r.toEvaluateCount;
        this.orderSectionList[4].num = r.backOrderCount;
        this.amountList[0].value = r.blance || 0;
      });
    },

    // 统一跳转接口,拦截未登录路由
    navTo(route) {
      if (route) {
        if (!this.hasLogin) {
          uni.removeStorageSync("backToPage");
          this.$mRouter.push({
            route: "/pages/user/login",
          });
        } else {
          // if (
          // 	route == "/pages/index/publish" ||
          // 	route == "/pages/index/collect"
          // ) {
          // 	uni.switchTab({
          // 		url: route,
          // 	});
          // } else {
          this.$mRouter.push({
            route,
          });
          // }
        }
      } else {
        wx.openCustomerServiceChat({
          extInfo: {
            url: "https://work.weixin.qq.com/kfid/kfca73cb4d1464a6a43",
          },
          corpId: "ww8f007820b431a55e",
          success(res) {},
        });
      }
    },

    swiTo(route) {
      if (!route) return;
      uni.reLaunch({
        url: route,
      });
      // uni.switchTab({
      //   url: route,
      // });
    },
    gosetting() {
      this.$mRouter.push({
        route: "/pages/system/set",
      });
    },
    /**
     *  会员卡下拉和回弹
     *  1.关闭bounce避免ios端下拉冲突
     *  2.由于touchmove事件的缺陷（以前做小程序就遇到，比如20跳到40，h5反而好很多），下拉的时候会有掉帧的感觉
     *    transition设置0.1秒延迟，让css来过渡这段空窗期
     *  3.回弹效果可修改曲线值来调整效果，推荐一个好用的bezier生成工具 http://cubic-bezier.com/
     */
    coverTouchstart(e) {
      if (pageAtTop === false) {
        return;
      }
      this.coverTransition = "transform .1s linear";
      startY = e.touches[0].clientY;
    },
    coverTouchmove(e) {
      moveY = e.touches[0].clientY;
      let moveDistance = moveY - startY;
      if (moveDistance < 0) {
        this.moving = false;
        return;
      }
      this.moving = true;
      if (moveDistance >= 80 && moveDistance < 100) {
        moveDistance = 80;
      }
      if (moveDistance > 0 && moveDistance <= 80) {
        this.coverTransform = `translateY(${moveDistance}px)`;
      }
    },
    coverTouchend() {
      if (this.moving === false) {
        return;
      }
      this.moving = false;
      this.coverTransition = "transform 0.3s cubic-bezier(.21,1.93,.53,.64)";
      this.coverTransform = "translateY(0px)";
    },
    // 跳转至商品详情
    navToProduct(type, id) {
      let route = `/pages/product/product?id=${id}`;
      switch (type) {
        case "discount":
          route = `/pages/marketing/discount/product?id=${id}`;
          break;
        case "bargain":
          route = `/pages/marketing/bargain/product?id=${id}`;
          break;
        case "group_buy":
          route = `/pages/marketing/group/product?id=${id}`;
          break;
        case "wholesale":
          route = `/pages/marketing/wholesale/product?id=${id}`;
          break;
      }
      this.$mRouter.push({
        route,
      });
    },
    // 切换店铺
    async handleSwitchShop() {
      let verify = await this.$http.post(this.$mHelper.verifyAccessToken);
      if (verify.data === 2) {
        this.$mHelper.toast("网络异常，请稍后再试...");
        this.$mHelper.relogin();
        return false;
      }
      this.$refs.shopRef.show();
    },
    // 订单点击
    handleItemClick(item) {
      console.log(item);
    },
    changeCheck(userInfo) {
      this.userInfo = userInfo;
      this.featureList[0].role = userInfo.role;
    },
    async getOrderCount() {
      await this.$http.get(orderCount).then((res) => {
        this.listData = this.listData.map((item) => {
          if (item.text === "待付款") {
            item.count = res.data?.waitPayCount;
          }
          if (item.text === "待收货") {
            item.count = res.data?.waitReceived;
          }
          return item;
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.hticon {
  font-size: 24px;
}

page {
  background: #f5f6f8;
}

.user {
  .user-section {
    height: 520upx;
    padding: 100upx 30upx 0;
    position: relative;

    .bg {
      position: absolute;
      left: 0;
      top: 0;
      width: 100vw;
      height: 60vw;
      opacity: 0.84;
    }

    .user-info-box {
      height: 180upx;
      position: relative;
      z-index: 1;

      .portrait-box {
        width: 100%;
        height: 130upx;
        display: flex;
        align-items: center;

        .portrait {
          width: 130upx;
          height: 130upx;
          border: 5upx solid #fff;
          border-radius: 50%;
        }

        .username {
          flex: 1;
          padding-left: 20upx;
          box-sizing: border-box;
          font-size: $font-lg + 6upx;
          color: $color-white;
          color: #323333;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        button {
          background-color: transparent;
          font-size: $font-lg + 6upx;
          color: $font-color-dark;
          border: none;
        }

        button::after {
          border: none;
        }
      }

      .change {
        width: 100%;
        text-align: right;
        margin-top: 20upx;

        .btn {
          width: 180upx;
          height: 60upx;
          background: linear-gradient(-45deg, #f89030 0%, #fd4e19 100%);
          border-radius: 27upx;
          padding: 14upx 28upx;
          font-size: 27upx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #ffffff;
          text-align: center;
        }
      }
    }

    .vip-card-box {
      display: flex;
      flex-direction: column;
      color: #f7d680;
      height: 120upx;
      // background: url('https://yzimg.jikebang.com/gczh/imgs/vip-card.png');
      background-size: 100% 100%;
      border-radius: 16upx 16upx 0 0;
      overflow: hidden;
      position: relative;
      padding: 20upx 24upx;
      margin-top: -30upx;

      .b-btn {
        position: absolute;
        right: 24upx;
        top: 24upx;
        width: 132upx;
        height: 40upx;
        text-align: center;
        line-height: 40upx;
        font-size: 22upx;
        color: #36343c;
        border-radius: 20px;
        background: linear-gradient(to left, #f9e6af, #ffd465);
        z-index: 1;
      }

      .tit {
        font-size: $font-base + 2upx;
        color: #f7d680;
        margin-bottom: 28upx;

        .iconfont {
          color: #f6e5a3;
          display: inline-block;
          margin-right: 16upx;
        }
      }
    }
  }

  .cover-container {
    margin-top: -150upx;
    padding: 0 30upx 20upx;
    position: relative;
    background-color: $page-color-base;

    .arc {
      position: absolute;
      left: 0;
      top: -34upx;
      width: 100%;
      height: 36upx;
    }

    .promotion-center {
      // background: #fff;
      background-color: transparent;
      margin-bottom: 20upx;
      position: relative;

      .title {
        font-size: 32rpx;
        font-family: PingFang SC;
        font-weight: bold;
        color: #323333;
        background-color: transparent;

        &::before {
          content: "";
          display: inline-block;
          width: 6rpx;
          height: 25rpx;
          margin-right: 12rpx;
          background: linear-gradient(0deg, #f89030 0%, #fd4e19 100%);
          border-radius: 3rpx;
        }
      }

      .list {
        margin-top: 20rpx;
        border-radius: 10rpx;
        padding: 30rpx;
        background: #fff;
        color: #333333;

        .list-box {
          display: flex;

          .item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;

            .img {
              height: 50rpx;
            }

            .text {
              margin-top: 12rpx;
              font-size: 24rpx;
              font-family: PingFang SC;
              font-weight: bold;
              color: #333333;
            }

            .badge {
              position: absolute;
              right: 10rpx;
              top: -20rpx;
            }
          }
        }
      }

      /*分类列表*/
      .category-list {
        width: 100%;
        padding: 0 0 30upx 0;
        border-bottom: solid 2upx #f6f6f6;
        display: flex;
        flex-wrap: wrap;

        .category {
          width: 33.3%;
          margin-top: 50upx;
          display: flex;
          justify-content: center;
          flex-wrap: wrap;
          position: relative;

          .img {
            width: 100%;
            display: flex;
            justify-content: center;
            text-align: center;

            .iconfont {
              font-size: $font-lg + 24upx;
            }
          }

          .text {
            width: 100%;
            display: flex;
            justify-content: center;
            font-size: 24upx;
            color: #3c3c3c;
          }

          .share-btn {
            height: 142upx;
            text-align: left;
            background: none;
            padding: 0;
            margin: 0;
          }

          .share-btn:after {
            border: none;
            border-radius: none;
          }
        }
      }
    }

    .tj-sction {
      @extend %section;
      display: flex;

      .tj-item {
        @extend %flex-center;
        flex: 1;
        flex-direction: column;
        margin: 30upx 0;
        font-size: $font-sm;
        color: #75787d;
        /*border-right: 2upx solid rgba(0, 0, 0, 0.2);*/
      }

      /*.tj-item:last-child {*/
      /*border-right: none;*/
      /*}*/
      .num {
        font-size: $font-base;
      }

      .red {
        color: $base-color;
      }
    }

    .order-section {
      @extend %section;
      // padding: 28upx 0;
      margin-top: 20rpx;

      .order-item {
        @extend %flex-center;
        width: 120upx;
        height: 120upx;
        border-radius: 10upx;
        font-size: $font-sm;
        color: $font-color-dark;
        position: relative;

        .text {
          font-size: 24upx;
        }
      }

      .badge {
        position: absolute;
        top: 0;
        right: 4upx;
      }

      .iconfont {
        font-size: 48upx;
      }

      .icon-shouhoutuikuan {
        font-size: 44upx;
      }
    }

    .history-section {
      background: #fff;
      margin-bottom: $spacing-sm;

      .h-list-history {
        margin: 0;
        border-radius: 10upx;
        white-space: nowrap;
        background-color: $page-color-base;

        .h-item-history {
          background-color: $color-white;
          display: inline-block;
          font-size: $font-sm;
          color: $font-color-base;
          width: 180upx;
          margin: $spacing-sm $spacing-sm 0 0;
          border-radius: 10upx;
          position: relative;
          top: 0;
          overflow: hidden;

          .h-item-img {
            width: 180%;
            height: 200upx;
            border-top-left-radius: 12upx;
            border-top-right-radius: 12upx;
            border-bottom: 1upx solid rgba(0, 0, 0, 0.01);
          }

          .tag {
            position: absolute;
            border-top-left-radius: 12upx;
            left: 0;
            right: 0;
            width: 60upx;
            height: 60upx;
          }

          .h-item-text {
            font-size: $font-sm;
            margin: $spacing-sm 4upx;
          }
        }
      }

      .no-foot-print {
        text-align: center;
        padding: 48upx 0;

        .no-foot-print-icon {
          font-size: $font-lg + 2upx;
          margin-right: 10upx;
        }
      }

      .share-btn {
        height: 102upx;
        text-align: left;
        background: none;
        padding: 0;
        margin: 0;
      }

      .share-btn:after {
        border: none;
        border-radius: none;
      }
    }
  }
}

%flex-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

%section {
  justify-content: space-around;
  display: flex;
  align-content: center;
  background: #fff;
  border-radius: 10upx;
}

.img {
  width: 100%;
  display: flex;
  justify-content: center;
  text-align: center;

  .iconfont {
    font-size: $font-lg + 24upx;
  }
}

.share-btn {
  height: 142upx;
  text-align: left;
  background: none;
  padding: 5px 0 0 0;
  margin: 0;

  .text {
    color: #303133;
    font-size: 24upx;
  }
}

.share-btn:after {
  border: none;
  border-radius: none;
}

.newpub {
  background: #ff7200;
  width: 38rpx;
  height: 38rpx;
  border-radius: 50%;
  text-align: center;
  line-height: 38rpx;
  font-size: 24rpx;
  color: #fff;
  position: absolute;
  right: 30%;
}
</style>
