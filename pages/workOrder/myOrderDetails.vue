<template>
	<view class="main">
		<view class="address">
			<view class="address-info">
				<view class="address-title">
					{{ details.customer.shopRecruitment }}
				</view>
				<view class="address-details" @click="openMap">
					{{ details.customer.address }}
				</view>
			</view>
			<view class="btns">
				<view class="btn" v-if="details.status.value !== 'to_be_settled' && details.status.value !== 'completed'" @click="toStore">耗材仓库</view>
				<view class="transfer" v-if="details.status.value !== 'completed'" @click="orderTransfer">工单转让</view>
			</view>
		</view>

		<view class="progress" v-if="details.status && details.status.value !== 'close'">
			<u-collapse :border="false" :value="['0']">
				<u-collapse-item title="维修进度跟踪" name="0">
					<view class="progress-details">
						<u-steps :current="getStepByStatus(details.currentProcess)" direction="column" activeColor="#FF541E" inactiveColor="#D9D9D9" dot>
							<u-steps-item :title="item.label" v-for="item in processList" :key="item.value" :desc="details[item.desc]"></u-steps-item>
						</u-steps>
					</view>
				</u-collapse-item>
			</u-collapse>
		</view>
		<view class="des">
			<view class="des-class">
				<view class="left-item">
					<image
						class="des-image"
						:src="
							details.customerDeviceGroup && details.customerDeviceGroup.deviceGroupImg.url
								? details.customerDeviceGroup.deviceGroupImg.url
								: require('../../static/images/top.png')
						"
						mode=""
					></image>
					<view class="des-class-details">
						<view class="model-number">
							{{ details.customerDeviceGroup.deviceGroup.label }}
						</view>
						<view class="model-brand">
							{{ details.brand + details.machine }}
						</view>
					</view>
				</view>
				<view class="right-item">
					<view class="btn" @click="historyRecord">历史维修记录</view>
					<view class="btn" @click="replacementRecord(details)">PM件更换记录</view>
				</view>
			</view>
			<view class="des-number des-boder">
				黑白计数器：
				<view class="count">{{ details.blackWhiteCount }}</view>
			</view>
			<!-- 	<view class="des-number des-boder" v-if="details.treatyType.value === '1230' || details.treatyType.value === '1202'">
				黑白废张数：
				<view class="count">{{ details.blackWhiteExclude ? details.blackWhiteExclude : 0 }}</view>
			</view> -->
			<view class="des-number des-boder">
				彩色计数器：
				<view class="count">{{ details.colorCount }}</view>
			</view>
			<!-- 	<view class="des-number des-boder" v-if="details.treatyType.value === '1230' || details.treatyType.value === '1202'">
				彩色废张数：
				<view class="count">{{ details.colorExclude ? details.colorExclude : 0 }}</view>
			</view> -->
			<view class="des-number des-boder">
				上次维修后到目前的印量：
				<view class="count">{{ details.printCount }}</view>
			</view>
			<view class="des-number des-boder">
				故障代码/卡纸代码：
				<view class="count">{{ details.errorCode }}</view>
			</view>
			<!-- <view class="look" @click="toKnowledgeBase">
        去知识库<u-icon name="arrow-right" color="#FF541E"></u-icon>
      </view> -->
			<!-- <view class="toast des-boder">
        (安装本印猫客户端，故障信息第一时间推送到您微信)
      </view> -->

			<view class="dex-title">故障描述:</view>
			<view class="des-text des-boder" style="word-break: break-all">
				{{ details.excDesc }}
			</view>
			<view class="dex-title">故障照片:</view>
			<view class="des-image-list des-boder">
				<image v-for="(item, index) in details.excPics" :key="item.key" :src="item.url" @click="previewImage(details.excPics, index)" mode=""></image>
			</view>
			<view class="name des-boder">
				<view class="name-top">
					<view class="name-left">
						报修客户：
						<view class="staust">
							<text>{{ details.customerStaff.name }}</text>
						</view>
					</view>
					<view class="name-right">
						<view class="phone" @click="callToCustomer">
							联系客户
							<u-icon name="phone-fill" size="22" color="#FF541E"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<view class="des-number" v-if="details.expectArriveTime">
				期望工程师上门时间：
				<view class="count">{{ details.expectArriveTime }}</view>
			</view>
			<view class="des-number" v-if="details.prospectArriveTime">
				工程师预计上门时间：
				<view class="count">{{ details.prospectArriveTime }}</view>
			</view>

			<view class="des-number des-boder">
				工单号：
				<view class="count">{{ details.code }}</view>
			</view>
			<!-- <view class="des-number">
        报修发起时间：<view class="count">{{ details.createdAt }}</view>
      </view>
      <view class="des-number" v-if="details.actualArriveTime">
        到店时间：<view class="count">{{ details.actualArriveTime }}</view>
      </view> -->
			<view class="des-number" v-if="details.travelTime">
				路途时长：
				<view class="count">{{ details.travelTime }}</view>
			</view>
			<view class="des-number" v-if="details.fixTime">
				维修时长：
				<view class="count">{{ details.fixTime }}</view>
			</view>
		</view>

		<view class="price">
			<view class="des-number des-boder">
				上门费：
				<view class="count">{{ details.visitPay }}元</view>
			</view>
			<view class="des-number des-boder" v-if="details.longWayVisitPay">
				远程上门费：
				<view class="count">{{ details.longWayVisitPay }}元</view>
			</view>

			<view class="des-number des-boder" v-if="details.repairPay">
				维修诊断费：
				<view class="count">{{ details.repairPay }}元</view>
			</view>
			<view class="des-number des-boder" v-if="details.actualReplacePay">
				零件更换费：
				<view class="count">{{ details.actualReplacePay }}元</view>
			</view>
			<view class="des-number des-boder" v-if="details.itemPay">
				维修耗材费用：
				<view class="count">{{ details.itemPay }}元</view>
			</view>
			<view class="des-number des-boder" v-if="details.engineerAdditionalPay">
				工程师追加费用：
				<view class="count">{{ details.engineerAdditionalPay }}元</view>
			</view>
			<view class="des-number des-boder" v-if="details.derateAmount">
				工程师减免费用：
				<view class="count">-{{ details.derateAmount }}元</view>
			</view>
			<view class="des-number des-boder">
				会员减免：
				<view class="count">-{{ details.discountAmount }}元</view>
			</view>
			<view class="des-number des-boder" v-if="details.additionalPay">
				客户追加报酬：
				<view class="count">{{ details.additionalPay }}元</view>
			</view>
			<view class="des-number des-boder">
				工程师减免费用：
				<view class="count">
					<input v-if="details.status.value === 'engineer_arrive'" type="text" v-model="derateAmount" @input="handleChangeInput" placeholder="请输入减免费用" value="" />
					<text v-else>-{{ details.derateAmount ? details.derateAmount : '0.00' }}</text>
					元
				</view>
			</view>

			<view class="des-number des-boder">
				应付维修费用：
				<view class="count">
					合计
					<text style="color: #ff541e; font-weight: bold">¥{{ details.totalPay }}</text>
					元
				</view>
			</view>
			<view class="des-number des-boder" v-if="details.treatyType.value == '1230' || details.treatyType.value == '1202'" style="margin-bottom: 40rpx">
				实付维修费用：
				<view class="count">
					合计
					<text style="color: #ff541e; font-weight: bold">¥0</text>
					元
				</view>
			</view>

			<!-- 工程师工程师接单状态 -->
			<view class="sbumit" v-if="details.status.value === 'engineer_receive'">
				<view class="sbumit-button" @click="handleBtnClick('开始出发')">开始出发</view>
				<!-- <view class="sbumit-button" @click="handleBtnClick('取消工单')">
          取消工单
        </view> -->
			</view>
			<!-- 工程师出发状态 -->
			<view class="sbumit" v-if="details.status.value === 'engineer_departure'">
				<view class="sbumit-button" @click="handleBtnClick('确认到店')">确认到店</view>
				<!-- <view class="sbumit-button" @click="handleBtnClick('取消工单')">
          取消工单
        </view> -->
			</view>
			<!-- 工程师到店维修状态 -->
			<!-- <view
        class="sbumit"
        v-if="
          (details.currentProcess === 'ENGINEER_ARRIVE' &&
            details.cancelStatus === 'CUSTOMER_CANCEL') ||
          (details.currentProcess === 'ENGINEER_DEPARTURE' &&
            details.cancelStatus === 'CUSTOMER_CANCEL') ||
          (details.currentProcess === 'ENGINEER_RECEIVE' &&
            details.cancelStatus === 'CUSTOMER_CANCEL')
        "
      >
        <view class="sbumit-button" @click="handleBtnClick('同意取消')">
          同意取消
        </view>
        <view class="sbumit-button" @click="handleBtnClick('驳回')">
          驳回
        </view>
      </view> -->
			<!-- 工程师到店维修状态 -->
			<view class="sbumit sbumits" v-if="details.status.value === 'engineer_arrive'">
				<view class="sbumit-button" @click="handleSave">确认修改费用</view>
				<view class="sbumit-button" @click="handleBtnClick('填写维修报告')">填写维修报告</view>
			</view>
			<!-- 状态：待确认维修报告、待结算、已完成 -->
			<view class="sbumit" v-if="details.status.value === 'wait_confirmed_report' || details.status.value === 'to_be_settled' || details.status.value === 'completed'">
				<view class="sbumit-button" @click="handleBtnClick('查看维修报告')">查看维修报告</view>
			</view>
		</view>
		<!-- 取消工单弹框 -->

		<!-- 选择维修工程师 -->
		<view class="rule-model" v-if="islookRule">
			<view class="rule-content">
				<view class="rule-title">
					<u-icon color="#ffffff" name="close"></u-icon>
					选择维修工程师
					<u-icon name="close" @click="islookRule = false"></u-icon>
				</view>
				<view class="socrll-list">
					<view class="socrll-item" v-for="(item, index) in workerList" :key="index">
						<view class="socrll-left">
							<u-icon color="#FF541E" name="account-fill"></u-icon>
							<view class="number">{{ item.name }}</view>
						</view>

						<view class="socrll-right">
							{{ item.skillExp.label }}
							<text
								@click="engineerId == item.id ? (engineerId = '') : (engineerId = item.id), (engineerName = item.name)"
								class="iconfont"
								:class="engineerId == item.id ? 'text-orange iconradiobox' : 'iconradioboxblank'"
							></text>
						</view>
					</view>
				</view>
			</view>
			<view class="button-content">
				<view class="button button-left" @click.stop="(engineerId = ''), (islookRule = false)">取消</view>
				<view class="button button-right" @click="confrimTransfer">确认</view>
			</view>
		</view>
		<rfLoading isFullScreen :active="isLoading"></rfLoading>
	</view>
</template>

<script>
import {
	cancelWorkOrder,
	getWorkOrderDetail,
	cancelAcceptCancelOrder,
	goWorkOrder,
	arriveWorkOrder,
	submitReport,
	getIgnoreLocation,
	discountWorkOrder,
	engineerList,
	assignEngineer
} from '@/api/workOrder';
import { debounce } from '@/utils/index.js';
const userInfo = uni.getStorageSync('userInfo');
export default {
	components: {},
	data() {
		return {
			paddingTop: '',
			price: 300,
			disabled: true,
			isLoading: true,
			detailId: null,
			details: {},
			userInfo: userInfo,
			islookRule: false,
			derateAmount: '', // 工程师减免费用
			workerList: [], // 工程师列表
			engineerId: '',
			engineerName: '',
			processList: [
				{ value: 'CREATE', label: '发起报修', desc: 'createdAt' },
				{
					value: 'ENGINEER_RECEIVE',
					label: '工程师接单',
					desc: 'orderReceiveTime'
				},
				{
					value: 'ENGINEER_DEPARTURE',
					label: '工程师出发',
					desc: 'departureTime'
				},
				{
					value: 'ENGINEER_ARRIVE',
					label: '到店维修',
					desc: 'actualArriveTime'
				},
				{
					value: 'WAIT_CONFIRM',
					label: '确认维修报告',
					desc: 'sendReportTime'
				},
				{ value: 'DONE', label: '已完成', desc: 'completedAt' }
			]
		};
	},
	onShow() {
		if (this.detailId) {
			this.loadData();
		}
	},
	// 下拉刷新
	onPullDownRefresh() {
		this.loadData();
		uni.stopPullDownRefresh();
	},
	created() {},
	onLoad: function ({ id }) {
		console.log(id);
		if (!id) {
			uni.navigateTo({
				url: `/pages/workOrder/myWorkOrder`
			});
			return;
		}
		this.detailId = id;
		this.loadData();
	},
	methods: {
		onLongTap() {
			const { customerRegion, customer } = this.details;
			const { address } = customer;
			uni.setClipboardData({ data: `${customerRegion}${address}` });
		},
		openMap() {
			const {
				customer: { address, location }
			} = this.details;
			wx.getLocation({
				type: 'gcj02',
				success: function (res) {
					const latitude = parseFloat(location.latitude) || res.latitude;
					const longitude = parseFloat(location.longitude) || res.longitude;
					wx.openLocation({
						latitude,
						longitude,
						name: address,
						scale: 18,
						success: function (result) {
							console.log(result);
						},
						fail: function (error) {
							uni.showToast({
								title: '打开地图失败',
								icon: 'none'
							});
						}
					});
				},
				fail: function (err) {
					uni.showToast({
						title: '定位失败',
						icon: 'none'
					});
				}
			});
		},

		/**
		 *@description 工单转让
		 */
		orderTransfer() {
			engineerList(this.details.productId)
				.then((res) => {
					this.workerList = res.data;
					this.islookRule = true;
				})
				.catch((err) => {
					uni.showToast({
						title: err || '系统出错啦，请稍后再试！',
						icon: 'none'
					});
				});
		},
		/**
		 * @@description 确认工单转让
		 */
		confrimTransfer() {
			const that = this;
			if (!that.engineerId) {
				uni.showToast({
					title: '请选择转让工程师',
					icon: 'none'
				});
				return;
			}
			uni.showModal({
				title: '提示',
				content: `确定将工单转让给 ${that.engineerName} 吗？`,
				success: function (res) {
					if (res.confirm) {
						assignEngineer({
							id: that.details.id,
							engineerId: that.engineerId
						})
							.then((res) => {
								setTimeout(() => {
									that.engineerId = '';
									that.engineerName = '';
									that.islookRule = false;
									uni.redirectTo({
										url: '/pages/workOrder/myWorkOrder'
									});
								}, 300);
							})
							.catch((err) => {
								uni.showToast({
									title: err || '系统出错啦，请稍后再试！',
									icon: 'none'
								});
							});
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				}
			});
		},
		handleChangeInput: debounce(function (e) {
			this.derateAmount = e.detail.value;
			this.handleDerateAmount();
		}, 500),
		/**
		 * @description 工程师减免费用
		 */
		handleDerateAmount() {
			const { detailId, derateAmount } = this;
			const params = {
				id: detailId,
				derateAmount: Number(derateAmount) // 转换为分
			};
			discountWorkOrder(params)
				.then((res) => {
					uni.showToast({
						title: '修改成功',
						icon: 'none'
					});
					this.loadData();
				})
				.catch((err) => {
					uni.showToast({
						title: err || err.message || '系统出错啦，请稍后再试！',
						icon: 'none'
					});
				});
		},
		handleSave() {
			this.handleDerateAmount();
		},
		async loadData() {
			this.isLoading = true;
			try {
				const result = await getWorkOrderDetail(this.detailId);
				console.log(result);
				if (result.code === 200) {
					this.details = result.data;
					uni.setStorageSync('orderDetails', this.details);
				}
			} catch (err) {
				console.error(err);
			} finally {
				this.isLoading = false;
			}
		},
		getStepByStatus(status) {
			return this.processList.findIndex((item) => item.value === status);
		},

		// 历史维修记录
		historyRecord() {
			console.log('历史维修记录');
			console.log(this.details.deviceGroupId);
			let id = this.details.deviceGroupId;
			uni.navigateTo({
				url: `/pages/workOrder/historyRecord?id=${id}`
			});
		},
		// PM件更换记录
		replacementRecord(item) {
			let id = item.customerDeviceGroup.id;
			let productInfo = item.brand + '/' + item.machine;
			let deviceGroup = item.customerDeviceGroup.deviceGroup.label;
			uni.navigateTo({
				url: `/pages/workOrder/pm?productInfo=${productInfo}&deviceGroup= ${deviceGroup}&id=${id}`
			});
		},
		// 联系工程师
		callToCustomer() {
			uni.makePhoneCall({
				phoneNumber: this.details.customerStaff.tel,
				success: () => {
					console.log('拨打电话成功');
				},
				fail: (err) => {
					console.log('拨打电话失败', err);
				}
			});
		},

		// 开始出发
		async startGo() {
			console.log('开始出发');
			const _this = this;
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					goWorkOrder({
						id: _this.detailId,
						location: {
							longitude: res.longitude,
							latitude: res.latitude,
							system: 'GCJ_02'
						}
					})
						.then((res) => {
							console.log(res);
						})
						.catch((err) => {
							console.error(err);
						})
						.finally(() => {
							_this.loadData();
						});
				},
				fail: function (err) {
					console.log(err, '4++++++++++');
					uni.showToast({
						title: '获取地理位置失败',
						icon: 'none'
					});
					const userId = uni.getStorageSync('userInfo').id;
					getIgnoreLocation().then((res) => {
						console.log(res, '44444444444');
						if (res.code === 200 && res.data.includes(userId)) {
							goWorkOrder({
								id: _this.detailId,
								location: {
									longitude: '',
									latitude: '',
									system: 'GCJ_02'
								}
							})
								.then((res) => {
									console.log(res);
								})
								.catch((err) => {
									console.error(err);
								})
								.finally(() => {
									_this.loadData();
								});
						}
					});
					// 根据具体错误信息进行处理
				}
			});
		},
		// 工程师 取消工单
		handleCancelWorkOrder() {
			uni.showModal({
				title: '提示',
				content: '取消工单后，将无法恢复哟。\n（接单后，您取消工单需要店铺确认后才可生效。）',
				success: async (res) => {
					if (res.confirm) {
						try {
							this.isLoading = true;
							const result = await cancelWorkOrder(this.detailId);
							console.log(result);
							if (result.code === 200) {
								uni.showToast({
									title: '取消成功',
									icon: 'none',
									duration: 2000,
									success: () => {
										setTimeout(() => {
											uni.$emit('refreshWorkOrderList');
											uni.navigateBack();
										}, 2000);
									}
								});
							} else if (result.code === 406) {
								uni.showToast({
									title: '取消失败',
									icon: 'none'
								});
								return;
							}
						} catch (err) {
							console.error(err);
						} finally {
							this.isLoading = false;
						}
					}
				}
			});
		},
		// 工程师 确认到店
		handleConfirmArrival() {
			console.log('工程师 确认到店');
			const _this = this;
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					arriveWorkOrder({
						id: _this.detailId,
						location: {
							longitude: res.longitude,
							latitude: res.latitude,
							system: 'GCJ_02'
						}
					})
						.then((res) => {
							console.log(res);
						})
						.catch((err) => {
							console.error(err);
						})
						.finally(() => {
							_this.loadData();
						});
				},
				fail: function (err) {
					uni.showToast({
						title: '获取地理位置失败',
						icon: 'none'
					});
					const userId = uni.getStorageSync('userInfo').id;
					getIgnoreLocation().then((res) => {
						console.log(res, '44444444444');
						if (res.code === 200 && res.data.includes(userId)) {
							arriveWorkOrder({
								id: _this.detailId,
								location: {
									longitude: res.longitude,
									latitude: res.latitude,
									system: 'GCJ_02'
								}
							})
								.then((res) => {
									console.log(res);
								})
								.catch((err) => {
									console.error(err);
								})
								.finally(() => {
									_this.loadData();
								});
						}
					});
					// 根据具体错误信息进行处理
				}
			});
		},
		// 工程师 同意取消
		handleAgreeCancel() {
			uni.showModal({
				title: '提示',
				content: '同意取消工单后，该工单将关闭，无法恢复哟。',
				success: async (res) => {
					if (res.confirm) {
						try {
							this.isLoading = true;
							console.log('同意取消');
							const result = await cancelAcceptCancelOrder(this.detailId);
							console.log(result);
							if (result.code === 200) {
								console.log('取消成功');
								uni.showToast({
									title: '取消成功',
									icon: 'none'
									// duration: 2000,
									// success: () => {
									//   setTimeout(() => {
									//     uni.navigateBack();
									//   }, 2000);
									// },
								});
							} else if (result.code === 406) {
								uni.showToast({
									title: '取消失败',
									icon: 'none'
								});
								return;
							}
						} catch (err) {
							console.error(err);
						} finally {
							this.isLoading = false;
							this.loadData();
						}
					}
				}
			});
		},
		// 填写维修报告
		handleFillInRepairReport() {
			uni.navigateTo({
				url: `/pages/workOrder/repairReport?type=0`
			});
		},
		// 查看维修报告
		handleCheckRepairReport() {
			uni.navigateTo({
				url: `/pages/workOrder/reportDetails?id=` + this.detailId
			});
		},

		handleBtnClick(type) {
			console.log(type);
			switch (type) {
				case '开始出发':
					this.startGo();
					break;
				case '取消工单':
					this.handleCancelWorkOrder();
					break;
				case '同意取消':
					this.handleAgreeCancel();
					break;
				case '确认到店':
					this.handleConfirmArrival();
					break;
				case '填写维修报告':
					this.handleFillInRepairReport();
					break;
				case '查看维修报告':
					this.handleCheckRepairReport();
					break;
				default:
					break;
			}
		},
		previewImage(url, index) {
			uni.previewImage({
				current: index,
				urls: url.map((item) => item.url)
			});
		},
		toKnowledgeBase() {
			uni.navigateTo({
				url: '/pages/learn/index'
			});
		},
		toStore() {
			console.log(this.details);
			const customerId = this.details.customer.id;
			const deviceGroupId = this.details.deviceGroupId;
			uni.navigateTo({
				url: `/pages/workOrder/customStore?customerId=${customerId}&deviceGroupId=${deviceGroupId}`
			});
		}
	}
};
</script>

<style scoped lang="scss">
.main {
	width: 100%;
	height: 100%;
	background-color: #f5f6f8;
	padding-bottom: 40rpx;
	.head-nav {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 10000;
	}

	.rule-model {
		width: 100%;
		height: 100vh;
		display: flex;
		align-items: flex-end;
		justify-content: center;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 10;
		background: rgba(0, 0, 0, 0.5);

		.rule-content {
			width: 100%;
			min-height: 334rpx;
			background: #ffffff;
			border-radius: 13rpx;
			padding: 40rpx 30rpx;
			box-sizing: border-box;
			font-size: 27rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #0f0f0f;
			padding-bottom: 153rpx;
			line-height: 48rpx;
			position: absolute;

			.rule-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #0f0f0f;
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 22rpx;
			}

			.socrll-list {
				width: 100%;
				min-height: 200rpx;
				max-height: 500rpx;
				overflow: hidden;
				overflow-y: scroll;

				.socrll-item {
					width: 100%;
					height: 80rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.socrll-left,
					.socrll-right {
						display: flex;
						align-items: center;
					}
				}
			}
		}
		.button-content {
			width: 100%;
			height: 153rpx;
			display: flex;
			padding: 0 30upx;
			align-items: center;
			justify-content: center;
			background-color: #ffffff;
			z-index: 99;

			.button {
				width: 352rpx;
				height: 75rpx;
				font-size: 31rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.button-left {
				background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
				border-radius: 37rpx 0rpx 0rpx 37rpx;
			}

			.button-right {
				background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
				border-radius: 0rpx 37rpx 37rpx 0rpx;
			}
		}
	}

	.address {
		width: 100%;
		min-height: 130rpx;
		margin-top: 22rpx;
		background-color: #fff;
		padding: 24rpx 24rpx;
		box-sizing: border-box;
		position: relative;
		display: flex;
		justify-content: space-between;
		.address-info {
			width: 70%;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			.address-title {
				font-size: 27rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #0c0c0c;
				line-height: 39rpx;
			}

			.address-details {
				margin-top: 12rpx;
				font-size: 27rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #666666;
				line-height: 39rpx;
			}
		}
		.btns {
			width: 30%;
			display: flex;
			flex-direction: column;
			align-items: center;
			.btn {
				text-align: center;
				width: 80%;
				border: 1px solid #f2f2f2;
				border-radius: 10rpx;
				padding: 8rpx 12rpx;
				margin-bottom: 10rpx;
			}
			.transfer {
				width: 60%;
				height: 45rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border: none !important;
				color: #fff;
				border-radius: 37px;
				font-size: 25rpx;
				margin: 0;
				background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
			}
		}
	}

	.progress {
		width: 100%;
		margin-top: 22rpx;
		background-color: #fff;
		padding: 24rpx 24rpx 0rpx;
		box-sizing: border-box;

		.progress-title {
			font-size: 27rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #0c0c0c;
			line-height: 39rpx;
		}

		.progress-details {
			width: 100%;
			min-height: 240rpx;
			margin-top: 12rpx;

			/deep/.u-steps {
				height: 200px;
				margin-top: 8rpx;

				.u-steps-item {
					height: 40rpx !important;

					.u-text__value--content {
						font-size: 28rpx !important;
					}

					.u-text__value--main {
						color: #ff541e;
						font-size: 32rpx !important;
						line-height: normal;
					}
				}
			}
		}
	}

	.des {
		width: 100%;
		margin-top: 22rpx;
		background-color: #fff;
		padding: 24rpx;
		box-sizing: border-box;

		.des-class {
			width: 100%;
			padding-bottom: 24rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.left-item {
				width: 70%;
				display: flex;
				align-items: center;
			}
			.right-item {
				width: 30%;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				.btn {
					text-align: center;
					width: 100%;
					border: 1px solid #f2f2f2;
					border-radius: 10rpx;
					padding: 8rpx 12rpx;
					margin-bottom: 10rpx;
				}
			}
			.des-image {
				width: 154rpx;
				height: 154rpx;
				border-radius: 13rpx;
				margin-right: 16rpx;
			}

			.des-class-details {
				flex: 1;
				height: 154rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				font-size: 27rpx;
				font-family: PingFang SC;

				.model-number {
					width: 100%;
					font-weight: bold;
					color: #0c0c0c;
					line-height: 40rpx;
				}

				.model-brand {
					width: 100%;
					margin-top: 10rpx;
					font-weight: 500;
					color: #535353;
					line-height: 40rpx;
				}
			}
		}

		.des-number {
			width: 100%;
			min-height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #323333;

			.count {
				color: #666666;
			}
		}

		.look {
			margin-top: 10rpx;
			font-size: 27rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ff541e;
			text-align: right;
		}

		.toast {
			margin-top: 20rpx;
			padding-bottom: 20rpx;
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #323333;
		}

		.dex-title {
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #323333;
			margin-top: 20rpx;
		}

		.des-text {
			min-height: 127rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #666666;
		}

		.des-image-list {
			width: 100%;
			height: 206rpx;
			display: flex;
			align-items: center;

			image {
				width: 154rpx;
				height: 154rpx;
				//background: #474747;
				border-radius: 13rpx;
				margin-right: 20rpx;
			}
		}

		.name {
			width: 100%;
			padding: 26rpx 0;

			.name-top {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;

				.name-left {
					color: #323333;
					display: flex;
					align-items: center;

					.staust {
					}
				}

				.name-right {
					color: #666666;
				}
			}

			.phone {
				width: 100%;
				height: 44rpx;
				margin-top: 22rpx;

				/deep/.u-icon {
					float: right;
				}
			}
		}
	}

	.time,
	.price {
		width: 100%;
		margin-top: 22rpx;
		background-color: #fff;
		padding: 0 24rpx;
		box-sizing: border-box;
	}

	.des-number {
		width: 100%;
		min-height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #323333;

		.count {
			display: flex;
			align-items: center;
			color: #666666;

			input {
				min-width: 200rpx;
				text-align: right;
				margin-right: 4rpx;
				border: 2rpx solid #f6f6f6;
				width: 200rpx;
			}
		}

		.des-pay {
			width: 100%;
			padding: 16rpx 0;
			display: flex;
			align-items: center;
			flex-direction: column;

			.add-pay {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
			}

			.price-change {
				width: 100%;
				height: 40rpx;
				font-size: 27rpx;
				color: #ff541e;
				text-align: right;
			}
		}
	}

	.des-boder {
		border-bottom: 1rpx solid #f3f3f3;
	}

	.sbumit {
		width: 100%;
		height: 210rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.sbumit-button {
			width: 705rpx;
			height: 80rpx;
			background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
			border-radius: 40rpx;
			font-size: 31rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
	.sbumits {
		.sbumit-button {
			&:first-child {
				border-radius: 40rpx 0 0 40rpx !important;
			}
			&:last-child {
				border-radius: 0 40rpx 40rpx 0 !important;
			}
		}
	}
}
::v-deep .u-collapse-item__content__text {
	padding: 0 !important;
}
::v-deep .u-cell__body {
	padding: 20rpx 0 !important;
	font-weight: bold;
}
</style>
