<template>
	<view class="pages">
		<view class="content-box">
			<view class="info-list">
				<view class="list-more">
					<view class="title">故障描述：</view>
					<view class="content">
						<view class="more">
							<textarea
								:maxlength="500"
								class="textarea"
								:value="form.question"
								@input="inputHandle($event, 'question', 500)"
								@confirm="inputHandle($event, 'question', 500)"
								placeholder="请尽可能的描述清楚机器问题，越详细越有利于工程师快速给出维修建议。"
							/>
							<view class="text-box">{{ form.question.length }}/500</view>
						</view>
					</view>
				</view>
				<view class="list-more">
					<view class="title">添加图片</view>
					<view class="content">
						<view class="more">
							<FileUpload @submit="handleQuestionSubmit" :limit="9" :fileList="questionImgList" :show-title="false"></FileUpload>
						</view>
					</view>
				</view>
				<view class="list-more">
					<view class="title">解决措施：</view>
					<view class="content">
						<view class="more">
							<textarea
								:maxlength="500"
								class="textarea"
								:value="form.measure"
								@input="inputHandle($event, 'measure', 500)"
								@confirm="inputHandle($event, 'measure', 500)"
								placeholder="解决措施。"
							/>
							<view class="text-box">{{ form.measure.length }}/500</view>
						</view>
					</view>
				</view>
				<view class="list-more">
					<view class="title">添加图片</view>
					<view class="content">
						<view class="more">
							<FileUpload @submit="handleMeasureSubmit" :limit="9" :fileList="measureImgList" :show-title="false"></FileUpload>
						</view>
					</view>
				</view>

				<view class="list-more">
					<view class="title">下次注意事项：</view>
					<view class="content">
						<view class="more">
							<textarea
								:maxlength="500"
								class="textarea"
								:value="form.announcements"
								@input="inputHandle($event, 'announcements', 500)"
								@confirm="inputHandle($event, 'announcements', 500)"
								placeholder="下次注意事项。"
							/>
							<view class="text-box">{{ form.announcements.length }}/500</view>
						</view>
					</view>
				</view>
				<!-- 				<view class="list-more">
					<view class="title">添加图片</view>
					<view class="content">
						<view class="more">
							<FileUpload @submit="handleMeasureSubmit" :limit="9" :fileList="measureImgList" :show-title="false"></FileUpload>
						</view>
					</view>
				</view> -->

				<view class="list">
					<view class="title">现象分类</view>
					<view class="content input-border" style="padding: 6rpx 0">
						<text class="right-input m-w-100" @click="isShow1 = true">
							{{ form.phenomenon.label }}
						</text>
						<i @click="isShow1 = true" class="date-cion"></i>
						<u-picker :show="isShow1" :columns="columns1" keyName="label" @confirm="(e) => handleConfirm(e, 'phenomenon')" @cancel="isShow1 = false"></u-picker>
					</view>
				</view>
				<view class="list">
					<view class="title">原因分类</view>
					<view class="content input-border" style="padding: 6rpx 0">
						<text class="right-input m-w-100" @click="isShow2 = true">
							{{ form.reason.label }}
						</text>
						<i @click="isShow2 = true" class="date-cion"></i>
						<u-picker :show="isShow2" :columns="columns2" keyName="label" @confirm="(e) => handleConfirm(e, 'reason')" @cancel="isShow2 = false"></u-picker>
					</view>
				</view>
				<view class="list">
					<view class="title">处理类型</view>
					<view class="content input-border" style="padding: 6rpx 0">
						<text class="right-input m-w-100" @click="isShow3 = true">
							{{ form.handleType.label }}
						</text>
						<i @click="isShow3 = true" class="date-cion"></i>
						<u-picker :show="isShow3" :columns="columns3" keyName="label" @confirm="(e) => handleConfirm(e, 'handleType')" @cancel="isShow3 = false"></u-picker>
					</view>
				</view>
				<view class="list">
					<view class="title">故障组件</view>
					<view class="content input-border" style="padding: 6rpx 0">
						<text class="right-input m-w-100" @click="isShow4 = true">
							{{ form.breakdown.label }}
						</text>

						<i @click="isShow4 = true" class="date-cion"></i>
						<u-picker :show="isShow4" :columns="columns4" keyName="label" @confirm="(e) => handleConfirm(e, 'breakdown')" @cancel="isShow4 = false"></u-picker>
					</view>
				</view>

				<view class="list">
					<view class="title">黑白计数器：</view>
					<view class="content">
						<input class="right-input input-border" style="width: 240rpx" v-model="form.blackWhiteCount" @input="(e) => handleInput(e, 'blackWhiteCount')" />
					</view>
				</view>
				<view class="list" v-if="orderInfo.treatyType.value === '1230' || orderInfo.treatyType.value === '1202'">
					<view class="title">黑白废张数：</view>
					<view class="content">
						<input class="right-input input-border" style="width: 240rpx" v-model="form.blackWhiteExclude" />
					</view>
				</view>
				<view class="list">
					<view class="title">彩色计数器：</view>
					<view class="content">
						<input class="right-input input-border" style="width: 240rpx" v-model="form.colorCount" @input="(e) => handleInput(e, 'colorCount')" />
					</view>
				</view>
				<view class="list" v-if="orderInfo.treatyType.value === '1230' || orderInfo.treatyType.value === '1202'">
					<view class="title">彩色废张数：</view>
					<view class="content">
						<input class="right-input input-border" style="width: 240rpx" v-model="form.colorExclude" />
					</view>
				</view>
				<view class="list">
					<view class="title">上次维修后到目前的印量：</view>
					<view class="content">
						{{ form.printCount }}
					</view>
				</view>
				<view class="list">
					<view class="title">更换耗材零件：</view>
					<view class="content">
						<text></text>
					</view>
				</view>
				<view class="type-list">
					<view class="type-box">
						<view class="type-header">
							<view class="type-name">客户自配</view>
							<view class="type-btn">
								<!-- <view class="btn add" @click="addClick"></view> -->
								<view class="search-btn" @click="searchPart('customer')">查询</view>
							</view>
						</view>
						<view class="type-content">
							<view v-if="useCustomerPartList.length > 0" class="goods" v-for="(list, index) in useCustomerPartList" :key="list.id">
								<view class="thumb">
									<image :src="list.skuInfo.picUrl[0].url" mode="cover"></image>
								</view>
								<view class="item">
									<view class="goods-name">
										<view class="two-omit">
											<text>{{ list.itemName }}</text>
											<view class="btn-box">
												<view class="btn delete" @click="deletePart(index)"></view>
											</view>
										</view>
										<view class="source">oem编号：{{ list.oemNumber }}</view>
										<view class="attribute">
											<view class="attr" v-for="(attr, index) in list.skuInfo.saleAttrVals" :key="index">
												<text>{{ attr.val }}</text>
											</view>
										</view>
										<view class="location">
											<uni-data-select
												style="height: 100%"
												v-model="list.location"
												:localdata="locationRange"
												@change="handleLocationchange"
												clear
												placeholder="请选择零件更换位置"
											></uni-data-select>
										</view>
										<view class="info">
											<!-- 价格 -->
											<view class="goods-price">
												<view class="price">
													<text class="min">￥</text>
													<text class="max">{{ list.saleUnitPrice }}</text>
												</view>
											</view>
											<!-- 使用数量 -->
											<view class="num">
												<text class="add" @tap.stop="changeBuyNumber(list, 'sub')">
													<text class="iconfont icon-subtract"></text>
												</text>
												<view class="number">
													<text>{{ list.num }}</text>
												</view>
												<text class="add" @tap.stop="changeBuyNumber(list, 'add')">
													<text class="iconfont icon-add"></text>
												</text>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
						<!-- 				<view class="type-main">
									<view class="main-box" v-for="(item, index) in customerPickingData" :key="index">
								<view class="top-box">
									<view class="select-box">
										<ComSelect
											placeholder="选择零件"
											ref="selectRef"
											:options="customerOptions"
											:inputValue="item.name"
											@input="(e) => handleInputEvent(e, 'customerPicking', index)"
											@clear="handleClearClick('id', index)"
											@confirm="(e) => handleConfirmClick(e, 'id', index)"
										></ComSelect>
									</view>
									<view class="select-box">
										<ComSelect
											placeholder="更换位置"
											ref="selectRef"
											:options="positionList"
											:inputValue="item.position"
											@input="(e) => handleInputEvent(e, 'position', index)"
											@clear="handleClearClick('position', index)"
											@confirm="(e) => handleConfirmClick(e, 'position', index)"
											@focus="positionSearch(index)"
										></ComSelect>
									</view>

									<view class="btn-box">
										<view class="btn delete" @click="deleteClick(index)"></view>
									</view>
								</view>

								<view class="bottom-box">
									<view class="input-box">
										<input v-model="item.num" type="number" placeholder="数量" @input="(e) => updateTotal1(e, item, index)" />
									</view>
									<text class="desc">库存量：{{ item.stock }}</text>
								</view>
							</view>
						</view> -->
					</view>
				</view>
				<view class="type-list">
					<view class="type-box">
						<view class="type-header">
							<view class="type-name">工程师领料</view>
							<view class="type-btn">
								<!-- <view class="btn add" @click="addClick2"></view> -->
								<view class="search-btn" @click="searchPart('engineer')">查询</view>
							</view>
						</view>
						<view class="type-content">
							<view v-if="useEngineerPartList.length > 0" class="goods" v-for="(list, index) in useEngineerPartList" :key="list.id">
								<view class="thumb">
									<image :src="list.skuInfo.picUrl[0].url" mode="cover"></image>
								</view>
								<view class="item">
									<view class="goods-name">
										<view class="two-omit">
											<text>{{ list.itemName }}</text>
											<view class="btn-box">
												<view class="btn delete" @click="deletePart(index)"></view>
											</view>
										</view>
										<view class="source">oem编号：{{ list.oemNumber }}</view>
										<view class="attribute">
											<view class="attr" v-for="(attr, index) in list.skuInfo.saleAttrVals" :key="index">
												<text>{{ attr.val }}</text>
											</view>
										</view>
										<view class="location">
											<uni-data-select
												style="height: 100%"
												v-model="list.location"
												:localdata="locationRange"
												@change="handleLocationchange"
												clear
												placeholder="请选择零件更换位置"
											></uni-data-select>
										</view>
										<view class="info">
											<!-- 价格 -->
											<view class="goods-price">
												<view class="price">
													<text class="min">￥</text>
													<text class="max">{{ list.saleUnitPrice }}</text>
												</view>
											</view>
											<!-- 使用数量 -->
											<view class="num">
												<text class="add" @tap.stop="changeBuyNumber(list, 'sub')">
													<text class="iconfont icon-subtract"></text>
												</text>
												<view class="number">
													<text>{{ list.num }}</text>
												</view>
												<text class="add" @tap.stop="changeBuyNumber(list, 'add')">
													<text class="iconfont icon-add"></text>
												</text>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
						<!-- 		<view class="type-main">
										<view class="main-box" v-for="(item, index) in engineerPickingData" :key="index">
								<view class="top-box">
									<view class="select-box">
										<ComSelect
											placeholder="选择零件"
											ref="selectRef"
											:inputValue="item.name"
											:options="engineerOptions"
											@input="(e) => handleInputEvent2(e, 'engineerPicking', index)"
											@clear="handleClearClick2('id', index)"
											@confirm="(e) => handleConfirmClick2(e, 'id', index)"
										></ComSelect>
									</view>
									<view class="select-box">
										<ComSelect
											placeholder="更换位置"
											ref="selectRef"
											:options="positionList"
											:inputValue="item.position"
											@input="(e) => handleInputEvent2(e, 'position', index)"
											@clear="handleClearClick2('position', index)"
											@confirm="(e) => handleConfirmClick2(e, 'position', index)"
											@focus="positionSearch2(index)"
										></ComSelect>
									</view>
									<view class="btn-box">
										<view class="btn delete" @click="deleteClick2(index)"></view>
									</view>
								</view>

								<view class="bottom-box">
									<view class="input-box">
										<input v-model="item.num" type="number" @input="(e) => updateTotal2(e, item, index)" placeholder="数量" />
									</view>
									<text class="desc">库存量：{{ item.stock }}</text>
								</view>
							</view>
						</view> -->
					</view>
				</view>
				<!-- <view class="price">
          <view class="des-number des-boder">
            上门费：<view class="count">{{ orderInfo.visitPay }}元</view>
          </view>

          <view class="des-number des-boder" v-if="orderInfo.repairPay">
            会员减免：<view class="count">{{ orderInfo.repairPay }}元</view>
          </view>
          <view class="des-number des-boder" v-if="orderInfo.replacePay">
            零件更换费：<view class="count">{{ orderInfo.replacePay }}元</view>
          </view>
          <view class="des-number des-boder" v-if="workOrder.itemPay">
            维修耗材费用：<view class="count">{{ workOrder.itemPay }}元</view>
          </view>

          <view
            class="des-number des-boder"
            v-if="orderInfo.engineerAdditionalPay"
          >
            工程师追加费用：<view class="count"
              >{{ orderInfo.engineerAdditionalPay }}元</view
            >
          </view>
          <view class="des-number des-boder" v-if="orderInfo.derateAmount">
            工程师减免费用：<view class="count"
              >-{{ orderInfo.derateAmount }}元</view
            >
          </view>
          <view class="des-number des-boder">
            维修折扣费：<view class="count"
              >-{{ orderInfo.discountAmount }}元</view
            >
          </view>
          <view class="des-number des-boder" v-if="orderInfo.additionalPay">
            客户追加报酬：<view class="count"
              >{{ orderInfo.additionalPay }}元</view
            >
          </view>

          <view class="des-number des-boder">
            应付维修费用：<view class="count"
              >合计
              <text style="color: #ff541e">¥{{ orderInfo.totalPay }}</text>
              元</view
            >
          </view>
        </view> -->
			</view>
		</view>
		<view class="button-content" style="margin-top: 100rpx; margin-bottom: 40rpx">
			<view class="button button-left" @click="saveFn(0)">保存草稿</view>
			<view class="button button-right" @click="saveFn(1)">确认提交</view>
		</view>
		<u-action-sheet title="确认使用零件" :show="showAction" :round="20" @close="handleCancel" safeAreaInsetBottom="true" closeOnClickOverlay>
			<view class="part-main">
				<view v-if="usePartList.length > 0" class="goods" v-for="list in usePartList" :key="list.id">
					<view class="left" @click.stop="checkGoods(list)">
						<view :class="selectParts.some((item) => item.id === list.id) ? 'check active iconfont icon-duihao' : 'check'"></view>
					</view>
					<view class="thumb">
						<image :src="list.skuInfo.picUrl[0].url" mode="cover"></image>
					</view>
					<view class="item">
						<view class="goods-name">
							<text class="two-omit">{{ list.itemName }}</text>
							<view class="source">oem编号：{{ list.oemNumber }}</view>
							<view class="attribute">
								<view class="attr" v-for="(attr, index) in list.skuInfo.saleAttrVals" :key="index">
									<text>{{ attr.val }}</text>
								</view>
							</view>
							<view class="info">
								<!-- 价格 -->
								<view class="goods-price">
									<view class="price">
										<text class="min">￥</text>
										<text class="max">{{ list.saleUnitPrice }}</text>
									</view>
								</view>
								<!-- 使用数量 -->
								<view class="num">
									<text class="add" @tap.stop="changeBuyNumber(list, 'sub')">
										<text class="iconfont icon-subtract"></text>
									</text>
									<view class="number">
										<text>{{ list.num }}</text>
									</view>
									<text class="add" @tap.stop="changeBuyNumber(list, 'add')">
										<text class="iconfont icon-add"></text>
									</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<rf-empty :info="'暂无可使用零件！'" v-if="usePartList.length === 0"></rf-empty>
			</view>
			<view class="sheet-footer">
				<view class="confirm-btn" @click.stop="confirmedUse">确认使用</view>
			</view>
		</u-action-sheet>
	</view>
</template>

<script>
const timeFormat = uni.$u.timeFormat;
import FileUpload from '@/components/file-upload/index.vue';
import ComSelect from '@/components/com-select/index.vue';
import {
	getDictTreeData,
	submitReport,
	getPopReport,
	savePopReport,
	getCurrentModelsConsumables,
	getCustomerItemStore,
	getLocationByPartId,
	calcPrintCount,
	calcPrintCountApi,
	dictTreeByCodeApi
} from '@/api/workOrder';

export default {
	components: {
		FileUpload,
		ComSelect
	},
	data() {
		return {
			isShow: false,
			currentDate: new Date(),
			orderInfo: uni.getStorageSync('orderDetails'),
			form: {
				// time: timeFormat(
				//   new Date(new Date().getTime() + 4 * 60 * 60 * 1000),
				//   "yyyy-mm-dd hh:MM"
				// ),
				// discounts: 0.0,
				question: '', // 问题
				measure: '', // 措施
				announcements: '', // 下次注意事项
				phenomenon: {
					label: '',
					value: ''
				}, //现象
				reason: {
					label: '',
					value: ''
				}, //原因
				handleType: {
					label: '',
					value: ''
				}, // 处理类型
				breakdown: {
					label: '',
					value: ''
				}, // 故障

				blackWhiteCount: '',
				colorCount: '',
				blackWhiteExclude: '', // 黑白废张数量
				colorExclude: '', // 彩色废张数量
				printCount: '0'
			},
			sourceType: ['album', 'camera'],
			// 故障图片
			questionImgList: [],
			// 措施图片
			measureImgList: [],
			isShow1: false,
			columns1: [['1', '2', '3']],
			isShow2: false,
			columns2: [['4', '5', '6']],
			isShow3: false,
			columns3: [['7', '8', '9']],
			isShow4: false,
			columns4: [['10', '11', '12']],
			// 客户选项数据
			customerOptions: [],
			// 客户零件详情
			customerPartDetail: [],
			// 客户自配数据
			customerPickingData: [
				{
					name: '',
					id: '',
					num: '',
					position: '',
					stock: ''
				}
			],
			positionList: [],
			// 工程师选项数据
			engineerOptions: [],
			// 工程师零件详情
			engineerPartDetail: [],

			// 工程师领料数据
			engineerPickingData: [
				{
					name: '',
					num: '',
					id: '',
					position: '',
					stock: '' // 库存
				}
			],
			_inputTimer: null,
			searchType: '',
			showAction: false,
			usePartList: [], // 拥有零件：客户自配、工程师携带
			useCustomerPartList: [], //客户使用到的零件
			useEngineerPartList: [], // 工程师使用到的零件
			selectParts: [],
			locationRange: []
		};
	},
	onReady() {},
	onLoad({ type }) {
		let id = this.orderInfo.id;
		this.getPageData();
		this.customerSearch();
		this.engineerSearch();
		this.getDictTreeByCode();
		// 填写报告
		if (type == '0') {
			this.form.blackWhiteCount = this.orderInfo.blackWhiteCount ?? '0';
			this.form.colorCount = this.orderInfo.colorCount ?? '0';
			this.form.printCount = this.orderInfo.printCount ?? '0';
			this.getStorageReportData(id);
			uni.setNavigationBarTitle({
				title: '填写维修报告'
			});
		} else {
			// 修改标题
			uni.setNavigationBarTitle({
				title: '查看维修报告'
			});
			console.log('12222222222');
			// 查看报告
		}
	},

	methods: {
		searchPart(type) {
			this.searchType = type;
			this.selectParts = [];
			this.usePartList = type === 'customer' ? this.customerPartDetail : this.engineerPartDetail;
			this.selectParts = type === 'customer' ? this.useCustomerPartList : this.useEngineerPartList;

			if (this.selectParts.length > 0) {
				this.usePartList.forEach((item) => {
					const selectedItem = this.selectParts.find((selectItem) => selectItem.id === item.id);
					if (selectedItem) {
						item.num = selectedItem.num;
					}
				});
			}

			this.showAction = true;
		},
		handleCancel() {
			this.showAction = false;
		},
		confirmedUse() {
			this.handleCancel();
			if (this.searchType === 'customer') {
				this.useCustomerPartList = this.selectParts;
			} else {
				this.useEngineerPartList = this.selectParts;
			}
		},
		async changeBuyNumber(data, type) {
			if (type == 'add') {
				if (data.num >= data.tempNum) {
					// data.tempNum = data.num;
					return;
				}
				data.num = data.num + 1;
				this.selectParts.forEach((item) => {
					if (item.id === data.id) {
						item.num = data.num;
					}
				});
			} else if (type == 'sub') {
				if (data.num > 1) {
					data.num = data.num - 1;
					this.selectParts.forEach((item) => {
						if (item.id === data.id) {
							item.num = data.num;
						}
					});
				}
			}
		},
		checkGoods(goods) {
			let found = false;
			this.selectParts = this.selectParts.filter((item) => {
				if (item.id === goods.id) {
					found = true;
					return false;
				}
				return true;
			});
			if (!found) {
				this.selectParts.push(goods);
			}
		},
		deletePart(index) {
			if (this.searchType === 'customer') {
				this.useCustomerPartList.splice(index, 1);
			} else {
				this.useEngineerPartList.splice(index, 1);
			}
		},
		getDictTreeByCode() {
			dictTreeByCodeApi(11000)
				.then((res) => {
					this.locationRange = res.data.map((item) => {
						return {
							text: item.label,
							value: item.label
						};
					});
				})
				.catch((err) => {
					this.$mHelper.toast(err || '获取零件更换位置失败！');
				});
		},
		inputHandle(e, attrName, maxLength) {
			clearTimeout(this._inputTimer);
			this._inputTimer = setTimeout(() => {
				let { value: inputVal } = e.detail;
				if (inputVal.length > maxLength) {
					inputVal = inputVal.slice(0, maxLength);
				}
				this.form[attrName] = inputVal;
			}, 100);
		},
		bindPickerChange(e) {
			this.index = e.detail.value;
		},
		getPageData() {
			// 获取页面数据
			let codeList = ['6000', '7000', '8000', '9000'];
			codeList.map((item, index) => [
				getDictTreeData(item).then((res) => {
					if (res.code == 200) {
						if (index == 0) {
							this.columns1 = [res.data];
						} else if (index == 1) {
							this.columns2 = [res.data];
						} else if (index == 2) {
							this.columns3 = [res.data];
						} else if (index == 3) {
							this.columns4 = [res.data];
						}
					}
				})
			]);
		},
		// 获取暂存报告
		getStorageReportData(id) {
			getPopReport(id)
				.then((res) => {
					console.log(res);
					if (res.data) {
						let report = res.data;
						if (report) {
							// 处理图片
							this.questionImgList = report.excDescPics;
							this.measureImgList = report.resolveDescPics;
							// 问题
							this.form.question = report.excDesc || '';
							// 措施
							this.form.measure = report.resolveDesc || '';
							// 下次注意事项
							this.form.announcements = report.announcements || '';
							// 现象分类
							this.form.phenomenon = report.excType;
							// 原因分类
							this.form.reason = report.reasonType;
							// 处理类型
							this.form.handleType = report.resolveType;
							// 故障组件
							this.form.breakdown = report.excUnit;
							// 黑白计数器
							this.form.blackWhiteCount = report.blackWhiteCount;
							// 彩色计数器
							this.form.colorCount = report.colorCount;
							this.form.printCount = report.printCount;
							// 耗材零件

							let partList = report.replaceInfoList;
							this.handlePartsData(partList);
						}
					}
				})
				.catch((err) => {
					console.log(err);
				});
		},
		// 处理耗材零件数据回显
		handlePartsData(data) {
			if (Array.isArray(data) && data.length > 0) {
				this.useCustomerPartList = [];
				this.useEngineerPartList = [];
				data.forEach((item) => {
					if (item?.itemStore?.userType === 'CUSTOMER') {
						this.useCustomerPartList.push({
							id: item.itemStoreId,
							num: item.num,
							name: item.itemStore.itemName,
							itemName: item.itemStore.itemName,
							position: item.location,
							stock: item.itemStore.num,
							tempNum: item.itemStore.num,
							skuInfo: item.itemStore.skuInfo,
							oemNumber: item.itemStore.oemNumber,
							saleUnitPrice: item.itemStore.saleUnitPrice
						});
					} else if (item?.itemStore?.userType === 'ENGINEER') {
						this.useEngineerPartList.push({
							id: item.itemStoreId,
							num: item.num,
							name: item.itemStore.itemName,
							itemName: item.itemStore.itemName,
							position: item.location,
							stock: item.itemStore.num,
							tempNum: item.itemStore.num,
							skuInfo: item.itemStore.skuInfo,
							oemNumber: item.itemStore.oemNumber,
							saleUnitPrice: item.itemStore.saleUnitPrice
						});
					}
				});
			}
		},
		// 保存
		saveFn(type) {
			console.log(this.customerPickingData);
			console.log(this.engineerPickingData);
			// 处理客户选择和工程师选择的数据
			// let list1 = this.customerPickingData.map((item) => {
			// 	return {
			// 		itemStoreId: item.id,
			// 		num: item.num,
			// 		location: item.position
			// 	};
			// });
			// let list2 = this.engineerPickingData.map((item) => {
			// 	return {
			// 		itemStoreId: item.id,
			// 		num: item.num,
			// 		location: item.position
			// 	};
			// });
			let list1 = this.useCustomerPartList.map((item) => {
				return {
					itemStoreId: item.id,
					num: item.num,
					location: item.location
				};
			});
			let list2 = this.useEngineerPartList.map((item) => {
				return {
					itemStoreId: item.id,
					num: item.num,
					location: item.location
				};
			});
			// let partList = list1.concat(list2).filter((item) => item.num !== '' && +item.num > 0);
			let partList = list1.concat(list2);

			if (type === 0) {
				let params = {
					engineerId: this.orderInfo.engineerId.id,
					workOrderId: this.orderInfo.id, // 工单id
					excDesc: this.form.question, // 原因描述
					excDescPics: this.questionImgList, // 	原因描述图片
					resolveDesc: this.form.measure, //解决措施
					resolveDescPics: this.measureImgList, //解决措施图片
					announcements: this.form.announcements, //下次注意事项

					excType: this.form.phenomenon, // 现象分类
					reasonType: this.form.reason, // 原因分类
					resolveType: this.form.handleType, // 处理类型
					excUnit: this.form.breakdown, // 故障组件

					blackWhiteCount: this.form.blackWhiteCount, // 黑白计数器
					blackWhiteExclude: this.form.blackWhiteExclude, // 黑白废张数
					colorCount: this.form.colorCount, // 彩色计数器
					colorExclude: this.form.colorExclude, // 彩色废张数
					printCount: this.form.printCount, // 上次维修后到目前的印量
					replaceInfoList: partList // 更换零件
				};
				console.log('保存草稿');
				savePopReport(params)
					.then((res) => {
						if (res.code === 200) {
							uni.showToast({
								title: '操作成功',
								icon: 'none',
								duration: 2000,
								success: () => {
									setTimeout(() => {
										uni.navigateBack();
									}, 2000);
								}
							});
						} else {
							uni.showToast({
								title: res.message,
								icon: 'none'
							});
						}
					})
					.catch((err) => {
						console.error(err);
					});
			}
			if (type === 1) {
				// 判断是否有必填项
				if (this.form.question === '') {
					this.$mHelper.toast('请填写故障描述');
					return;
				}
				if (this.questionImgList.length === 0) {
					this.$mHelper.toast('请上传故障描述图片');
					return;
				}
				if (this.form.measure === '') {
					this.$mHelper.toast('请填写解决措施');
					return;
				}
				if (this.measureImgList.length === 0) {
					this.$mHelper.toast('请上传解决措施图片');
					return;
				}
				if (this.form.announcements.length === 0) {
					this.$mHelper.toast('请填写下次注意事项');
					return;
				}
				// 选择零件耗材后必须填写数量
				let hasError = partList.some((item) => {
					if (item.itemStoreId && (!item.num || item.num == 0)) {
						return true;
					}
					return false;
				});

				if (hasError) {
					uni.showToast({
						title: '请输入零件数量',
						icon: 'none'
					});
					return;
				}
				partList = partList.filter((item) => item.itemStoreId && item.num > 0).map((item) => ({ itemStoreId: item.itemStoreId, num: item.num, location: item.location }));

				let params = {
					engineerId: this.orderInfo.engineerId.id,
					workOrderId: this.orderInfo.id, // 工单id
					excDesc: this.form.question, // 原因描述
					excDescPics: this.questionImgList, // 	原因描述图片
					resolveDesc: this.form.measure, //解决措施
					resolveDescPics: this.measureImgList, //解决措施图片
					announcements: this.form.announcements, //下次注意事项

					excType: this.form.phenomenon, // 现象分类
					reasonType: this.form.reason, // 原因分类
					resolveType: this.form.handleType, // 处理类型
					excUnit: this.form.breakdown, // 故障组件

					blackWhiteCount: this.form.blackWhiteCount, // 黑白计数器
					blackWhiteExclude: this.form.blackWhiteExclude, // 黑白废张数
					colorCount: this.form.colorCount, // 彩色计数器
					colorExclude: this.form.colorExclude, // 彩色废张数
					printCount: this.form.printCount, // 上次维修后到目前的印量
					replaceInfoList: partList // 更换零件
				};
				submitReport(params)
					.then((res) => {
						if (res.code === 200) {
							uni.showToast({
								title: '操作成功',
								icon: 'none',
								duration: 2000,
								success: () => {
									setTimeout(() => {
										uni.redirectTo({
											url: `/pages/workOrder/myOrderDetails?id=${this.orderInfo.id}`
										});
									}, 2000);
								}
							});
						}
					})
					.catch((err) => {
						console.error(err);
						uni.showToast({
							title: err.message || '提交失败'
						});
					});
			}
		},
		// 故障描述图片上传
		handleQuestionSubmit(data) {
			this.questionImgList = data;
			// data.map((ele) => [this.questionImgList.push(ele.url)]);
			// console.log(this.questionImgList);
		},
		// 解决措施图片上传
		handleMeasureSubmit(data) {
			this.measureImgList = data;
			// data.map((ele) => [this.measureImgList.push(ele.url)]);
			// console.log(this.measureImgList);
		},
		handleConfirm(e, props) {
			let data = e.value[0];
			this.form[props] = data;
			this.isShow1 = false;
			this.isShow2 = false;
			this.isShow3 = false;
			this.isShow4 = false;
		},

		// 每行新增
		addClick() {
			console.log('新增');
			this.customerPickingData.push({
				name: '',
				num: '',
				id: '',
				position: '',
				stock: ''
			});
			console.log(this.customerPickingData);
		},
		// 每行删除
		deleteClick(index) {
			console.log('删除');
			this.customerPickingData.splice(index, 1);
		},
		// 客户自选查询
		customerSearch(keyword) {
			let params = {
				customerId: this.orderInfo.customerId,
				deviceGroupId: this.orderInfo.deviceGroupId,
				itemName: keyword
			};
			getCustomerItemStore(params).then((res) => {
				try {
					if (res.code == 200 && res.data.length > 0) {
						let result = [];
						let setName = '';
						const data = res.data;
						this.customerPartDetail = data.map((item) => {
							return {
								...item,
								tempNum: item.num
							};
						});
						data.map((item) => {
							setName = item.itemName;
							item.skuInfo.saleAttrVals.map((v, i) => {
								setName = setName + ' / ' + v.val;
								if (item.skuInfo.saleAttrVals.length - 1 == i) {
									result.push({
										name: item.itemName,
										label: setName,
										value: item.id
									});
								}
							});
							return result;
						});
						if (result.length > 0) {
							this.customerOptions = result;
						} else {
							this.customerOptions = [];
						}
					}
				} catch (err) {
					console.log(err);
				}
			});
		},
		// 工程师查询
		engineerSearch(keyword) {
			let params = {
				deviceGroupId: this.orderInfo.deviceGroupId,
				itemName: keyword
			};
			getCurrentModelsConsumables(params).then((res) => {
				try {
					if (res.code == 200 && res.data.length > 0) {
						let result = [];
						let setName = '';
						const data = res.data;
						this.engineerPartDetail = data.map((item) => {
							return {
								...item,
								tempNum: item.num
							};
						});

						data.map((item) => {
							setName = item.itemName;
							item.skuInfo.saleAttrVals.map((v, i) => {
								setName = setName + ' / ' + v.val;
								if (item.skuInfo.saleAttrVals.length - 1 == i) {
									result.push({
										name: item.itemName,
										label: setName,
										value: item.id
									});
								}
							});
							return result;
						});
						if (result.length > 0) {
							console.log(result);
							this.engineerOptions = result;
						} else {
							this.engineerOptions = [];
						}
					}
				} catch (err) {
					console.log(err);
				}
			});
		},

		// 输入框input事件 -- 客户自选
		handleInputEvent(value, type, index) {
			console.log(value, type, index);
			if (type === 'customerPicking') {
				console.log('哈哈哈');
				this.customerSearch(value);
			} else if (type === 'position') {
				console.log('位置');
			}
		},
		// 判断输入数据的和不能超过数据总数
		updateTotal1(e, item, index) {
			//
			let other = this.customerPickingData.filter((it, i) => index !== i && it.id === item.id && it.num !== '').reduce((sum, it) => sum + parseInt(it.num), 0);

			let total = this.customerPartDetail.filter((it) => it.id === item.id)?.[0]?.num;
			if (total) {
				this.customerPickingData[index].stock = total;
				if (other + parseInt(e.detail.value) >= total) {
					this.$nextTick(() => {
						this.customerPickingData[index].num = total - other;
					});
					console.log('输入数量不能超过总数');
				} else {
					this.customerPickingData[index].num = e.detail.value;
				}
				console.log(this.customerPickingData[index].num);
			}
		},
		// 输入框input事件 -- 工程师领用
		handleInputEvent2(value, type, index) {
			console.log(value, type, index);
			if (type === 'engineerPicking') {
				console.log('哈哈哈');
				// 工程师查询
				this.engineerSearch(value);
			} else if (type === 'position') {
				console.log('位置');
			}
		},
		// 下拉框选中
		handleConfirmClick(data, props, index) {
			console.log(data);
			if (data.value) {
				this.customerPickingData[index][props] = data.value;
				if (props === 'id') {
					this.customerPickingData[index].name = data.label;
					this.customerPickingData[index].num = '';
					this.customerPickingData[index].position = '';
					let total = this.customerPartDetail.filter((it) => it.id === data.value)?.[0]?.num;
					console.log(total);
					if (total) {
						this.customerPickingData[index].stock = total;
					}
					this.positionSearch(index);
				}
			}
		},
		// 查询客户自配更换位置
		positionSearch(index) {
			this.positionList = [];
			let id = this.customerPickingData[index].id;
			if (!id) return;
			// 查询位置
			let partId = this.customerPartDetail.filter((it) => it.id === id)?.[0]?.partId;
			if (partId) {
				getLocationByPartId(partId).then((res) => {
					console.log(res);
					if (res.code === 200) {
						this.positionList = res.data.map((item) => {
							return {
								label: item,
								value: item
							};
						});
					}
				});
			}
		},

		// 下拉框清除
		handleClearClick(props, index) {
			this.customerPickingData[index][props] = '';
			if (props === 'id') {
				this.customerPickingData[index].name = '';
				this.customerPickingData[index].num = '';
				this.customerPickingData[index].stock = '';
				this.customerPickingData[index].position = '';
				this.positionList = [];
			}
		},

		// 每行新增
		addClick2() {
			console.log('新增');
			this.engineerPickingData.push({
				name: '',
				num: '',
				id: '',
				position: '',
				stock: ''
			});
			console.log(this.engineerPickingData);
		},
		// 每行删除
		deleteClick2(index) {
			console.log('删除');
			this.engineerPickingData.splice(index, 1);
		},
		// 判断输入数据的和不能超过数据总数
		updateTotal2(e, item, index) {
			//
			let other = this.engineerPickingData.filter((it, i) => index !== i && it.id === item.id && it.num !== '').reduce((sum, it) => sum + parseInt(it.num), 0);

			let total = this.engineerPartDetail.filter((it) => it.id === item.id)?.[0]?.num;
			if (total) {
				this.engineerPickingData[index].stock = total;
				if (other + parseInt(e.detail.value) >= total) {
					this.$nextTick(() => {
						this.engineerPickingData[index].num = total - other;
					});
					console.log('输入数量不能超过总数');
				} else {
					this.engineerPickingData[index].num = e.detail.value;
				}
				console.log(this.engineerPickingData[index].num);
			}
		},
		// 下拉框选中2
		handleConfirmClick2(data, props, index) {
			console.log(data);
			if (data.value) {
				this.engineerPickingData[index][props] = data.value;
				if (props === 'id') {
					this.engineerPickingData[index].name = data.label;
					this.engineerPickingData[index].num = '';
					this.engineerPickingData[index].position = '';
					let total = this.engineerPartDetail.filter((it) => it.id === data.value)?.[0]?.num;
					console.log(total);
					if (total) {
						this.engineerPickingData[index].stock = total;
					}
					// 查询位置
					this.positionSearch2(index);
				}
			}
		},
		// 下拉框清除2
		handleClearClick2(props, index) {
			this.engineerPickingData[index][props] = '';
			if (props === 'id') {
				this.engineerPickingData[index].name = '';
				this.engineerPickingData[index].num = '';
				this.engineerPickingData[index].stock = '';
				this.engineerPickingData[index].position = '';
				this.positionList = [];
			}
		},
		// 查询工程师自带更换位置
		positionSearch2(index) {
			console.log(index);
			this.positionList = [];
			let id = this.engineerPickingData[index].id;
			if (!id) return;
			// 查询位置
			let partId = this.engineerPartDetail.filter((it) => it.id === id)?.[0]?.partId;
			if (partId) {
				getLocationByPartId(partId).then((res) => {
					console.log(res);
					if (res.code === 200) {
						this.positionList = res.data.map((item) => {
							return {
								label: item,
								value: item
							};
						});
					}
				});
			}
		},

		async calcPrintCount() {
			const result = await calcPrintCountApi({
				blackWhiteCount: this.form.blackWhiteCount,
				colorCount: this.form.colorCount,
				deviceGroupId: this.orderInfo.deviceGroupId
			});
			if (result.code === 200) {
				this.form.printCount = result.data;
				this.$set(this.form, 'printCount', result.data);
			}
		},
		handleInput(e, key) {
			this.$set(this.form, key, e.detail.value);
			this.calcPrintCount();
		}
	}
};
</script>

<style lang="scss" scoped>
.icon-subtract {
	position: relative;
	margin: 0 20rpx;

	&::after {
		content: '';
		display: block;
		width: 25rpx;
		height: 5rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}
}

.icon-add {
	position: relative;
	margin: 0 20rpx;

	&::after {
		content: '';
		display: block;
		width: 25rpx;
		height: 5rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}

	&::before {
		content: '';
		display: block;
		width: 5rpx;
		height: 25rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba($color: #000, $alpha: 0.8);
	}
}
.sheet-footer {
	width: 100%;
	padding: 18rpx 0 42rpx;
	display: flex;
	justify-content: center;
	background-color: #fff;
	position: fixed;
	bottom: 0;
	left: 0;

	.confirm-btn {
		width: 80%;
		margin: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
		border-radius: 50px;
		color: #fff;
	}
}
.part-main {
	width: 100%;
	padding: 20rpx;
	background-color: #f6f6f6;
	max-height: 1000upx;
	min-height: 500upx;
	overflow: auto;
	padding-bottom: 160rpx;
}

.part-main,
.type-content {
	.goods {
		display: flex;
		width: 100%;
		height: 270rpx;
		padding: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;
		margin: 20rpx 0;
		&:first-child {
			margin-top: 0;
		}
		&:last-child {
			margin-bottom: 0;
		}

		.left {
			width: 10%;
			display: flex;
			align-items: center;
			.check {
				width: 35rpx;
				height: 35rpx;
				border: 1px solid #999999;
				border-radius: 50%;

				&.active {
					border: 1px solid #e5452f;
					color: #e5452f !important;
					line-height: 30rpx;
				}
			}
		}

		.thumb {
			display: flex;
			align-items: center;
			width: 30%;
			height: 100%;

			image {
				width: 160rpx;
				height: 160rpx;
				border-radius: 10rpx;
			}
		}
		.item {
			display: flex;
			align-items: center;
			width: 80%;

			.goods-name {
				width: 100%;
				text-align: left;

				.two-omit {
					display: flex;
					justify-content: space-between;
					font-size: 27rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #0c0c0c;
					line-height: 39rpx;
					margin-top: 20rpx;
					.btn-box {
						width: 45rpx;
						// height: 60rpx;
						display: flex;
						align-items: center;
						justify-content: flex-end;

						.btn {
							width: 25rpx;
							height: 25rpx;
						}

						.delete {
							background: url('@/static/images/icon_delete.png');
							background-size: 100% 100%;
						}
					}
				}
				.source {
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 300;
				}
				.desc {
					font-size: 27rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #535353;
					line-height: 39rpx;
				}

				.attribute {
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					gap: 15rpx;
					margin: 10rpx 0;

					.attr {
						display: flex;
						align-items: center;
						padding: 0 10rpx;
						height: 40rpx;
						background-color: #e8e8e8;
						border-radius: 10rpx;

						text {
							font-size: 24rpx;
							color: #333333;
						}
					}
				}

				.location {
					width: 60%;
					height: 50rpx;
					border: 1rpx solid #949494;
					padding: 0 10rpx;
					border-radius: 10rpx;
					font-size: 24rpx
					::v-deep .uni-select {
						height: 50rpx !important;
						font-size: 24rpx !important;
						.uni-select__input-placeholder{
							font-size: 24rpx !important;
						}
					}
				}

				.info {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.goods-price {
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						.price {
							text {
								color: #fe3b0f;
								font-size: 24rpx;
							}
							.min {
								font-size: 26rpx;
							}
							.max {
								font-size: 34rpx;
							}
						}
					}
					.num {
						display: flex;
						height: 40rpx;

						.add {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 60rpx;
							height: 40rpx;
							background-color: #ffffff;

							text {
								color: #212121;
								font-size: 40rpx;
							}
						}

						.number {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 80rpx;
							height: 40rpx;
							background-color: #f6f6f6;
							border-radius: 8rpx;
							text-align: center;

							text {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
				}
			}
		}
	}
}
.pages {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	padding-bottom: 40rpx;
	background: #f2f2f2;

	.content-box {
		width: 100%;
		height: 100%;
		background: #fff;
		margin-top: 20rpx;
	}

	.info-list {
		padding: 0 4%;

		.list {
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 100rpx;
			border-bottom: 2rpx solid #f6f6f6;

			.title {
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #323333;
			}

			.content {
				display: flex;
				align-items: center;
				margin-left: 20rpx;
				flex-wrap: nowrap;

				text {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #666666;
				}
			}
		}

		.list-more {
			width: 100%;
			border-bottom: 2rpx solid #f6f6f6;

			.right-input {
				text-align: right;
			}

			.title {
				padding: 20rpx 0;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #323333;
				position: relative;
				margin-left: 20rpx;
				min-width: 260rpx;

				&::before {
					content: '*';
					display: inline-block;
					width: 20rpx;
					height: 20rpx;
					font-size: 28rpx;
					color: #ff541e;
					position: absolute;
					top: 45%;
					transform: translateY(-50%);
					left: -20rpx;
				}
			}

			.content {
				display: flex;
				align-items: center;
				margin-left: 20rpx;

				text {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #666666;
					margin-left: 20rpx;
				}
			}

			.more {
				width: 100%;
			}

			.textarea {
				height: 140rpx;
			}

			.text-box {
				text-align: right;
			}
		}

		.type-list {
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			border-bottom: 2rpx solid #f6f6f6;

			.type-box {
				width: 100%;

				.type-header {
					display: flex;
					align-items: center;
					padding: 5rpx 0;

					.type-name {
						flex: 1;
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #323333;
						margin-left: 20rpx;
					}

					.type-btn {
						width: 140rpx;
						height: 56rpx;
						display: flex;
						align-items: center;
						justify-content: flex-end;
						.search-btn {
							width: 70%;
							height: 56rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							border: none !important;
							color: #fff;
							border-radius: 37px;
							font-size: 26rpx;
							margin: 0;
							background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
						}

						// .btn {
						// 	width: 25rpx;
						// 	height: 25rpx;
						// 	margin-left: 20rpx;
						// }

						// .add {
						// 	background: url('/static/images/icon_add.png');
						// 	background-size: 100% 100%;
						// }

						// .delete {
						// 	background: url('/static/images/icon_delete.png');
						// 	background-size: 100% 100%;
						// }
					}
				}

				.type-main {
					width: 100%;
					// margin-top: 10rpx;

					.main-box {
						width: 100%;
						margin-bottom: 10rpx;
					}

					.top-box {
						width: 100%;
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 10rpx;
					}

					.select-box {
						flex: 1;
						height: 60rpx;
						margin-right: 10rpx;

						&:nth-last-child(1) {
							margin-right: 0;
						}
					}

					.bottom-box {
						width: 100%;
						display: flex;
						align-items: center;
						margin-bottom: 10rpx;
					}

					.input-box {
						width: 200rpx;
						height: 60rpx;
						margin-right: 20rpx;

						input {
							border: 1px solid #e5e5e5;
							height: 60rpx;
							width: 100%;
							border-radius: 4rpx;
							padding: 0 10rpx;
						}
					}

					.btn-box {
						width: 45rpx;
						height: 60rpx;
						display: flex;
						align-items: center;
						justify-content: flex-end;

						.btn {
							width: 25rpx;
							height: 25rpx;
						}

						.add {
							background: url('/static/images/icon_add.png');
							background-size: 100% 100%;
						}

						.delete {
							background: url('/static/images/icon_delete.png');
							background-size: 100% 100%;
						}
					}
				}
			}
		}

		.right-input {
			text-align: right;
		}

		.input-border {
			border: 1px solid #e5e5e5;
			border-radius: 4rpx;
			padding: 0 10rpx;
		}

		.m-w-100 {
			display: inline-block;
			min-width: 200rpx;
			max-width: 400rpx;
			height: 40rpx;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.date-cion {
			margin-left: 20rpx;
			margin-right: 10rpx;
			display: inline-block;
			width: 20rpx;
			height: 12rpx;
			background: url('/static/images/home/<USER>') no-repeat;
			background-size: 100% 100%;
		}

		.price {
			width: 100%;
			margin-top: 22rpx;
			background-color: #fff;
			padding: 0 24rpx;
			box-sizing: border-box;
		}

		.des-number {
			width: 100%;
			min-height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #323333;

			.count {
				color: #666666;
			}
		}
	}

	.button-content {
		width: 100%;
		height: 105rpx;
		// position: fixed;
		// left: 0;
		// bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #fff;
		margin: 20rpx 0;

		.button {
			width: 352rpx;
			height: 75rpx;
			font-size: 31rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;

			&:first-child {
				border-radius: 40px 0 0 40px;
			}

			&:last-child {
				border-radius: 0 40px 40px 0;
			}

			&:first-child:last-child {
				border-radius: 40px;
			}
		}

		.button-left {
			background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
			/* border-radius: 37rpx 0rpx 0rpx 37rpx; */
		}

		.button-right {
			background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
			/* border-radius: 0rpx 37rpx 37rpx 0rpx; */
		}
	}
}
</style>
