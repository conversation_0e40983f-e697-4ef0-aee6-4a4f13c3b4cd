<template>
  <view class="pages">
    <view class="content-box">
      <view class="info-list">
        <view class="list-more">
          <view class="title">预计上门时间:</view>
          <view class="content">
            <text class="right-input m-w-100" @click="isShow = true">
              {{ form.prospectArriveTime }}
            </text>
            <i @click="isShow = true" class="date-cion"></i>
            <u-datetime-picker
              ref="datetimePicker"
              :show="isShow"
              mode="datetime"
              v-model="currentDate"
              :minDate="startDate"
              @confirm="handleConfirmClick"
              @cancel="handleCancelClick"
            ></u-datetime-picker>
          </view>
        </view>
        <view class="line"> </view>

        <view class="list-more">
          <view class="title"> 金额调整: </view>

          <view class="content">
            <view class="radio-box">
              <u-radio-group v-model="fixPrice">
                <u-radio label="保持原价" name="保持原价" @change="radioChange">
                </u-radio>
                <u-radio label="修改金额" name="修改金额" @change="radioChange">
                </u-radio>
              </u-radio-group>
            </view>
          </view>
        </view>
        <view class="list-more" v-if="fixPrice === '修改金额'">
          <view class="empty-title"></view>
          <view class="content">
            <view class="radio-box">
              <u-radio-group v-model="isAddPrice">
                <u-radio label="加价" name="加价" @change="priceRadioChange">
                </u-radio>
                <u-radio label="减免" name="减免" @change="priceRadioChange">
                </u-radio>
              </u-radio-group>
            </view>
          </view>
        </view>
        <view class="list-more">
          <view class="empty-title"></view>
          <view
            class="content"
            v-if="fixPrice === '修改金额' && isAddPrice === '加价'"
          >
            <input
              v-model="form.additionalPrice"
              class="right-input-box"
              type="digit"
            />
            <text>元</text>
          </view>
          <view
            class="content"
            v-if="fixPrice === '修改金额' && isAddPrice === '减免'"
          >
            <input
              v-model="form.discountPrice"
              class="right-input-box"
              type="digit"
            />
            <text>元</text>
          </view>
        </view>
      </view>
    </view>
    <view class="button-content">
      <view class="button button-left" @click="saveFn(1)"> 取消 </view>
      <view class="button button-right" @click="saveFn(0)"> 确认接单 </view>
    </view>
  </view>
</template>

<script>
const timeFormat = uni.$u.timeFormat;
import { acceptWorkOrder,getIgnoreLocation } from "@/api/workOrder";
export default {
  components: {},
  data() {
    return {
      isShow: false,
      // 是否保持原价
      fixPrice: "保持原价",
      // 是否加价
      isAddPrice: "加价",
      currentDate: new Date(),
      form: {
        // 当前时间加4个小时

        prospectArriveTime: "",
        // 减免金额
        discountPrice: 0.0,
        // 加价金额;
        additionalPrice: 0.0,
      },
      workOrderId: "",
      startDate: new Date().getTime(),
    };
  },
  onLoad({ id, time }) {
    console.log(id);
    this.workOrderId = id;
    console.log(time);
    // 条件编译
    // #ifdef MP-WEIXIN || H5
    if (time) {
      this.form.prospectArriveTime = timeFormat(time, "yyyy-mm-dd hh:MM:00");
    }
    // #endif
  },

  onReady() {
    // 微信小程序需要用此写法
    this.$refs.datetimePicker.setFormatter(this.formatter);
  },
  methods: {
    groupChange(e) {
      console.log(e);
    },
    radioChange(e) {
      console.log(e);
    },
    priceGroupChange(e) {
      console.log(e);
    },
    priceRadioChange(e) {
      console.log(e);
      if (e === "加价") {
        this.isAddPrice = false;
        this.form.discountPrice = 0;
      } else if (e === "减免") {
        this.isAddPrice = true;
        this.form.additionalPrice = 0;
      }
    },
    saveFn(type) {
      console.log(type);
      console.log(this.form);
      if (type === 0) {
        let params = {
          id: this.workOrderId,
          fixPrice: this.fixPrice === "保持原价" ? false : true,
          prospectArriveTime: this.form.prospectArriveTime,
          additionalPrice:
            this.fixPrice === "修改金额" && this.isAddPrice === "加价"
              ? this.form.additionalPrice
              : null,
          discountPrice:
            this.fixPrice === "修改金额" && this.isAddPrice === "减免"
              ? this.form.discountPrice
              : null,
        };
        uni.getLocation({
          type: "gcj02",
          success: (res) => {
            const location = {
              longitude: res.longitude,
              latitude: res.latitude,
              system: "GCJ_02",
            };
            acceptWorkOrder({
              ...params,
              location,
            })
              .then((res) => {
                console.log(res);
                if (res.code === 200) {
                  uni.showToast({
                    title: "操作成功",
                    icon: "none",
                    duration: 2000,
                    success: () => {
                      setTimeout(() => {
                        uni.reLaunch({
                          url: "/pages/workOrder/pendingOrder",
                        });
                      }, 2000);
                    },
                  });
                } else {
                  uni.showToast({
                    title: res.message,
                    icon: "none",
                  });
                }
              })
              .catch((err) => {
                console.log(err);
                uni.showToast({
                  title: err.message,
                  icon: "none",
                });
              });
          },
		  fail: function (err) {
			  uni.showToast({
			    title: '获取地理位置失败',
			    icon: "none",
			  });
			  const userId= uni.getStorageSync('userInfo').id
			  getIgnoreLocation().then((res) => {
				  console.log(res,'44444444444');
			    if (res.code === 200 && res.data.includes(userId)) {
					const location = {
					  longitude: '',
					  latitude: '',
					  system: "GCJ_02",
					};
					acceptWorkOrder({
					  ...params,
					  location,
					}).then((res) => {
					    if (res.code === 200) {
					      uni.showToast({
					        title: "操作成功",
					        icon: "none",
					        duration: 2000,
					        success: () => {
					          setTimeout(() => {
					            uni.reLaunch({
					              url: "/pages/workOrder/pendingOrder",
					            });
					          }, 2000);
					        },
					      });
					    } else {
					      uni.showToast({
					        title: res.message,
					        icon: "none",
					      });
					    }
					}).catch((err) => {
					    uni.showToast({
					      title: err.message,
					      icon: "none",
					    });
					});
			    }
			  });
		      // 根据具体错误信息进行处理
		    }
        });
      } else if (type === 1) {
        uni.navigateBack();
      }
    },
    // 选择时间
    handleConfirmClick(date) {
      console.log(date.value);

      let time = timeFormat(date.value, "yyyy-mm-dd hh:MM:00");
      console.log(time);
      this.form.prospectArriveTime = time;
      this.isShow = false;
    },
    handleCancelClick() {
      console.log(this.form);
      this.isShow = false;
    },
    formatter(type, value) {
      if (type === "year") {
        return `${value}年`;
      }
      if (type === "month") {
        return `${value}月`;
      }
      if (type === "day") {
        return `${value}日`;
      }
      return value;
    },
  },
};
</script>

<style lang="scss" scoped>
.pages {
  width: 100%;
  height: calc(100vh - 88rpx);
  box-sizing: border-box;
  background: #f2f2f2;

  .content-box {
    width: 100%;
    height: 100%;
    background: #fff;
    margin-top: 20rpx;
  }

  .info-list {
    padding: 0 4%;

    .list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 100rpx;
      border-bottom: 2rpx solid #f6f6f6;

      .title {
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #323333;
      }

      .content {
        display: flex;
        align-items: center;
        margin-left: 20rpx;
        flex-wrap: nowrap;

        text {
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #666666;
        }
      }
    }

    .other {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .btn {
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #ff541e;
        line-height: 39rpx;
      }

      .desc {
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #323333;
      }
    }
    .line {
      width: 100%;
      height: 2rpx;
      background: #f6f6f6;
    }
    .list-more {
      width: 100%;
      display: flex;
      align-items: center;
      .right-input {
        text-align: right;
        border-radius: 10rpx;
      }
      .right-input-box {
        text-align: right;
        border: 1px solid #ccc;
        border-radius: 10rpx;
        height: 60rpx;
      }
      .m-w-100 {
        display: inline-block;
        min-width: 200rpx;
        max-width: 400rpx;
        height: 40rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .title {
        padding: 20rpx 0;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #323333;
        position: relative;
        margin-left: 20rpx;
        min-width: 260rpx;

        &::before {
          content: "*";
          display: inline-block;
          width: 20rpx;
          height: 20rpx;
          font-size: 28rpx;
          color: #ff541e;
          position: absolute;
          top: 45%;
          transform: translateY(-50%);
          left: -20rpx;
        }
      }
      .empty-title {
        padding: 20rpx 0;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #323333;
        position: relative;
        margin-left: 20rpx;
        min-width: 260rpx;
        height: 88rpx;
      }
      .content {
        display: flex;
        align-items: center;
        margin-left: 20rpx;

        text {
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #666666;
          margin-left: 20rpx;
        }
        .radio-box {
          .u-radio-group {
            display: flex;
            align-items: center;
          }
        }
      }
      .date-cion {
        margin-left: 20rpx;
        display: inline-block;
        width: 32rpx;
        height: 27rpx;
        background: url("/static/images/home/<USER>") no-repeat;
        background-size: 100% 100%;
      }

      .more {
        // width: 100%;
        flex: 1;
        text-align: right;
      }
    }
  }

  .button-content {
    width: 100%;
    height: 75rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    bottom: 40rpx;
    left: 0;

    .button {
      width: 352rpx;
      height: 75rpx;
      font-size: 31rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .button-left {
      background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
      border-radius: 37rpx 0rpx 0rpx 37rpx;
    }

    .button-right {
      background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
      border-radius: 0rpx 37rpx 37rpx 0rpx;
    }
  }
}
</style>
