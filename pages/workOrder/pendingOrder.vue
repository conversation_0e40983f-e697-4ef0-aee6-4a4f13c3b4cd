<template>
	<view class="pages">
		<view class="tabs-box">
			<view
				class="tabs-item"
				:class="{
					active: orderType === tab.value
				}"
				v-for="tab in tabList"
				:key="tab.value"
				@click="tabChange(tab.value)"
			>
				{{ tab.name }}
			</view>
		</view>
		<view class="content-box">
			<!-- 指定我的工单 -->
			<view v-if="orderType === 0">
				<OList :infoData="orderList" @itemClick="handleItemClick"></OList>
				<rf-empty :info="'您暂时没有相关工单！'" v-if="orderList.length === 0"></rf-empty>
				<view class="loading" v-if="orderList.length > 0 && orderList.length === total">—— 到底啦 ——</view>
			</view>
			<!-- 工单池 -->
			<view v-if="orderType === 1">
				<OList :infoData="workOrderTank" @itemClick="handleItemClick"></OList>
				<rf-empty :info="'您暂时没有相关工单！'" v-if="workOrderTank.length === 0"></rf-empty>
				<view class="loading" v-if="workOrderTank.length > 0 && workOrderTank.length === total">—— 到底啦 ——</view>
			</view>
		</view>
		<rfLoading isFullScreen :active="isLoading"></rfLoading>
	</view>
</template>

<script>
import OList from './cpns/o-list.vue';
import { getWorkOrderList, getWorkOrderListByMe } from '@/api/workOrder';
export default {
	components: {
		OList
	},
	data() {
		return {
			orderType: 0,
			tabList: [
				{
					name: '指定我的工单',
					value: 0
				},
				{
					name: '工单池',
					value: 1
				}
			],
			// 分页参数
			pageNumber: 1,
			pageSize: 10,
			total: 0,
			loadNewData: true, // 是否第一次新增数据
			// 指定我的工单列表数据
			orderList: [],
			// 工单池列表数据
			workOrderTank: [],
			isLoading: false
		};
	},

	onShow() {
		this.resetParams();
		this.getPageData();
	},

	// 下拉刷新
	onPullDownRefresh() {
		this.resetParams();
		this.getPageData();
		uni.stopPullDownRefresh();
	},
	// 触底事件
	onReachBottom() {
		if (this.pageNumber * this.pageSize >= this.total) return;
		this.getPageData();
	},
	methods: {
		tabChange(index) {
			this.orderType = index;
			this.resetParams();
			this.getPageData();
		},
		handleItemClick(item) {
			let id = item.id;
			uni.navigateTo({
				url: `/pages/workOrder/orderDetails?id=${id}`
			});
		},
		getPageData() {
			if (this.orderType === 0) {
				this.getAssignedToMeData();
			} else if (this.orderType === 1) {
				this.getWorkOrderTankData();
			}
		},
		// 重置分页参数
		resetParams() {
			this.pageNumber = 1;
			this.pageSize = 10;
			this.loadNewData = true;
		},
		// 获取指派给我的工单数据
		getAssignedToMeData() {
			this.isLoading = true;
			let id = uni.getStorageSync('userInfo').id;
			console.log(id);
			let params = {
				pageNumber: this.pageNumber,
				pageSize: this.pageSize,
				engineerId: id,
				waitReceived: true
			};
			getWorkOrderListByMe(params)
				.then((res) => {
					console.log(res, '指派工单');
					// try {
					//   let result = res.data.rows;
					//   if (result.length === 0) return;
					//   this.orderList = result.map((item) => {
					//     return this.mapDataToList(item);
					//   });
					//   this.total = Number(res.data?.total) || 0;
					// } catch (error) {
					//   console.log(error);

					// }
					if (res.code === 200) {
						try {
							if (this.loadNewData) {
								let result = res.data.rows;
								if (result.length === 0) return this.$mHelper.toast('暂无数据');
								this.orderList = result.map((item) => {
									return this.mapDataToList(item);
								});
								this.total = Number(res.data?.total) || 0;
								this.pageNumber++;
								this.loadNewData = false;
							} else {
								let temp = res.data.rows.map((item) => {
									return this.mapDataToList(item);
								});
								this.orderList = [...this.orderList, ...temp];
								this.pageNumber++;
								this.total = Number(res.data?.total) || 0;
							}
						} catch (error) {
							console.log(error);
						}
					}
				})
				.finally(() => {
					this.isLoading = false;
				});
		},
		// 获取工单池数据
		getWorkOrderTankData() {
			this.isLoading = true;
			let params = {
				pageNumber: this.pageNumber,
				pageSize: this.pageSize,
				waitReceived: true
			};
			getWorkOrderList(params)
				.then((res) => {
					console.log(res, '工单池数据');
					this.workOrderTank = [];
					try {
						let result = res.data.rows;
						if (result.length === 0) return;
						this.workOrderTank = result.map((item) => {
							return this.mapDataToList(item);
						});
						this.total = Number(res.data?.total) || 0;
					} catch (error) {
						console.log(error);
					}
				})
				.finally(() => {
					this.isLoading = false;
				});
		},

		// 映射数据到订单列表
		// 这里需要根据接口返回的数据结构进行修改
		mapDataToList(obj) {
			return {
				...obj,
				desc: obj?.customer?.shopRecruitment || '',
				name: obj?.brand + '/' + obj?.machine || '',
				time: obj?.expectArriveTime || '',
				status: obj?.status.label || '',
				price: obj?.totalPay || '',
				id: obj?.id
			};
		}
	}
};
</script>

<style lang="scss" scoped>
.pages {
	width: 100%;
	min-height: 100vh;
	padding: 20rpx;
	box-sizing: border-box;
	background: #f2f2f2;

	.tabs-box {
		width: 100%;
		height: 80rpx;
		background: #fff;
		display: flex;
		justify-content: space-around;
		align-items: center;

		.tabs-item {
			width: 50%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #535353;

			&.active {
				font-size: 34rpx;
				font-weight: bold;
				color: #121212;
			}
		}
	}

	.content-box {
		width: 100%;
	}
}

.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	margin-bottom: 20upx;
}
</style>
