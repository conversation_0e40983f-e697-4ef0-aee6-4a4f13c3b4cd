<template>
  <!-- 申诉单列表 -->
  <view class="list-box">
    <view
      class="list-item"
      v-for="(item, index) in infoData"
      :key="index"
      @click="handleItemClick(item)"
    >
      <view class="list-left">
        <view class="desc">
          {{ item.customerName }}
        </view>
        <view class="name">
          {{ item.productInfo }}
        </view>
        <view class="time"> 申诉发起时间：{{ item.createdAt }} </view>
      </view>
      <view class="list-right">
        <view class="status" :style="{ color: colorMap[item.status.value] }">
          {{ item.status.label }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    infoData: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      colorMap: {
        'in_appeal': '#FFA200',
        'completed': '#008000',
        'close':  '#999999',
      }
    }
  },
  methods: {
    handleItemClick({ id }) {
      this.$emit("itemClick", id);
    },
  },
};
</script>

<style lang="scss" scoped>
.list-box {
  width: 100%;
  box-sizing: border-box;
  margin-top: 26rpx;

  .list-item {
    width: 100%;
    background-color: #fff;
    margin-bottom: 26rpx;
    box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
    padding: 20rpx;
    box-sizing: border-box;
    display: flex;
    border-radius: 20rpx;

    .list-left {
      flex: 1;
    }

    .list-right {
      margin-left: 40rpx;
      min-width: 160rpx;
      max-width: 200rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      text-align: right;
    }

    .name {
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 10rpx 0;

      /* 最多显示两行，多余用省略号展示 */
      /* display: -webkit-box;
			  -webkit-box-orient: vertical;
			  -webkit-line-clamp: 2;
			  overflow: hidden;
			  margin-bottom: 30rpx; */
    }

    .status {
      color: #999999;
    }

    .desc {
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #0c0c0c;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }

    .time {
      font-size: 28rpx;
      color: #666;
    }

    .price {
      font-size: 30rpx;
      font-weight: bold;
      color: #ff541e;
      margin: 0 10rpx;
    }
  }
}
</style>
