<template>
  <view class="list-box">
    <view
      class="list-item"
      v-for="(item, index) in infoData"
      :key="index"
      @click="handleItemClick(item)"
    >
      <view class="list-left">
        <view class="desc">
          {{ item.desc }}
        </view>
        <view class="name">
          {{ item.name }}
        </view>
        <view class="time"> 期望上门时间：{{ item.time }} </view>
      </view>
      <view class="list-right">
        <view class="status">
          {{ item.status }}
        </view>
        <view class="price"> {{ item.price }}元 </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    infoData: {
      type: Array,
      default: () => {
        return [
          {
            name: "理光/CSDEC8100",
            desc: "店招名展示全部，一行展示不完换行展示咩哈哈哈哈哈",
            people: "张三",
            time: "2022-01-01 00:00",
            status: "待接单",
            price: "100.00",
          },
          {
            name: "理光/CSDEC8100",
            desc: "店招名展示全部，一行展示不完换行展示咩哈哈哈哈哈店招名展示全部，一行展示不完换行展示咩哈哈哈哈哈店招名展示全部，一行展示不完换行展示咩哈哈哈哈哈",
            people: "张三",
            time: "2022-01-01 00:00",
            status: "待接单",
            price: "100.00",
          },
        ];
      },
    },
  },
  methods: {
    handleItemClick(item) {
      this.$emit("itemClick", item);
    },
  },
};
</script>

<style lang="scss" scoped>
.list-box {
  width: 100%;
  box-sizing: border-box;
  margin-top: 26rpx;

  .list-item {
    width: 100%;
    background-color: #fff;
    margin-bottom: 26rpx;
    box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
    padding: 20rpx;
    box-sizing: border-box;
    display: flex;
    border-radius: 20rpx;

    .list-left {
      flex: 1;
    }

    .list-right {
      margin-left: 40rpx;
      min-width: 160rpx;
      max-width: 200rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      text-align: right;
    }

    .name {
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 10rpx 0;

      /* 最多显示两行，多余用省略号展示 */
      /* display: -webkit-box;
			  -webkit-box-orient: vertical;
			  -webkit-line-clamp: 2;
			  overflow: hidden;
			  margin-bottom: 30rpx; */
    }

    .status {
      color: #ff541e;
    }

    .desc {
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #0c0c0c;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }

    .time {
      font-size: 28rpx;
      color: #666;
    }

    .price {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
    }
  }
}
</style>
