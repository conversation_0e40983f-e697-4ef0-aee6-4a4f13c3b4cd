<template>
	<view class="main">
		<view class="des">
			<view class="des-class">
				<image
					class="des-image"
					:src="customerDeviceGroup.deviceGroupImg && customerDeviceGroup.deviceGroupImg.url ? customerDeviceGroup.deviceGroupImg.url : require('../../static/images/top.png')"
					mode=""
				></image>
				<view class="des-class-details">
					<view class="model-number">
						{{ customerDeviceGroup.deviceGroup.label }}
					</view>
					<view class="model-brand">
						{{ customerDeviceGroup.productInfo }}
					</view>
				</view>
			</view>
			<view class="dex-title">故障描述:</view>
			<view class="des-text des-boder">
				<text style="word-break: break-all">{{ repairReport.excDesc }}</text>
				<view class="des-image-list">
					<image v-for="(item, index) in repairReport.excDescPics" @click="previewImage(repairReport.excDescPics, index)" :key="item.key" :src="item.url" mode="heightFix"></image>
				</view>
			</view>
			<view class="dex-title">解决措施：</view>
			<view class="des-text">
				<text style="word-break: break-all">{{ repairReport.resolveDesc }}</text>
				<view class="des-image-list">
					<image
						v-for="(item, index) in repairReport.resolveDescPics"
						@click="previewImage(repairReport.resolveDescPics, index)"
						:key="item.key"
						:src="item.url"
						mode="heightFix"
					></image>
				</view>
			</view>
		</view>
		<view class="price">
			<view class="des-number des-boder">
				现象分类：
				<view class="count">{{ repairReport.excType.label }}</view>
			</view>
			<view class="des-number des-boder">
				原因分类：
				<view class="count">{{ repairReport.reasonType.label }}</view>
			</view>
			<view class="des-number des-boder">
				处理类型：
				<view class="count">{{ repairReport.resolveType.label }}</view>
			</view>
			<view class="des-number des-boder">
				故障组件：
				<view class="count">{{ repairReport.excUnit.label }}</view>
			</view>
			<view class="des-number des-boder">
				黑白计数器：
				<view class="count">{{ repairReport.blackWhiteCount }}</view>
			</view>
			<view class="des-number des-boder" v-if="customerDeviceGroup.treatyType.value === '1230' || customerDeviceGroup.treatyType.value === '1202'">
				黑白废张数：
				<view class="count">{{ repairReport.blackWhiteExclude }}</view>
			</view>
			<view class="des-number des-boder">
				彩色计数器：
				<view class="count">{{ repairReport.colorCount }}</view>
			</view>
			<view class="des-number des-boder" v-if="customerDeviceGroup.treatyType.value === '1230' || customerDeviceGroup.treatyType.value === '1202'">
				彩色废张数：
				<view class="count">{{ repairReport.colorExclude }}</view>
			</view>
			<view class="des-number des-boder">
				上次维修后到目前的印量：
				<view class="count">{{ repairReport.printCount }}</view>
			</view>
		</view>
		<view class="des" v-if="replaceOrder && replaceOrder.replaceDetailList && replaceOrder.replaceDetailList.length > 0">
			<view class="dex-title des-margin">更换耗材零件：</view>
			<view class="des-class" v-for="item in replaceOrder.replaceDetailList" :key="item.id">
				<image class="des-image" :src="item.skuInfo.picUrl[0].url ? item.skuInfo.picUrl[0].url : require('../../static/images/top.png')" mode=""></image>
				<view class="des-class-details">
					<view class="model-number">
						{{ item.itemName }}
						<view class="model">
							{{ item.itemStore.skuSource === 'ENGINEER_APPLY' ? '工程师外带' : '客户自配' }}
						</view>
					</view>
					<view class="model-brand" style="display: flex; justify-content: flex-start">
						<text v-for="(attr, index) in item.skuInfo.saleAttrVals" :key="attr.id">{{ attr.val }}{{ index === item.skuInfo.saleAttrVals.length - 1 ? '' : ',' }}</text>
					</view>
					<view class="model-brand">
						OEM编号：{{ item.itemStore.oemNumber }}
						<view class="number">×{{ item.num }}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- <view class="price">
      <view class="des-number des-boder">
        上门费：<view class="count">{{ workOrder.visitPay }}元</view>
      </view>
      <view class="des-number des-boder" v-if="workOrder.repairPay">
        会员减免：<view class="count">{{ workOrder.repairPay }}元</view>
      </view>
      <view class="des-number des-boder" v-if="workOrder.actualReplacePay">
        零件更换费：<view class="count"
          >{{ workOrder.actualReplacePay }}元</view
        >
      </view>
      <view class="des-number des-boder" v-if="workOrder.itemPay">
        维修耗材费用：<view class="count">{{ workOrder.itemPay }}元</view>
      </view>
      <view class="des-number des-boder" v-if="workOrder.engineerAdditionalPay">
        工程师追加费用：<view class="count"
          >{{ workOrder.engineerAdditionalPay }}元</view
        >
      </view>
      <view class="des-number des-boder" v-if="workOrder.derateAmount">
        工程师减免费用：<view class="count"
          >-{{ workOrder.derateAmount }}元</view
        >
      </view>
      <view class="des-number des-boder">
        维修折扣费：<view class="count">{{
          workOrder.discountAmount ? `-${workOrder.discountAmount}元` : "-"
        }}</view>
      </view>
      <view class="des-number des-boder">
        客户追加报酬：<view class="count">{{ workOrder.additionalPay }}元</view>
      </view>
      <view class="des-number des-boder" style="margin-bottom: 40rpx">
        应付维修费用：<view class="count"
          >合计
          <text style="color: #ff541e; font-weight: 600"
            >¥{{ workOrder.totalPay }}</text
          >
          元</view
        >
      </view>
    </view> -->
		<rfLoading isFullScreen :active="isLoading"></rfLoading>
	</view>
</template>

<script>
import { reportDetailsApi } from '@/api/workOrder';
export default {
	data() {
		return {
			isLoading: false,
			detailId: null,
			replaceOrder: {},
			customerDeviceGroup: {},
			repairReport: {},
			workOrder: {}
		};
	},
	onLoad({ id }) {
		this.detailId = id;
		this.loadData();
	},
	methods: {
		async loadData() {
			try {
				this.isLoading = true;
				const result = await reportDetailsApi(this.detailId);
				if (result.code === 200) {
					const { customerDeviceGroup, repairReport, replaceOrder, workOrder } = result.data;
					this.customerDeviceGroup = customerDeviceGroup;
					this.repairReport = repairReport;
					this.replaceOrder = replaceOrder;
					this.workOrder = workOrder;
				}
			} catch (err) {
				console.error(err);
			} finally {
				this.isLoading = false;
			}
		},
		previewImage(url, index) {
			uni.previewImage({
				current: index,
				urls: url.map((item) => item.url)
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	width: 100%;
	height: 100%;
	background-color: #f5f6f8;
	padding-bottom: 40rpx;
	.head-nav {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 10000;
	}

	.des {
		width: 100%;
		margin-top: 22rpx;
		background-color: #fff;
		padding: 24rpx;
		box-sizing: border-box;

		.des-class {
			width: 100%;
			padding-bottom: 24rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.des-image {
				width: 154rpx;
				height: 154rpx;
				border-radius: 13rpx;
				margin-right: 16rpx;
			}

			.des-class-details {
				flex: 1;
				height: 154rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				font-size: 27rpx;
				font-family: PingFang SC;

				.model-number {
					width: 100%;
					font-weight: bold;
					color: #0c0c0c;
					line-height: 40rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.model {
						font-weight: normal;
						color: #535353;
					}
				}

				.model-brand {
					width: 100%;
					margin-top: 10rpx;
					font-weight: 500;
					color: #535353;
					line-height: 40rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
				}
			}
		}

		.dex-title {
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #323333;
			margin-top: 16rpx;

			&.des-margin {
				margin-bottom: 14rpx;
			}
		}

		.des-text {
			min-height: 127rpx;
			padding-bottom: 20rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #666666;
		}
	}

	.price {
		width: 100%;
		margin-top: 22rpx;
		background-color: #fff;
		padding: 0 24rpx;
		box-sizing: border-box;
	}

	.des-number {
		width: 100%;
		min-height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #323333;

		.count {
			color: #666666;
		}
	}

	.des-boder {
		border-bottom: 1rpx solid #f3f3f3;
	}

	.sbumit {
		width: 100%;
		height: 210rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.sbumit-button {
			width: 705rpx;
			height: 80rpx;
			background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
			border-radius: 40rpx;
			font-size: 31rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}
.des-image-list {
	width: 100%;
	height: 206rpx;
	display: flex;
	align-items: center;
	//justify-content: space-between;
	overflow-x: auto;

	image {
		width: 154rpx;
		height: 154rpx;
		//background: #474747;
		border-radius: 13rpx;
	}
	image + image {
		margin-left: 10rpx;
	}
}
</style>
