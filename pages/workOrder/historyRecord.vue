<template>
  <view class="user">
    <!-- 内容区-->
    <view class="cover-container">
      <view class="top-box">
        <view class="tui-card">
          <view
            class="list-list"
            v-for="(item, index) in details"
            :key="index"
            @click="toPage(item)"
          >
            <view class="tui-card-header">{{ item.repairReport.excDesc }}</view>
            <view class="cell">更换零件：</view>
            <view class="cell" v-for="part in item.replaceOrder.replaceDetailList" :key="part.id">
              <text>{{ part.itemName }}</text>
              <text>{{ part.skuInfo.saleAttrVals.map(a => a.val).join('，') }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 无商品展示 -->
    <rf-empty
      class="no-listData"
      :info="'暂无历史报修'"
      v-if="details.length === 0 && !loading"
    ></rf-empty>
    <!-- 页面加载-->
    <rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
  </view>
</template>
<script>
import { historyWorkOrderList } from "@/api/workOrder";
export default {
  components: {},
  data() {
    return {
      id: "",
      details: [],
      loading: false,
    };
  },

  onLoad({ id }) {
    this.id = id;
  },
  async onShow() {
    await this.initData();
  },
  created() {},
  //下拉
  onPullDownRefresh() {
    this.initData();
    uni.stopPullDownRefresh();
  },
  methods: {
    // 数据初始化
    async initData() {
      historyWorkOrderList(this.id).then((res) => {
        this.details = [];
        if (res.code === 200) {
          this.details = res.data;
        }
        this.loading = false;
      });
    },
    // 统一跳转接口,拦截未登录路由
    toPage(item) {
      console.log(item)
      uni.navigateTo({
        url: "/pages/workOrder/reportDetails?id=" + item.replaceOrder.workOrderId,
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.hticon {
  font-size: 24px;
}

page {
  background: #f5f6f8;
}

.cover-container {
  margin-top: 40upx;
  padding: 0 30upx;
  position: relative;

  .top-box {
    width: 100%;
    margin: auto;

    overflow: hidden;
    margin-bottom: 20upx;
  }

  .tui-card {
    .tui-card-header {
      width: 100%;
      font-size: 30rpx;
      line-height: 80rpx;
      text-align: left;
      font-weight: bold;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      color: #333;
    }

    .tui-card-cell,
    .tui-card-cell-left {
      width: 100%;
      font-size: 34rpx;
      line-height: 80rpx;
      text-align: left;
      padding: 0 20rpx;
      font-weight: bold;
    }

    .tui-card-cell-left {
      text-align: left;
      line-height: 1;
    }
  }

  .list-header {
    width: 100%;
    margin: auto;
    display: flex;
    background: #fff6f3;

    .cell {
      line-height: 80rpx;
      flex: 1;
      text-align: center;
      color: #333;
    }
  }

  .list-list {
    width: 100%;
    padding: 20rpx;
    line-height: 80rpx;
    background: #fff;
    margin-bottom: 20rpx;
    box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    border-radius: 20rpx;

    .cell {
      line-height: 60rpx;
      text-align: left;
      color: #666;
      display: flex;
      align-items: center;
      text:first-child {
        display: inline-block;
        width: 180rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 15px;
      }
    }
  }
}

.img {
  width: 100%;
  display: flex;
  justify-content: center;
  text-align: center;

  .iconfont {
    font-size: $font-lg + 24upx;
  }
}

.share-btn {
  height: 142upx;
  text-align: left;
  background: none;
  padding: 5px 0 0 0;
  margin: 0;

  .text {
    color: #303133;
    font-size: 24upx;
  }
}

.share-btn:after {
  border: none;
  border-radius: none;
}

.newpub {
  background: #ff7200;
  width: 38rpx;
  height: 38rpx;
  border-radius: 50%;
  text-align: center;
  line-height: 38rpx;
  font-size: 24rpx;
  color: #fff;
  position: absolute;
  right: 30%;
}

.back {
  position: absolute;
  left: 0;
  top: 40rpx;
  width: 100rpx;
  height: 100rpx;

  // 返回
  .back-one {
    display: block;
    align-items: center;
    justify-content: center;
    width: 50rpx;
    height: 50rpx;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 100%;
    position: relative;
    top: 20rpx;
    left: 20rpx;

    text {
      display: flex;
      width: 20rpx;
      height: 20rpx;
      border-left: 2rpx solid #ffffff;
      border-bottom: 2rpx solid #ffffff;
      transform: rotate(45deg);
      position: absolute;
      left: 20rpx;
      top: 15rpx;
    }
  }

  .action {
    background-color: transparent;

    text {
      border-color: #555555;
    }
  }
}
</style>
