<template>
	<view class="pages">
		<!-- 	<view class="box1">
			<FormQuery :form-columns="formColumns" :is-machine="true" @search="search" />
		</view> -->
		<view class="content-box">
			<appeal-list :info-data="infoData" @itemClick="handleItemClick"></appeal-list>
			<rf-empty :info="'您暂时没有相关工单！'" v-if="infoData.length === 0"></rf-empty>
		</view>
		<view class="loading" v-if="infoData.length > 0 && infoData.length === total">—— 到底啦 ——</view>
		<rfLoading isFullScreen :active="isLoading"></rfLoading>
	</view>
</template>

<script>
import AppealList from './cpns/appeal-list.vue';
import { appealListApi } from '@/api/appeal';
import FormQuery from '@/components/formQuery/index';
export default {
	components: {
		AppealList,
		FormQuery
	},
	data() {
		return {
			infoData: [],
			pageNumber: 1,
			pageSize: 10,
			total: 0,
			isLoading: false,
			searchparms: {},
			formColumns: [
				{
					dataIndex: 'customerName',
					title: '客户名称',
					valueType: 'input'
				},
				{
					dataIndex: 'customerPhone',
					title: '保修手机号',
					valueType: 'input'
				},
				{
					dataIndex: 'product',
					title: '品牌机型',
					valueType: 'machine'
				},
				{
					dataIndex: 'date',
					title: '日期',
					valueType: 'datetime',
					inputType: 'daterange'
				}
			]
		};
	},
	onLoad() {
		uni.$on('refreshAppealList', () => {
			this.refresh();
		});
		this.refresh();
	},
	// 滚动到底部
	onReachBottom() {
		console.log('滚动到底部');
		if ((this.pageNumber - 1) * this.pageSize >= this.total) {
			return;
		}
		this.loadData();
	},
	onPullDownRefresh() {
		if (this.isLoading) return;
		uni.startPullDownRefresh({
			success: async () => {
				await this.refresh();
			}
		});
		uni.stopPullDownRefresh();
	},
	methods: {
		// 查询
		search(parameter) {
			console.log(parameter);
		},
		handleItemClick(id) {
			// this.$emit("itemClick", id);
			uni.navigateTo({ url: '/pages/workOrder/appealOrderDetails?id=' + id });
		},
		async loadData() {
			try {
				if (this.isLoading) return;
				this.isLoading = true;
				const args = {
					pageNumber: this.pageNumber,
					pageSize: this.pageSize
				};
				const result = await appealListApi(args);
				if (result.code === 200) {
					this.infoData.push(...result.data.rows);
					this.total = +result.data.total;
					this.pageNumber++;
				}
			} catch (err) {
				console.error(err);
			} finally {
				this.isLoading = false;
			}
		},
		async refresh() {
			this.pageNumber = 1;
			this.infoData = [];
			await this.loadData();
		}
	}
};
</script>

<style lang="scss" scoped>
.pages {
	width: 100%;
	min-height: 100vh;
	padding: 20rpx;
	box-sizing: border-box;
	background: #f2f2f2;

	.content-box {
		width: 100%;
		// margin-top: 510rpx;
	}
}

.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	margin-bottom: 20upx;
}
</style>
