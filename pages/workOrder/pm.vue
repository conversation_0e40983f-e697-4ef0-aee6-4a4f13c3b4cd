<template>
  <view class="user">
    <view class="cover-container">
      <view class="top-box">
        <view class="tui-card">
          <view class="tui-card-cell"
            >机器名称：<text style="font-weight: normal">{{
              productInfo
            }}</text>
          </view>
        </view>
        <view class="tui-card">
          <view class="tui-card-cell"
            >品牌机型：<text style="font-weight: normal">{{
              deviceGroup
            }}</text></view
          >
        </view>
      </view>

      <view class="top-box">
        <view class="tui-card">
          <view class="list-list" v-for="(item, index) in listPM" :key="index">
            <view class="cell"
              >零件名称：{{
                item.productPartName ? item.productPartName : ""
              }}</view
            >
            <view class="cell"
              >所属单元：{{ item.unit.label ? item.unit.label : "" }}</view
            >
            <view class="cell"
              >位置：{{ item.position ? item.position : "" }}</view
            >
            <view class="cell"
              >上次计数器：{{ item.lastCount ? item.lastCount : "" }}</view
            >
            <view class="cell"
              >当前计数器：{{
                item.currentCount ? item.currentCount : ""
              }}</view
            >
            <view class="cell"
              >已使用寿命：{{ item.useLife ? item.useLife : "" }}</view
            >
          </view>
        </view>
      </view>
    </view>
    <rf-empty
      :info="'此机器暂无更换记录'"
      v-if="listPM.length === 0"
    ></rf-empty>
    <!-- 页面加载-->
    <rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
  </view>
</template>
<script>
/**
 * @des 个人中心
 *
 * @<NAME_EMAIL>
 * @date 2020-01-10 11:41
 * @copyright 2019
 */
import { hisPmReplace } from "@/api/workOrder";
export default {
  components: {},
  data() {
    return {
      productInfo: "",
      deviceGroup: "",
      id: "",
      listPM: [],
      loading: true,
    };
  },
  onLoad(option) {
    this.productInfo = option.productInfo;
    this.deviceGroup = option.deviceGroup;
    this.id = option.id;
    this.loading = true;
  },
  onShow() {
    this.hisPmReplaceFn();
  },
  methods: {
    async hisPmReplaceFn() {
      try {
        const result = await hisPmReplace(this.id);
        if (result.code === 200) {
          this.listPM = result.data.pmReplaceList;
          this.loading = false;
        }
      } catch (err) {
        this.loading = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.hticon {
  font-size: 24px;
}

page {
  background: #f5f6f8;
}

.user {
  padding-top: 30upx;

  .cover-container {
    padding: 0 24upx 20upx;
    position: relative;

    .top-box {
      width: 100%;
      margin: auto;
      padding: 0 20rpx;
      box-sizing: border-box;
      border-radius: 10upx;
      overflow: hidden;
      margin-bottom: 20upx;
      background: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .tui-card {
      .tui-card-header,
      .tui-card-header-left {
        width: 100%;
        font-size: 60rpx;
        line-height: 80rpx;
        text-align: center;
      }

      .tui-card-header-left {
        text-align: left;
        font-size: 40rpx;
        line-height: 80rpx;
      }

      .tui-card-cell {
        width: 100%;
        font-size: 30rpx;
        line-height: 90rpx;
        text-align: left;
        font-weight: bold;
      }
    }

    .list-list {
      width: 100%;
      line-height: 80rpx;
      background: #fff;
      padding: 26rpx 12rpx;
      box-sizing: border-box;
      border-bottom: 1rpx solid #f5f6f8;

      .cell {
        line-height: 50rpx;
        text-align: left;
        color: #666;
      }
    }

    .list-list:last-child {
      border: none;
    }
  }
}
</style>
