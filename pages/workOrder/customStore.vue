<script>
export default {
  name: "customStore",
  data() {
    return {
      goodsList: [],
      isEnloading: false,
      total: 0,
    }
  },
  onLoad({ customerId, deviceGroupId }) {
    this.loadData(customerId, deviceGroupId)
  },
  methods: {
    async loadData(customerId, deviceGroupId) {
      try {
        this.isEnloading = true
        const params = {
          customerId,
          deviceGroupId
        }
        const result = await this.$http.post('/api/engineerItemStore/listCustomerItemStore', params)
        if(result.code === 200) {
          this.goodsList = result.data
          this.total = this.goodsList.length
        }
      } catch (err) {
        console.log(err)
      } finally {
        this.isEnloading = false
      }
    }
  }
}
</script>

<template>
  <view>
    <view class="cart-list" v-if="goodsList.length > 0">
      <view class="list" v-for="good in goodsList" :key="good.id">
        <view class="goods">
          <view class="thumb">
            <image :src="good.skuInfo.picUrl[0].url" mode=""></image>
          </view>
          <view class="item">
            <view class="title">
              <text class="two-omit">{{ good.itemName }}</text>
            </view>
            <view class="attribute">
              <view class="attr">
                <text>{{
                    good.skuInfo.saleAttrVals.map(
                        (item) => item.name + ":" + item.val
                    )
                  }}</text>
                <text>{{ good.saleAttrValsStr }}</text>
              </view>
            </view>
            <view class="price-num">
              <view class="price">
                <text class="min">￥</text>
                <text class="max">{{ good.saleUnitPrice }}</text>
              </view>
              <view class="num" @tap.stop>
                <view class="number">
                  <text>{{ good.num }}</text>
                  <!-- <input v-model="good.SkuNum" type="number" maxlength="8" /> -->
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="goodsList.length === 0" style="padding-top: 200rpx">
      <rf-empty :info="'暂未查询到耗材商品哦'"></rf-empty>
    </view>
    <view class="loading" v-if="!isEnloading && goodsList.length">{{
        isEnloading ? "加载中..." : goodsList.length < total ? "上拉加载更多" : ""
      }}</view>
    <view
        class="loading"
        v-if="goodsList.length > 0 && goodsList.length === total && isEnloading"
    >—— 到底啦 ——</view
    >
  </view>
</template>

<style lang="scss" scoped>
.box1 {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  right: 0;
  background: #fff;
  z-index: 9;
  padding-bottom: 20rpx;
  border-bottom: 5px solid #f5f5f5;
}
.box2 {
  margin-top: 280px;
}
.main {
  width: 100vw;
  height: 100vh;
  padding: 0 22rpx;
  box-sizing: border-box;
  background-color: #fff;

  .input-totast {
    width: 100%;
    color: #e5452f;
    display: flex;

    .totast-content {
      flex: 1;
      font-size: 28rpx !important;
    }
  }

  .pd_body {
    width: 100%;
    // position: relative;

    .pd_input {
      font-size: 14px !important;
      border: 1px solid #e5e5e5 !important;
      box-sizing: border-box !important;
      border-radius: 4px !important;
      height: 80rpx !important;
      padding: 0 22rpx !important;
    }

    .socrll-check {
      width: 100%;
      position: absolute;
      top: 100rpx;
      left: 0;
      z-index: 3;
      padding: 0 22rpx;

      /deep/.uni-select__selector-scroll {
        width: 100%;
        height: 210px;
        padding: 4px 22rpx !important;
        box-sizing: border-box;
        background-color: #ffffff;
        border: 1px solid #ebeef5;
        border-radius: 6px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        .socrll-list {
          height: 210px;
          overflow: hidden;
          overflow-y: scroll;
        }

        .socrll-item {
          display: flex;
          cursor: pointer;
          line-height: 35px;
          font-size: 14px;
          text-align: center;
        }
      }

      .no-socrll-list {
        height: 45px;
        padding: 4px 22rpx !important;
        box-sizing: border-box;
        background-color: #ffffff;
        border: 1px solid #ebeef5;
        border-radius: 6px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        overflow: hidden;
        overflow-y: scroll;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        color: #999;
      }

      .socrll-popper__arrow {
        filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
        position: absolute;
        top: -4px;
        left: 15%;
        margin-right: 3px;
        border-top-width: 0;
        border: 1px solid #ebeef5;
        border-bottom-color: #ffffff;
        border-right-color: #ffffff;
        width: 18rpx;
        height: 18rpx;
        background-color: #ffffff;
        transform: rotate(45deg);
      }
    }
  }

  .service {
    width: 50rpx;
    min-height: 80rpx;
    position: fixed;
    right: 0;
    bottom: 200rpx;
    text-align: center;
    border-radius: 8rpx 0 0 8rpx;
    float: right;
    border: 2rpx solid #e5452f;
    border-right: 0;
    color: #e5452f;
  }

  .footer {
    width: 50%;
    display: flex;
    align-items: center;
    margin: auto;
    .confirm-btn {
      width: 50%;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none !important;
      color: #fff;
      border-radius: 37px;
      font-size: 28rpx;
      margin: 0;
    }

    .save {
      background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
    }

    .cancel {
      background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
    }
  }
}

/deep/.uni-select {
  height: 80rpx !important;
  padding: 0 22rpx !important;
}

/deep/.line {
  display: none;
}

/deep/.uni-data-pickerview {
  .item {
    justify-content: center;
  }
}

/deep/.uni-section-content {
  height: 80rpx !important;
  padding: 0 22rpx !important;
  margin-bottom: 22rpx;
}

/deep/.uni-section__content-title {
  font-size: 26rpx !important;
}

/deep/.uni-input {
  height: 80rpx !important;
  line-height: 40rpx !important;
  border: 1rpx solid #e5e5e5;
  padding: 15rpx;
  border-radius: 10rpx;
}

/deep/.uni-select__input-placeholder {
  font-size: 14px !important;
  color: #999 !important;
}

/deep/.input-value {
  padding: 0 14rpx 0 22rpx !important;
  height: 80rpx;
}

/deep/.selected-area {
  font-size: 14px !important;
  color: #999 !important;
}

/deep/.selected-item-active {
  border-bottom: 2px solid #e5452f;
}

/deep/.selected-item {
  margin: 0 6rpx !important;
  padding: 8rpx 0 !important;
}

/deep/.check {
  border-color: #e5452f !important;
  margin-left: 6px;
  margin-right: 0;
}

.pd_body {
  /deep/.uni-section-content {
    position: relative;

    .pd_icon {
      position: absolute;
      right: 44rpx;
      top: 20rpx;
    }

    .close_icon {
      position: absolute;
      right: 88rpx;
      top: 16rpx;
      z-index: 10000;
    }
  }
}

.icons {
  font-family: iconfont;
  font-size: 32upx;
  font-style: normal;
  color: #999;
}
/deep/.uni-section-header {
  width: 150rpx !important;
  float: left;
  clear: both;
}
</style>
<style scoped lang="scss">
.page {
  // padding-bottom: 200rpx;
  background: #f5f6f8;
  // min-height: 100vh;
  min-height: 100vh;
}

.head {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 80rpx;

  background-color: #ffffff;

  .title {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 30rpx;
    color: #212121;
  }

  .edit {
    position: absolute;
    right: 20rpx;
    top: 50%;
    transform: translate(0, -50%);
    background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
    padding: 8rpx 20rpx;
    border-radius: 60px;

    text {
      color: #fff;
      font-size: 26rpx;
    }
  }
}

/* 购物车列表 */
.cart-list {
  width: 100%;
  padding: 20rpx 0;
  margin-top: 10rpx;

  .list {
    display: flex;
    height: 185rpx;
    background-color: #ffffff;
    box-shadow: 0 0 20rpx #f6f6f6;
    border-radius: 10rpx;

    .check {
      display: flex;
      align-items: center;
      width: 10%;
      height: 80%;
      margin-left: 20rpx;

      // background-color: #fff;

      text {
        font-size: 36rpx;
        color: #333333;
      }

      .icon-checked {
        color: #fe3b0f;
        // box-shadow: 0 0 10rpx  #fe3b0f;
      }
    }

    .goods {
      display: flex;
      align-items: center;
      width: 90%;
      height: 100%;

      .thumb {
        display: flex;
        // align-items: center;
        justify-content: center;
        width: 30%;
        height: 100%;
        margin-top: 20rpx;

        image {
          width: 160rpx;
          height: 160rpx;
          border-radius: 10rpx;
        }
      }

      .item {
        padding: 10rpx 0;
        width: 70%;
        height: 100%;

        .title {
          display: flex;
          align-items: center;
          width: 100%;
          height: auto;

          text {
            font-size: 26rpx;
            color: #212121;
          }
        }

        .attribute {
          display: flex;
          align-items: center;
          margin-top: 10rpx;

          .attr {
            display: flex;
            align-items: center;
            padding: 0 20rpx;
            height: 40rpx;
            background-color: #f6f6f6;
            border-radius: 10rpx;

            text {
              font-size: 24rpx;
              color: #333333;
            }

            .more {
              display: flex;
              width: 10rpx;
              height: 10rpx;
              border-left: 2rpx solid #333333;
              border-bottom: 2rpx solid #333333;
              transform: rotate(-45deg);
              margin-left: 10rpx;
            }
          }
        }

        .price-num {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 80rpx;

          .price {
            display: flex;

            .min {
              color: #fe3b0f;
              font-size: 24rpx;
            }

            .max {
              font-size: 28rpx;
              color: #fe3b0f;
              font-weight: bold;
            }
          }

          .num {
            display: flex;
            height: 40rpx;
            margin-right: 20rpx;

            .add {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 60rpx;
              height: 40rpx;
              background-color: #ffffff;

              text {
                color: #212121;
                font-size: 24rpx;
              }
            }

            .number {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 80rpx;
              height: 40rpx;
              background-color: #f6f6f6;
              border-radius: 8rpx;
              text-align: center;

              text {
                font-size: 24rpx;
                color: #212121;
              }
            }
          }
        }
      }
    }
  }
}

.background {
  background: #f5f6f8;
}

/* 购物车失效商品列表 */
.lose-efficacy-list {
  width: 100%;
  background-color: #ffffff;
  padding: 0 30rpx;
  margin-top: 30rpx;
  border-radius: 10rpx;
  overflow: hidden;

  .lose-efficacy-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 80rpx;

    .title {
      display: flex;
      align-items: center;
      height: 100%;

      text {
        font-size: 28rpx;
        color: #222222;
      }
    }

    .empty {
      display: flex;
      align-items: center;
      height: 100%;

      text {
        font-size: 26rpx;
        color: #fe3b0f;
      }
    }
  }

  .list {
    display: flex;
    align-items: center;
    width: 100%;
    height: 185rpx;
    border-bottom: 1px solid #f6f6f6;

    .tag {
      display: flex;
      align-items: center;
      width: 10%;
      height: 100%;

      text {
        padding: 4rpx 10rpx;
        font-size: 24rpx;
        color: #ffffff;
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 20rpx;
      }
    }

    .goods {
      display: flex;
      align-items: center;
      width: 90%;
      height: 100%;
      background-color: #ffffff;
      border-radius: 10rpx;

      .pictrue {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30%;
        height: 100%;

        image {
          width: 160rpx;
          height: 160rpx;
          border-radius: 10rpx;
        }
      }

      .item {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 70%;
        height: 160rpx;

        .title {
          width: 100%;

          text {
            font-size: 28rpx;
            color: #999999;
          }
        }

        .explain {
          display: flex;
          align-items: center;

          text {
            font-size: 24rpx;
            color: #222222;
          }
        }
      }
    }
  }
}

/* 为你推荐 */
.recommend-info {
  width: 100%;
  background-color: #f2f2f2;
  cursor: pointer;

  .recommend-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100rpx;

    .title {
      display: flex;
      align-items: center;

      image {
        width: 416rpx;
        height: 40rpx;
      }
    }
  }

  .goods-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 30rpx;

    .list {
      width: 49%;
      height: 540rpx;
      margin-bottom: 20rpx;
      background-color: #ffffff;
      border-radius: 10rpx;
      overflow: hidden;

      .pictrue {
        display: flex;
        justify-content: center;
        width: 100%;

        image {
          height: 350rpx;
        }
      }

      .title-tag {
        // display: flex;
        height: 100rpx;
        padding: 20rpx;

        .tag {
          float: left;
          margin-right: 10rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          white-space: normal;
          font-size: 26rpx;
          line-height: 40rpx;

          text {
            font-size: 24rpx;
            color: #ffffff;
            padding: 4rpx 16rpx;
            background: linear-gradient(to right, #fe3b0f, #fc603a);
            border-radius: 6rpx;
            margin-right: 10rpx;
          }
        }
      }

      .price-info {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        padding: 0 20rpx;
        height: 80rpx;

        .user-price {
          display: flex;
          align-items: center;

          text {
            color: #ff0000;
          }

          .min {
            font-size: 24rpx;
          }

          .max {
            font-size: 32rpx;
          }
        }

        .vip-price {
          display: flex;
          align-items: center;

          image {
            width: 26rpx;
            height: 26rpx;
            margin-right: 10rpx;
          }

          text {
            color: #fcb735;
            font-size: 24rpx;
          }
        }
      }
    }
  }
}

/* 结算 */
.close-account {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100rpx;
  background-color: #ffffff;
  border-top: 2rpx solid #f6f6f6;

  .check-total {
    display: flex;
    align-items: center;
    width: 50%;
    height: 100%;

    .check {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40%;
      height: 100%;

      text {
        font-size: 36rpx;
        color: #333333;
      }

      .icon-checked {
        color: #fe3b0f;
        // box-shadow: 0 0 10rpx  #fe3b0f;
      }

      .all {
        font-size: 24rpx;
        margin-left: 10rpx;
      }
    }

    .total {
      display: flex;
      align-items: center;
      width: 60%;
      height: 100%;

      text {
        font-size: 24rpx;
        color: #333333;
      }

      .price {
        font-weight: bold;
        color: #fe3b0f;
      }
    }
  }

  .account {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 4%;
    margin-top: 20rpx;
    .btn-calculate {
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 1;
      margin: 0 30rpx;
      height: 60rpx;
      background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
      border-radius: 60rpx;

      text {
        color: #ffffff;
        font-size: 24rpx;
      }
    }

    .btn-del {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .attention {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 140rpx;
        height: 60rpx;
        border: 2rpx solid #eeeeee;
        border-radius: 60rpx;
        color: #333333;
        font-size: 24rpx;
        margin-right: 20rpx;
      }

      .del {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100rpx;
        height: 60rpx;
        background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
        border-radius: 60rpx;
        color: #ffffff;
        font-size: 24rpx;
      }
    }
  }
}

.icon-subtract {
  position: relative;
  margin: 0 20rpx;

  &::after {
    content: "";
    display: block;
    width: 25rpx;
    height: 5rpx;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba($color: #000, $alpha: 0.8);
  }
}

.icon-add {
  position: relative;
  margin: 0 20rpx;

  &::after {
    content: "";
    display: block;
    width: 25rpx;
    height: 5rpx;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba($color: #000, $alpha: 0.8);
  }

  &::before {
    content: "";
    display: block;
    width: 5rpx;
    height: 25rpx;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba($color: #000, $alpha: 0.8);
  }
}

.check-icon {
  width: 35rpx;
  height: 35rpx;
  border: 1px solid #999999;
  border-radius: 50%;

  &.active {
    border: 1px solid #e5452f;
    color: #e5452f !important;
    line-height: 30rpx;
  }
}
/deep/.uni-stat__select {
  width: 80%;
}

.loading {
  height: 80upx;
  line-height: 80upx;
  text-align: center;
  color: #ccc;
  font-size: 24upx;
  width: 100%;
  padding-bottom: 180rpx;
}
</style>