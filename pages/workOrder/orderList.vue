<template>
  <view class="page">
    <view class="top-box">
      <view class="search-box">
        <mSearch
          class="mSearch-input-box"
          :mode="2"
          button="inside"
          :placeholder="'搜索我的工单'"
          @search="onSearch(false)"
          @confirm="onSearch(false)"
          v-model="keyword"
        ></mSearch>
      </view>
      <!-- 订单tab -->
      <view class="order-tab">
        <view
          class="tab"
          :class="{ action: OrderType == 0 }"
          @click="onOrderTab(0)"
        >
          <text>全部</text>
          <text class="line"></text>
        </view>
        <view
          class="tab"
          :class="{ action: OrderType == 1 }"
          @click="onOrderTab(1)"
        >
          <text>待接单</text>
          <text class="line"></text>
        </view>
        <view
          class="tab"
          :class="{ action: OrderType == 2 }"
          @click="onOrderTab(2)"
        >
          <text>待结算</text>
          <text class="line"></text>
        </view>
        <view
          class="tab"
          :class="{ action: OrderType == 3 }"
          @click="onOrderTab(3)"
        >
          <text>已完成</text>
          <text class="line"></text>
        </view>
        <view
          class="tab"
          :class="{ action: OrderType == 4 }"
          @click="onOrderTab(4)"
        >
          <text>申诉</text>
          <text class="line"></text>
        </view>
      </view>
    </view>
    <!-- 订单列表 -->
    <view class="order-list">
      <OList @itemClick="onOrderList"></OList>
      <!-- <view
        class="list"
        v-for="(item, index) in orderData"
        @click="onOrderList(item.orderNum)"
        :key="index"
      >
        <view class="title-status">
          <view class="title">
            <text>{{ item.shopName }}</text>
          </view>
          <view class="status">
            <text>{{ onOrderFilter(item.orderStatus) }}</text>
            <text class="iconfont icon-laji del"></text>
          </view>
        </view>
        <view class="goods-list">
          <view
            class="goods"
            v-for="list in item.tradeOrderDetailList"
            :key="list.id"
          >
            <view class="thumb">
              <image :src="list.saleSkuInfo.picUrl[0].url" mode="cover"></image>
            </view>
            <view class="item">
              <view class="goods-name">
                <text class="two-omit">{{ list.itemName }}</text>
                <text class="desc">数量：{{ list.itemNum }}</text>
              </view>
              <view class="goods-price">
                <text class="min">￥</text>
                <text class="max">{{ list.payAmount }}</text>
              </view>
            </view>
          </view>
					<view class="price">
						<text class="min">￥</text>
                <text class="max">{{ item.orderPrice }}</text>
					</view>
          <text>期望上门时间：{{ item.orderTime }}</text>
        </view>
        <view class="status-btn">
          <view
            v-if="
              item.orderStatus !== 'WAIT_PAY' &&
              item.orderStatus !== 'CLOSED' &&
              item.isReversPending
            "
            class="btn action"
          >
            <text>售后中</text>
          </view>
          <view
            v-if="item.orderStatus == 'WAIT_PAY'"
            class="btn action"
            @click.stop="onOrderCalcel(item.orderNum)"
          >
            <text>取消订单</text>
          </view>
          <view
            v-if="item.orderStatus == 'WAIT_RECEIVE' && !item.isReversPending"
            class="btn action"
            @click.stop="subReceive(item.orderNum)"
          >
            <text>确认收货</text>
          </view>
          <view
            v-if="
              item.orderStatus !== 'WAIT_PAY' &&
              item.orderStatus !== 'WAIT_RECEIVE' &&
              item.orderStatus !== 'CLOSED' &&
              !item.isReversPending
            "
            class="btn action"
            @click.stop="setReturn(item)"
          >
            <text>申请售后</text>
          </view>
        </view>
      </view> -->
    </view>
    <rf-empty
      :info="'您暂时没有相关订单！'"
      v-if="orderData.length === 0"
    ></rf-empty>
    <view
      class="loading"
      v-if="orderData.length > 0 && orderData.length == total"
      >—— 到底啦 ——</view
    >
    <rfLoading isFullScreen :active="isLoading"></rfLoading>
  </view>
</template>

<script>
import mSearch from "@/components/rf-search/rf-search";
import { orderList } from "@/api";
import { orderReceived } from "@/api/order";
import OList from "./cpns/o-list.vue";
export default {
  components: {
    mSearch,
    OList,
  },
  data() {
    return {
      OrderType: 0,
      keyword: "",
      // 分页参数
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      loadNewData: true, //是否重新请求数据
      orderStatus: "",
      orderStatusList: ["", "WAIT_PAY", "WAIT_RECEIVE", "SUCCESS", "CLOSED"],
      orderData: [
        {
          orderNum: 1,
          shopName: "店招名展示全部，一行展示不完换行展示咩哈哈哈哈哈",
          tradeName: "理光/CSDEC8100",
          orderStatus: "WAIT_PAY",
          orderStatusText: "待付款",
          orderTime: "2020-02-02 12:23:34",
          orderPrice: "123.00",
        },
        {
          orderNum: 2,
          shopName: "店招名展示全部，一行展示不完换行展示咩哈哈哈哈哈",
          tradeName: "理光/CSDEC8100",
          orderStatus: "WAIT_PAY",
          orderStatusText: "待付款",
          orderTime: "2020-02-02 12:23:34",
          orderPrice: "123.00",
        },
      ],
      customerId: "",
      isLoading: false,
    };
  },
  onLoad(params) {
    // this.$mHelper.loadVerify();
    uni.$on("refresh", (data) => {
      this.resetParams();
      // this.getOrderData();
    });
    this.OrderType = params.type;
    this.orderStatus = this.orderStatusList[this.OrderType];
    this.customerId = uni.getStorageSync("customerId");
    // this.getOrderData();
  },
  // 滚动到底部
  onReachBottom() {
    console.log("滚动到底部");
    if ((this.pageNumber - 1) * this.pageSize >= this.total) {
      return;
    }
    // this.getOrderData();
  },
  onPullDownRefresh() {
    if (this.isLoading) return;
    uni.startPullDownRefresh({
      success: () => {
        this.resetParams();
        this.getOrderData();
      },
    });
    uni.stopPullDownRefresh();
  },
  methods: {
    /**
     * 搜索点击
     */
    async onSearch() {
      // 执行搜索
      let verify = await this.$http.post(this.$mHelper.verifyAccessToken);
      if (verify.data === 2) {
        this.$mHelper.toast("网络异常，请稍后再试...");
        this.$mHelper.relogin();
        return false;
      }
      console.log(this.keyword);
      this.resetParams();
      this.getOrderData();
    },

    setReturn(data) {
      //设置返回方式
      uni.showActionSheet({
        title: "选择退款方式",
        itemList:
          data.orderStatus == "WAIT_DELIVER"
            ? ["仅退款"]
            : ["仅退款", "退货退款"],
        success: (res) => {
          this.types = res.tapIndex == 0 ? "仅退款" : "退货退款";
          this.tapIndex = res.tapIndex;
          uni.navigateTo({
            url: `/pages/order/afterSale?type=${res.tapIndex}&orderNum=${data.orderNum}`,
          });
        },
      });
    },
    /**
     * 返回点击
     */
    onBack() {
      uni.navigateBack();
    },
    // 订单过滤
    onOrderFilter(type) {
      let orderList = [
        {
          value: "WAIT_PAY",
          displayName: "待付款",
          help: "待付款",
        },
        {
          value: "PAID",
          displayName: "已支付",
          help: "已支付",
        },
        {
          value: "WAIT_DELIVER",
          displayName: "待发货",
          help: "已付款，待发货",
        },
        {
          value: "WAIT_RECEIVE",
          displayName: "待收货",
          help: "待收货",
        },
        {
          value: "CLOSED",
          displayName: "订单关闭",
          help: "订单关闭",
        },
        {
          value: "SUCCESS",
          displayName: "交易成功",
          help: "交易成功",
        },
      ];
      let orderStatus = orderList.filter((item) => {
        return item.value == type;
      })[0].displayName;
      return orderStatus;
    },
    /**
     * 订单tab点击
     */
    onOrderTab(type) {
      this.OrderType = type;
      this.orderStatus = this.orderStatusList[this.OrderType];
      this.keyword = "";

      this.resetParams();
      this.getOrderData();
    },
    // 重置参数
    resetParams() {
      this.pageNumber = 1;
      this.pageSize = 10;
      this.loadNewData = true;
    },
    // 获取订单数据
    async getOrderData() {
      if (this.isLoading) return;
      this.isLoading = true;
      let params = {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        orderStatus: this.orderStatus,
        customerId: this.customerId,
        keyWord: this.keyword,
      };
      let res = await this.$http.get(`${orderList}`, params);
      if (res.code === 200) {
        if (this.loadNewData) {
          this.orderData = res.data.rows;
          this.pageNumber++;
          this.total = res.data.total;
          this.loadNewData = false;
        } else {
          this.orderData = [...this.orderData, ...res.data.rows];
          this.pageNumber++;
          this.total = res.data.total;
        }
      }
      this.isLoading = false;
    },
    async refresh() {
      await this.resetParams();
      await this.getOrderData();
    },
    /**
     * 订单详情
     */
    onOrderList(id) {
      console.log(id);
      uni.navigateTo({
        url: `/pages/workOrder/orderDetails?id=${id}`,
      });
    },
    /**
     * 取消订单
     */
    onOrderCalcel(id) {
      uni.navigateTo({
        url: `/pages/order/cancelOrder?id=${id}`,
      });
    },
    async subReceive(orderNum) {
      await this.$http.post(`${orderReceived}${orderNum}`).then(() => {
        uni.showToast({
          title: "收货成功",
          icon: "none",
          duration: 1000,
        });
        setTimeout(() => {
          this.resetParams();
          this.getOrderData();
        }, 100);
      });
    },
    /**
     * 评价点击
     */
    onEvaluate() {
      uni.navigateTo({
        url: "/pages/MyEvaluatePush/MyEvaluatePush",
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  background-color: #f6f6f6;
  min-height: 100vh;
}

.top-box {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 10;
  width: 100%;
  background-color: #f6f6f6;
}

.search-box {
  width: 100%;
  background-color: rgb(242, 242, 242);
  padding: 15rpx 2.5%;
  display: flex;
  justify-content: space-between;
  height: 90rpx;

  .mSearch-input-box {
    width: 100%;
  }

  .input-box > input {
    width: 100%;
    height: 60rpx;
    font-size: 32rpx;
    border: 0;
    border-radius: 60rpx;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    padding: 0 3%;
    margin: 0;
    background-color: #ffffff;
  }
}

/* 顶部返回 */
.head-back {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100rpx;
  background-color: #ffffff;
  /* #ifdef APP-PLUS */
  height: calc(50rpx + var(--status-bar-height));
  padding-top: var(--status-bar-height);
  /* #endif */
  /* #ifdef MP */
  height: 150rpx;
  padding-top: 20rpx;

  /* #endif */
  .back {
    position: absolute;
    left: 0;
    top: 0;
    /* #ifdef APP-PLUS */
    padding-top: var(--status-bar-height);
    /* #endif */
    /* #ifdef MP */
    padding-top: 20rpx;
    /* #endif */
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100rpx;
    height: 100%;

    text {
      width: 20rpx;
      height: 20rpx;
      border-left: 2rpx solid #555555;
      border-bottom: 2rpx solid #555555;
      transform: rotate(45deg);
    }
  }

  .title {
    display: flex;
    align-items: center;

    text {
      font-size: 28rpx;
      color: #222222;
    }
  }

  .more-icon {
    position: absolute;
    right: 0;
    top: 0;
    /* #ifdef APP-PLUS */
    right: 0rpx;
    padding-top: var(--status-bar-height);
    /* #endif */
    /* #ifdef MP */
    right: 220rpx;
    padding-top: 20rpx;
    /* #endif */
    display: flex;
    align-items: center;
    height: 100%;

    .icon-list {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 100%;

      text {
        font-size: 34rpx;
        color: #222222;
      }
    }
  }
}

/* 订单tab */
.order-tab {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100rpx;

  .tab {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    /* width: 20%; */
    flex: 1;
    height: 80%;

    text {
      font-size: 26rpx;
      color: #959595;
    }
  }

  .action {
    text {
      color: #222222;
    }

    .line {
      position: absolute;
      left: 50%;
      bottom: 0;
      width: 60rpx;
      height: 6rpx;
      background: linear-gradient(to right, $base, #f6f6f6);
      transform: translate(-50%, 0);
    }
  }
}

/* 订单列表 */
.order-list {
  padding: 20rpx;
  border-radius: 20rpx;
  width: 100%;
  margin-top: 160rpx;
  overflow-y: auto;
  /* #ifdef APP-PLUS */
  margin-top: calc(170rpx + var(--status-bar-height));

  /* #endif */
  .list {
    padding: 0 4%;
    min-height: 400rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    margin-bottom: 20rpx;

    .title-status {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 100rpx;

      .title {
        display: flex;
        align-items: center;

        text {
          font-size: 26rpx;
          font-weight: bold;
          color: #222222;
        }
      }

      .status {
        display: flex;
        align-items: center;

        text {
          font-size: 26rpx;
          color: $base;
        }

        .del {
          padding: 10rpx;
          font-size: 34rpx;
          color: #222222;
          background-color: #f6f6f6;
          border-radius: 100%;
          margin-left: 20rpx;
        }
      }
    }

    .goods-list {
      width: 100%;

      .goods {
        display: flex;
        align-items: center;
        width: 100%;
        height: 200rpx;

        .thumb {
          display: flex;
          align-items: center;
          width: 30%;
          height: 100%;

          image {
            width: 160rpx;
            height: 160rpx;
            border-radius: 10rpx;
          }
        }

        .item {
          display: flex;
          // align-items: center;
          width: 70%;
          // height: 100%;

          .goods-name {
            width: 70%;

            .two-omit {
              font-size: 27rpx;
              font-family: PingFang SC;
              font-weight: bold;
              color: #0c0c0c;
              line-height: 39rpx;
              margin-top: 20rpx;
            }

            .desc {
              font-size: 27rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #535353;
              line-height: 39rpx;
            }
          }

          .goods-price {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 30%;

            text {
              color: #222222;
            }

            .min {
              font-size: 26rpx;
            }

            .max {
              font-size: 34rpx;
            }
          }
        }
      }
    }

    .status-btn {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      height: 100rpx;

      .btn {
        padding: 10rpx 30rpx;
        border: 2rpx solid #eeeeee;
        border-radius: 100rpx;
        margin-left: 20rpx;

        text {
          font-size: 26rpx;
          color: #555555;
        }
      }

      .action {
        border: 2rpx solid $base;

        text {
          color: $base;
        }
      }
    }
  }
}

.loading {
  height: 80upx;
  line-height: 80upx;
  text-align: center;
  color: #ccc;
  font-size: 24upx;
  width: 100%;
  margin-bottom: 20upx;
}
</style>
