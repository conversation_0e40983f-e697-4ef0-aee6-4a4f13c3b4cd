<template>
	<view class="pages">
		<!--    <view class="search-box" :class="fixed ? 'fixed' : 'auto'">-->
		<!--      <mSearch-->
		<!--        class="mSearch-input-box"-->
		<!--        :mode="2"-->
		<!--        button="inside"-->
		<!--        :placeholder="'搜索我的工单'"-->
		<!--        @search="onSearch(false)"-->
		<!--        @confirm="onSearch(false)"-->
		<!--        v-model="keyword"-->
		<!--      ></mSearch>-->
		<!--    </view>-->

		<!-- 			<view class="box1">
			<FormQuery :form-columns="formColumns" :is-machine="true" @search="search" />
		</view
		> -->

		<view class="content-box">
			<OList :infoData="orderList" @itemClick="handleOrderListClick"></OList>
			<rf-empty :info="'您暂时没有相关工单！'" v-if="orderList.length === 0"></rf-empty>
		</view>
		<!-- 回到顶部 -->
		<rf-back-top :scrollTop="scrollTop"></rf-back-top>

		<view class="loading" v-if="orderList.length > 0 && orderList.length === total">—— 到底啦 ——</view>
		<rfLoading isFullScreen :active="isLoading"></rfLoading>
	</view>
</template>

<script>
import mSearch from '@/components/rf-search/rf-search';
import OList from './cpns/o-list.vue';
// import SettledList from "./cpns/settled-list.vue";
import { myWorkOrderList } from '@/api/workOrder';

import FormQuery from '@/components/formQuery/index';
export default {
	components: {
		mSearch,
		OList,
		FormQuery
		// SettledList,
	},
	data() {
		return {
			// 分页参数
			pageNumber: 1,
			pageSize: 10,
			total: 0,
			orderType: 0,
			keyword: '',
			fixed: false, // 是否固定在顶部
			loadNewData: true, // 是否第一次新增数据
			// 滚动顶部
			scrollTop: 0,

			// 已结算
			orderList: [],
			isLoading: false,
			isFirstLoad: true,
			searchparms: {},
			datetimerange: '',
			showProduct: '',
			productList: [],
			formColumns: [
				{
					dataIndex: 'customerName',
					title: '客户名称',
					valueType: 'input'
				},
				{
					dataIndex: 'customerPhone',
					title: '保修手机号',
					valueType: 'input'
				},
				{
					dataIndex: 'product',
					title: '品牌机型',
					valueType: 'machine'
				},
				{
					dataIndex: 'date',
					title: '日期',
					valueType: 'datetime',
					inputType: 'daterange'
				}
			]
		};
	},
	mounted() {
		this.resetParams();
		this.getPageData();
	},
	onLoad() {
		// 用户收取获取其地理位置
		uni.getSetting({
			success: (res) => {
				if (!res.authSetting['scope.userLocation']) {
					uni.authorize({
						scope: 'scope.userLocation',
						success: () => {
							console.log('用户已授权位置信息');
						},
						fail: () => {
							console.log('用户拒绝授权位置信息');
						}
					});
				} else {
					console.log('位置信息已授权');
				}
			}
		});
	},
	onShow() {
		if (this.isFirstLoad) return;
		this.resetParams();
		this.getPageData();
	},

	// 页面滚动
	onPageScroll(e) {
		// 获取滚动距离
		this.scrollTop = e.scrollTop;
		this.fixed = this.scrollTop > 0;
	},

	// 下拉刷新
	onPullDownRefresh() {
		this.resetParams();
		this.getPageData();
		uni.stopPullDownRefresh();
	},
	// 触底事件
	onReachBottom() {
		if ((this.pageNumber - 1) * this.pageSize >= this.total) return;
		this.getPageData();
	},
	methods: {
		/**
		 * 搜索点击
		 */
		onSearch() {
			console.log(this.keyword);
			this.resetParams();
			this.getPageData();
		},
		// 查询
		search(parameter) {
			console.log(parameter);
		},
		// 重置分页参数
		resetParams() {
			this.orderList = [];
			this.pageNumber = 1;
			this.pageSize = 10;
			this.loadNewData = true;
		},

		getPageData() {
			this.getOrderListByStatus();
		},
		// 订单列表点击
		handleOrderListClick(item) {
			console.log(item);
			let id = item.id;
			uni.navigateTo({
				url: `/pages/workOrder/myOrderDetails?id=${id}`
			});
		},

		// 我的工单列表数据
		getOrderListByStatus() {
			this.isLoading = true;
			let id = uni.getStorageSync('userInfo').id;
			let params = {
				pageNumber: this.pageNumber,
				pageSize: this.pageSize,
				engineerId: id,
				keyword: this.keyword
			};
			myWorkOrderList(params)
				.then((res) => {
					console.log(res);
					if (res.code === 200) {
						try {
							if (this.loadNewData) {
								let result = res.data.rows;
								if (result.length === 0) return this.$mHelper.toast('暂无数据');
								this.orderList = result.map((item) => {
									return this.mapDataToList(item);
								});

								this.total = Number(res.data?.total) || 0;
								this.pageNumber++;
								this.loadNewData = false;
							} else {
								let temp = res.data.rows.map((item) => {
									return this.mapDataToList(item);
								});
								this.orderList = [...this.orderList, ...temp];
								this.pageNumber++;
								this.total = Number(res.data?.total) || 0;
							}
							this.isFirstLoad = false;
						} catch (error) {
							console.log(error);
						}
					}
				})
				.finally(() => {
					this.isLoading = false;
				});
		},
		// 映射数据到订单列表
		// 这里需要根据接口返回的数据结构进行修改
		mapDataToList(obj) {
			return {
				...obj,
				desc: obj.customer?.name || '',
				name: obj.brand + '/' + obj.machine || '',
				time: obj.expectArriveTime || '',
				status: obj.status?.label || '',
				price: obj.totalPay || '',
				id: obj.id
			};
		}
	}
};
</script>

<style lang="scss" scoped>
.pages {
	width: 100%;
	min-height: 100vh;
	padding: 20rpx;
	box-sizing: border-box;
	background: #f2f2f2;

	.search-box {
		width: 100%;
		background-color: #f2f2f2;
		padding: 15rpx 2.5%;
		display: flex;
		justify-content: space-between;
		height: 90rpx;
		&.fixed {
			position: fixed;
			left: 0;
			top: 0;
		}

		&.auto {
			position: relative;
		}

		.mSearch-input-box {
			width: 100%;
		}

		.input-box > input {
			width: 100%;
			height: 60rpx;
			font-size: 32rpx;
			border: 0;
			border-radius: 60rpx;
			-webkit-appearance: none;
			-moz-appearance: none;
			appearance: none;
			padding: 0 3%;
			margin: 0;
			background-color: #ffffff;
		}
	}

	.content-box {
		width: 100%;
		// margin-top: 510rpx;
	}
}
.loading {
	height: 80upx;
	line-height: 80upx;
	text-align: center;
	color: #ccc;
	font-size: 24upx;
	width: 100%;
	margin-bottom: 20upx;
}
</style>
