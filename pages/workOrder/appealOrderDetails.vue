<template>
	<view class="page">
		<view class="container">
			<view class="item">
				<view class="label">客户名称:</view>
				<view class="value">{{details.customerName}}</view>
			</view>
			<view class="item">
				<view class="label">客户电话:</view>
				<view class="value value-tel" @click="callToEngineer(details.customerStaffTel)">{{details.customerStaffTel ? details.customerStaffTel : '无电话'}}<u-icon name="phone-fill" size="22" color="#FF541E"></u-icon></view>
			</view>
			<view class="item wrap">
				<view class="label">申诉理由描述:</view>
				<view class="value" style="word-break: break-all">{{
          details.reason
        }}</view>
			</view>
			<view class="item wrap">
				<view class="label">上传凭证:</view>
				<view class="value">
					<image v-for="(item, index) in details.picUrls" @click="previewImage(details.picUrls, index)" :src="item.url" :key="item.name" mode="heightFix" class="image"></image>
				</view>
			</view>
			<view class="item">
				<view class="label">接单工程师:</view>
				<view class="value">{{ details.engineer.name }}</view>
			</view>
			<view class="item">
				<view class="label">申诉单编号:</view>
				<view class="value">{{ details.code }}</view>
			</view>
			<view class="item">
				<view class="label">关联工单号:</view>
				<view class="value">{{ details.workOrderCode }}</view>
			</view>
			<view class="item">
				<view class="label">申诉发起时间:</view>
				<view class="value">{{ details.createdAt }}</view>
			</view>
			<view class="item">
				<view class="label">申诉结束时间:</view>
				<view class="value">{{
          details.status.value !== "in_appeal" ? details.updatedAt : "/"
        }}</view>
			</view>
			<view class="item" @click="toWorkOrderDetail(details.workOrderId)">
				<view class="label">工单详情:</view>
				<view class="value red-font">查看＞</view>
			</view>
		</view>
		<view class="bottom-btn" v-if="details.status.value === 'in_appeal'">
			<view class="yellow-btn" @click="cancelAppeal">
				<text>驳回申诉</text>
			</view>
			<view class="red-btn" @click="confirmAppeal">
				<text>已解决</text>
			</view>
		</view>
		<rfLoading isFullScreen :active="isLoading"></rfLoading>
	</view>
</template>

<script>
	import { appealDetailApi, appealHandleApi } from "@/api/appeal";

	export default {
		name: "AppealDetail",
		data() {
			return {
				detailId: "",
				isLoading: false,
				details: {},
				rejectReason: ''
			};
		},
		onLoad({ id }) {
			if (!id) {
				uni.navigateTo({
					url: `/pages/workOrder/appealOrder`,
				});
				return;
			}
			this.detailId = id;
			this.loadData();
		},
		methods: {
			async loadData() {
				try {
					this.isLoading = true;
					const result = await appealDetailApi(this.detailId);
					if (result.code === 200) {
						this.details = result.data;
					}
				} catch (err) {
					console.error(err);
				} finally {
					this.isLoading = false;
				}
			},
			toWorkOrderDetail(id) {
				uni.navigateTo({
					url: `/pages/workOrder/myOrderDetails?id=${id}`,
				});
			},
			previewImage(url, index) {
				uni.previewImage({
					current: index,
					urls: url.map((item) => item.url),
				});
			},
			callToEngineer(num) {
				uni.makePhoneCall({
					phoneNumber: num,
					success: () => {
						console.log("拨打电话成功");
					},
					fail: (err) => {
						console.log("拨打电话失败", err);
					},
				});
			},
			cancelAppeal() {
				uni.showModal({
					title: "提示",
					editable: true,
					placeholderText: '请输入驳回的原因',
					success: async (res) => {
						if (res.confirm) {
							if (!res.content) {
								uni.showToast({
									title: "请输入驳回的原因",
									icon: "none",
								});
								return
							}
							try {
								this.isLoading = true;
								const result = await appealHandleApi({
									id: this.detailId,
									rejectReason: res.content,
									resolved: false,
								});
								if (result.code === 200) {
									uni.showToast({
										title: "驳回成功",
										icon: "none",
										duration: 2000,
										success: () => {
											setTimeout(() => {
												uni.$emit("refreshAppealList");
												uni.navigateBack();
											}, 1500);
										},
									});
								}
							} catch (err) {
								console.error(err);
							} finally {
								this.isLoading = false;
							}
						}
					},
				});
			},
			confirmAppeal() {
				uni.showModal({
					title: "提示",
					content: "确认该申诉已解决？",
					success: async (res) => {
						if (res.confirm) {
							try {
								this.isLoading = true;
								const result = await appealHandleApi({
									id: this.detailId,
									resolved: true,
								});
								if (result.code === 200) {
									uni.showToast({
										title: "操作成功",
										icon: "none",
										duration: 2000,
										success: () => {
											setTimeout(() => {
												uni.$emit("refreshAppealList");
												uni.navigateBack();
											}, 1500);
										},
									});
								}
							} catch (err) {
								console.error(err);
							} finally {
								this.isLoading = false;
							}
						}
					},
				});
			},
		},
	};
</script>

<style lang="scss" scoped>
	.page {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100vh;
		background-color: #f5f6f8;

		.container {
			width: 100%;
			min-height: calc(100% - 150rpx);
			background-color: #fff;
			margin-top: 21rpx;
			padding: 0 22rpx;
			display: flex;
			flex-direction: column;
			justify-content: flex-start;

			.item {
				width: 100%;
				display: flex;
				justify-content: space-between;
				border-bottom: 1rpx solid #f3f3f3;
				padding: 26.67rpx 0;

				.label {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #323333;
					width: 300rpx;
				}

				.value {
					width: 400rpx;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #666666;
					text-align: right;
				}

				.value-tel {
					display: flex;
					align-items: center;
					justify-content: flex-end;
				}

				.red-font {
					color: #ff541e;
				}
			}

			.wrap {
				display: block;

				.value {
					width: 100%;
					margin-top: 26.67rpx;
					text-align: left;
					display: flex;

					image {
						height: 153.6rpx;
					}

					image+image {
						margin-left: 26.67rpx;
					}
				}
			}
		}

		.bottom-btn {
			display: flex;
			align-items: center;
			width: 100%;
			height: 75rpx;
			padding: 0 22rpx;
			position: fixed;
			bottom: 82rpx;

			&>view {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 50%;
				height: 75rpx;

				text {
					font-size: 30rpx;
					color: #ffffff;
				}
			}

			.yellow-btn {
				background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
				border-radius: 70rpx 0 0 70rpx;
			}

			.red-btn {
				background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
				border-radius: 0 70rpx 70rpx 0;
			}
		}
	}
</style>