<!--
 * @Description: 
 * @Autor: shh
 * @Date: 2023-03-13 09:33:58
 * @LastEditors: shan<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-17 19:16:34
-->
<template>
	<view class="home-box">
		<!-- 页面 -->
		<view class="main-wrapper">
			<view class="head-info">
				<view class="user-info" @tap="!userInfo ? navTo('/pages/user/login') : navTo('/pages/user/userinfo')">
					<view class="left">
						<image class="img" src="../../static/images/top.png" mode="widthFix"></image>
					</view>
					<view class="right">
						<view v-if="!userInfo" class="name">登录</view>
						<view v-if="userInfo" class="name">{{ userInfo.name }}</view>
						<!-- <view v-if="userInfo" class="phone">
              电话：{{ userInfo.name }}
            </view> -->
					</view>
				</view>
			</view>
			<view class="setting" @tap.stop="gosetting">
				<text class="iconfont iconsettings"></text>
			</view>
			<view class="list">
				<view class="list-box">
					<template v-for="item in listData">
						<view class="item" v-if="powerMenu.includes(item.power)" :key="item.text" @click="navTo(item.path)">
							<image class="img" :src="item.url" mode="heightFix"></image>
							<text class="text">{{ item.text }}</text>
							<rf-badge v-if="item.count && item.count > 0" type="error" size="small" class="badge" :text="item.count > 99 ? '99+' : item.count"></rf-badge>
						</view>
					</template>
				</view>
			</view>
		</view>

		<!-- 页面加载-->
		<rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
	</view>
</template>
<script>
/**
 * @des 首页
 *
 * @<NAME_EMAIL>
 * @date 2020-01-08 14:14
 * @copyright 2019
 */
import { getOpenId, verifyAccessToken } from '@/api/login';

import { goodsListApi, classifyList } from '@/api/index';
// import recommend from "@/components/home/<USER>";
// import rfSwiperSlide from "@/components/rf-swiper-slide";
import indexConfig from '@/config/index.config';
import mSearch from '@/components/rf-search/rf-search.vue';
import { mapMutations } from 'vuex';
import rfBadge from '@/components/rf-badge/rf-badge';
import { classifyTag } from '@/api';

export default {
	components: {
		mSearch,
		rfBadge
	},
	data() {
		return {
			userInfo: {},
			isfirst: true,
			fixed: true,
			fixeds: true,
			powerMenu: [],
			listData: [
				{
					text: '待接工单',
					power: '/pendingOrder',
					url: '/static/images/home/<USER>',
					path: '/pages/workOrder/pendingOrder'
				},
				{
					text: '我的工单',
					power: '/myWorkOrder',
					url: '/static/images/home/<USER>',
					path: '/pages/workOrder/myWorkOrder'
				},

				{
					text: '申诉单',
					power: '/appealOrder',
					url: '/static/images/home/<USER>',
					path: '/pages/workOrder/appealOrder'
				},
				{
					text: '知识库',
					power: '/engLearn',
					url: '/static/images/home/<USER>',
					path: '/pages/learn/index'
				},
				{
					text: '耗材库',
					power: '/wareStore',
					url: '/static/images/home/<USER>',
					path: '/pages/system/warehouse'
				},
				{
					text: '申领耗材',
					power: '/wareApply',
					url: '/static/images/home/<USER>',
					path: '/pages/system/wareapply'
				},
				{
					text: '退料',
					power: '/returnApply',
					url: '/static/images/home/<USER>',
					path: '/pages/system/returnApply'
				},
				{
					text: '客户信息',
					power: '/customer',
					url: '/static/images/home/<USER>',
					path: '/pages/customer/customer'
				},
				{
					text: '出库管理',
					power: '/outStorage',
					url: '/static/images/home/<USER>',
					path: '/pages/system/outStorage'
				},
				{
					text: '领料审核',
					power: '/receiveList',
					url: '/static/images/home/<USER>',
					path: '/pages/system/receiveList'
				},
				{
					text: '问题商品',
					power: '/wrong',
					url: '/static/images/home/<USER>',
					path: '/pages/system/wrong'
				}
			],
			istoken: uni.getStorageSync('accessToken'),
			// 顶部背景
			userBg: this.$mAssetsPath.userBg,
			// 是否打开了弹窗
			// 顶部适配
			paddingTop: null,
			// 店铺名字
			shop_name: '',
			// HOME热键信息
			menuInfo: this.$getMenu.menuInfo,
			// 搜索关键词
			keyword: '',
			// 导航栏当前项
			classifyShow: 0,
			// 导航栏列表
			classList: [],
			// 是否登记机型
			islistModels: false,

			// 商品列表
			dataList: [],
			// 商户配置
			config: {},
			// 加载动画
			loading: true,
			// 是否加载完成
			isloading: false,
			// 是否到底
			isEnloading: false,
			// 滚动顶部
			scrollTop: 0,
			//分类id
			categoryId: '',
			//筛选列表参数
			tagsList: [],
			//机型列表
			productTreeIdList: [],
			// 分页参数
			pageNumber: 1,
			// 每页大小
			pageSize: 10,
			// 总数
			total: 0,

			loadNewData: true, // 是否第一次新增数据
			// 没咋用
			newsBg: this.$mAssetsPath.newsBg,
			errorImage: this.$mAssetsPath.errorImage,
			appName: this.$mSettingConfig.appName,
			isOpenIndexCate: this.$mSettingConfig.isOpenIndexCate
		};
	},

	onHide() {
		// this.loading = true; // 页面离开，关闭弹窗
		// uni.removeStorageSync("keyword"); // 离开当前页面 删除本地关键词
	},
	async onShow() {
		setTimeout(() => {
			this.loading = false;
			this.powerMenu = uni.getStorageSync('powerMenu');
		}, 1000);
		this.userInfo = uni.getStorageSync('userInfo');
		const accessToken = uni.getStorageSync('accessToken');
		if (accessToken) {
			await this.getCount();
		}

		// this.$mHelper.loadVerify();
		// if (uni.getStorageSync("from")) {
		//   if (uni.getStorageSync("from") == "classify") {
		//     this.resetParams(); // 初始化分页
		//     this.initData(); // 获取列表数据
		//   } else if (uni.getStorageSync("from") == "detail") {
		//   }
		//   uni.removeStorageSync("from");
		// } else {
		//   this.keyword = uni.getStorageSync("keyword"); // 关键词
		//   this.dataList = []; // 初始化列表数据
		//   // this.productTreeIdList = []; // 初始化机型筛选数据
		//   // this.tagsList = []; // 初始化零件分类，所属单元参数
		//   this.shop_name = uni.getStorageSync("userInfo"); // 获取店铺信息
		//   this.resetParams(); // 初始化分页
		//   this.initData(); // 获取列表数据
		//   this.isfirst = true;
		// }
	},

	// 监听用户点击右上角转发到朋友圈
	onShareTimeline(res) {
		return {
			title: '一家提供一站式耗材、零件采购、维修服务的文印平台',
			desc: '一家提供一站式耗材、零件采购、维修服务的文印平台',
			type: 0,
			summary: ''
		};
	},
	// 用户点击分享
	onShareAppMessage() {
		let shareParams = {
			title: '本印猫',
			desc: '一家提供一站式耗材、零件采购、维修服务的文印平台',
			path: '/pages/home/<USER>'
		};
		return shareParams;
	},

	// 页面滚动
	onPageScroll(e) {
		// 获取滚动距离
		this.scrollTop = e.scrollTop;
		if (this.scrollTop > 0) {
			this.fixed = true;
		} else {
			this.fixed = false;
		}
	},
	// // 滚动到底部
	// onReachBottom() {
	//   if ((this.pageNumber - 1) * this.pageSize >= this.total) {
	//     this.isEnloading = true;
	//     return;
	//   }
	//   this.fixed = true;
	//   this.getGoodsList(); // 商品列表
	// },

	// 下拉刷新
	onPullDownRefresh() {
		// this.fixed = false;
		this.getCount();
		uni.stopPullDownRefresh();
	},

	methods: {
		// 统一跳转接口,拦截未登录路由
		navTo(route) {
			this.$mRouter.push({
				route
			});
		},
		gosetting() {
			this.$mRouter.push({
				route: '/pages/system/set'
			});
		},
		async getCount() {
			const result = await this.$http.get('/api/engineer/work-order/sumaryCount', {});
			this.listData = this.listData.map((item) => {
				if (item.text === '申诉单') {
					item.count = result?.data?.appealOrderCount;
				}
				if (item.text === '我的工单') {
					item.count = result?.data?.myWorkOrderCount;
				}
				if (item.text === '待接工单') {
					item.count = result?.data?.pendingOrdersCount;
				}
				return item;
			});
		}
	}
};
</script>
<style lang="scss" scoped>
.home-box {
	width: 100%;
	height: 100vh;
	box-sizing: border-box;
	position: relative;
	// background: linear-gradient(to bottom, #ff541e 25%, #f5f6f8 34%);
	background: transparent;
	background-size: 100% 100vh;
	background-repeat: no-repeat;
	overflow: hidden;
	overflow-y: scroll;

	.main-wrapper {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		position: relative;

		.head-info {
			width: 100%;
			// background: linear-gradient(to bottom, #ff541e, #ff541e);
			background: #f8f8f8 url('https://benyin-1315885374.cos.ap-chengdu.myqcloud.com/program%2Fuser_bg.png');
			background-size: 100% 100%;
			height: 490rpx;

			.user-info {
				width: 100%;
				height: 120rpx;
				padding: 0 20rpx;
				display: flex;
				position: absolute;
				left: 0;
				top: 182rpx;

				.left {
					width: 120rpx;
					height: 100%;
					margin-right: 20rpx;

					.img {
						width: 100%;
					}
				}

				.right {
					flex: 1;
					height: 100%;
					color: #333;
					display: flex;
					align-content: space-around;
					flex-wrap: wrap;

					.name {
						width: 100%;
						font-size: 32rpx;
						font-family: PingFang SC;
						font-weight: bold;
					}

					.phone {
						width: 100%;
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: bold;
					}
				}
			}
		}

		.list {
			box-sizing: border-box;
			position: absolute;
			left: 0;
			right: 0;
			top: 350rpx;
			z-index: 1;
			margin: 20rpx;
			border-radius: 23rpx;
			padding: 30rpx;
			background: #fff;
			color: #333333;

			.list-box {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;

				.item {
					display: flex;
					flex-direction: column;
					align-items: center;
					position: relative;
					width: 33%;
					margin: 40rpx 0;

					.img {
						height: 50rpx;
					}

					.text {
						margin-top: 12rpx;
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #333333;
					}

					.badge {
						position: absolute;
						right: 60rpx;
						top: -20rpx;
					}
				}
			}
		}
	}
}

.setting {
	position: absolute;
	top: 200rpx;
	right: 40rpx;
	z-index: 999;

	text {
		font-size: 50rpx;
	}
}
</style>
