<template>
	<view class="main">
		<view class="list-item" v-if="!loading">
			<view class="des-title"> {{details.title}} </view>
			<view class="des-class">
				<view class="des-class-title">
					知识库类型
				</view>
				<view class="des-card-list">
					<view class="des-card-card">
						{{details.type.label}}
					</view>
				</view>
			</view>
			<view class="des-class des-boder">
				<view class="des-class-title">
					适用机型
				</view>
				<view class="des-card-list">
					<view class="des-card-card" v-for="(items,indexs) in details.models" :key="indexs">
						{{items}}
					</view>
				</view>
			</view>
		</view>
		<view class="list-item" v-if="!loading && details.codeExplain">
			<view class="des-title"> 代码解释 </view>
			<view class="des-problem" v-html="details.codeExplain">
			</view>
		</view>
		<view class="list-item" v-if="!loading">
			<view class="des-title"> 相关案例 </view>
			<view class="des-details" @click="toCaseFn(items)" v-for="(items,indexs) in details.knowledgeRepairCase" :key="indexs">
				<view style="display: flex;align-items: center;">
					<view class="des-details-number">{{indexs+1}}</view>
					<view class="des-details-title">{{items.title}}</view>
				</view>
				<u-icon class="des-icon" name="arrow-right" color="#343434"></u-icon>
			</view>

		</view>
		<rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
	</view>
</template>

<script>
	import {
		learnAppletDetail
	} from "@/api/order.js";
	export default {
		data() {
			return {
				details: [],
				loading: true,
			}
		},
		onLoad(option) {
			this.learnAppletDetailFn(option.id)
		},
		// 下拉刷新
		onPullDownRefresh() {
			uni.stopPullDownRefresh();
		},
		// 上拉加载
		onReachBottom() {},
		methods: {
			// 跳转案例
			toCaseFn(parmes) {
				let obj = encodeURIComponent(JSON.stringify(parmes))
				let route = `/pages/help/case?parmes=${obj}`;
				this.$mRouter.push({
					route,
				});
			},
			learnAppletDetailFn(id) {
				this.$http.get(`/api/knowledge-base-info/appletDetail/${id}`).then(res => {
					this.details = res.data
					this.loading = false

				})
			}

		}
	}
</script>

<style lang="scss" scoped>
	.main {
		width: 100%;
		height: 100%;
		min-height: 100vh;
		padding-top: 27rpx;
		background-color: #F2F2F2;

		.list-item {
			width: 100%;
			padding: 26rpx;
			box-sizing: border-box;
			margin-bottom: 27rpx;
			background-color: #FFFFFF;
			display: flex;
			flex-direction: column;


			.des-title {
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #343434;
			}

			.des-class {
				display: flex;
				align-items: center;
				padding: 12rpx 0;
				box-sizing: border-box;

				.des-class-title {
					font-size: 27rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #9A9A9A;
					margin-right: 26rpx;
				}

				.des-card-list {
					flex: 1;
					height: 70rpx;
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					overflow: hidden;


					.des-card-card {
						height: 46rpx;
						padding: 0 16rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 27rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #535353;
						background: #F2F2F2;
						border-radius: 3rpx;
						margin: 10rpx;
					}
				}

				&.des-boder {
					border-top: 1rpx solid #F1F1F1;

				}
			}

			.des-problem {
				min-height: 237rpx;
				margin-top: 12rpx;
				padding: 26rpx;
				box-sizing: border-box;
				font-size: 32rpx;
				font-family: PingFang SC;
				background: #1e1e1e;
				border-radius: 10rpx;
				color: #FFFFFF;

				>text {
					font-size: 24rpx;
					color: red;
				}
			}

			.des-details {
				width: 100%;
				height: 90rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.des-details-number {
					width: 36rpx;
					height: 36rpx;
					margin-right: 14rpx;
					background: #F1F1F1;
					border-radius: 3rpx;
					font-size: 27rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #343434;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.des-details-title {
					font-size: 27rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #343434;
				}

				&.border {
					border-top: 1rpx solid #F1F1F1;
					border-bottom: 1rpx solid #F1F1F1;

				}
			}
		}

		.rfLoading {
			position: fixed;
			top: 0;
			left: 0;
			z-index: 100000000000000;
		}
	}
</style>