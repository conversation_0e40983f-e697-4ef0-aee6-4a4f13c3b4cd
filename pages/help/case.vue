<template>
	<view class="main">
		<view class="list-item" v-if="listData && !loading">
			<view class="des-title"> {{listData.title}} </view>
			<view class="des-class">
				<view class="des-class-title"> 故障描述: </view>
				<view class="des-class-problem" v-html="listData.faultDesc"></view>
			</view>
			<view class="des-class">
				<view class="des-class-title"> 解决措施: </view>
				<view class="des-class-problem" v-html="listData.solutionMeasures"></view>
			</view>
		</view>
		<view class="list-item no-padding" v-if="listData.saleSkuVos && listData.saleSkuVos.length>0" @click="isShowGoods=!isShowGoods">
			<view class="des-title padding"> 相关商品推荐 <u-icon class="des-icon" :name="isShowGoods ? 'arrow-up':'arrow-down'" color="#343434"></u-icon>
			</view>
			<view class="des-goods" v-if="!isShowGoods">
				<view class="goods-item" @tap.stop="todGoodsDetails(item)" v-for="(item,index) in listData.saleSkuVos" :key="index">
					<image class="goods-image" :src="item.skuPicUrl[0].url" mode=""></image>
					<view class="goods-info">
						<view class="goods-name">
							{{item.itemName}}
						</view>
						<view class="goods-code">
							OEM编号：{{item.oemNumber}}
						</view>
						<view class="goods-price">
							￥{{item.price}}
						</view>
					</view>
				</view>
			</view>

		</view>
		<view class="list-item no-padding" v-if="listData.installVideo && listData.installVideo.length>0" @click="isShowopen=!isShowopen">
			<view class="des-title padding"> 拆装视频 <u-icon class="des-icon" :name="isShowopen ? 'arrow-up':'arrow-down'" color="#343434"></u-icon>
			</view>
			<view class="des-goods padding" v-if="!isShowopen">
				<view class="video-item" @click.stop="playFn(item.url)" v-for="(item,index) in listData.installVideo" :key="index">
					<view class="video-cont">
						<video class="video-img" :show-center-play-btn='false' :src="item.url" initial-time='0.1' object-fit="cover" :controls='false'></video>
						<view class="video-model">
							<image class="icon_play" src="../../static/images/learn/icon_play.png" mode=""></image>
						</view>
					</view>
					<view class="video-des">
						{{item.name}}
					</view>
				</view>
			</view>

		</view>
		<view class="list-item no-padding" v-if="listData.relateVideo && listData.relateVideo.length>0" @click="isShowCorre=!isShowCorre">
			<view class="des-title padding"> 相关视频 <u-icon class="des-icon" :name="isShowCorre ? 'arrow-up':'arrow-down'" color="#343434"></u-icon>
			</view>
			<view class="des-goods padding" v-if="!isShowCorre">
				<view class="video-item" @click.stop="playFn(item.url)" v-for="(item,index) in listData.relateVideo" :key="index">
					<view class="video-cont">
						<video class="video-img" :show-center-play-btn='false' :src="item.url" initial-time='0.1' object-fit="cover" :controls='false'></video>
						<view class="video-model">
							<image class="icon_play" src="../../static/images/learn/icon_play.png" mode=""></image>
						</view>
					</view>
					<view class="video-des">
						{{item.name}}
					</view>
				</view>
			</view>

		</view>

		<rfLoading class="rfLoading" isFullScreen :active="loading"></rfLoading>
		<!-- 引导图 -->
		<!-- 播放弹窗 -->
		<view :class="isfull ? 'play_video_max':'play_model'" v-if="isPlay" @tap.stop="isPlay=false,autoplay=false">
			<video :class="isfull ? 'play_video_maxs':'play_video'" :autoplay="autoplay" @fullscreenchange="fullscreen" @tap.stop="{}" object-fit="cover" :src="videoUrl" controls></video>
		</view>
	</view>
</template>

<script>
	// import recommend from "@/components/home/<USER>";
	export default {
		components: {
			// recommend
		},
		data() {
			return {

				listData: {},
				videoUrl: '',
				isShowGoods: false, // 是否展示商品
				isShowopen: false, // 是否展示拆装视频
				isShowCorre: false, // 是否展相关
				loading: true,
				isPlay: false, //是否展示播放弹窗
				autoplay: false, // 是否自动播放

				isfull: false
			}
		},
		onLoad(option) {
			this.listData = JSON.parse(decodeURIComponent(option.parmes))
			this.loading = false
		},
		// 下拉刷新
		onPullDownRefresh() {
			uni.stopPullDownRefresh();
		},
		// 上拉加载
		onReachBottom() {},
		methods: {
			todGoodsDetails(parmes) {
				let route = `/pages/goods/detail?id=${parmes.itemId}&skuId=${parmes.skuId}`;
				this.$mRouter.push({
					route,
				});
			},
			// 播放
			playFn(url) {
				this.isPlay = true
				this.autoplay = true
				this.videoUrl = url
			},
			// 触发全屏播放
			fullscreen(e) {
				this.isfull = !this.isfull
			}
		}
	}
</script>

<style lang="scss" scoped>
	.main {
		width: 100%;
		height: 100%;
		min-height: 100vh;
		padding-top: 27rpx;
		padding-bottom: 40rpx;
		background-color: #F2F2F2;

		.list-item {
			width: 100%;
			padding: 26rpx;
			box-sizing: border-box;
			margin-bottom: 27rpx;
			background-color: #FFFFFF;
			display: flex;
			flex-direction: column;


			.des-title {
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #343434;
				display: flex;
				align-items: center;
				justify-content: space-between;

				&.padding {
					padding: 0 26rpx;
					box-sizing: border-box;
				}
			}

			.des-class {
				width: 100%;

				.des-class-title {
					width: 100%;
					margin: 26rpx 0;
					font-size: 27rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #333333;
				}

				.des-class-problem {
					width: 100%;
					min-height: 187rpx;
					line-height: 39rpx;
					padding: 26rpx;
					box-sizing: border-box;
					font-size: 27rpx;
					font-weight: 500;
					color: #535353;
					background-color: #F2F2F2;
					border-radius: 7rpx;

				}
			}

			.des-goods {
				width: 100%;
				max-height: 705rpx;
				overflow-y: scroll;

				.goods-item {
					width: 100%;
					height: 160rpx;
					padding: 24rpx;
					box-sizing: border-box;
					margin-top: 18rpx;
					display: flex;
					align-items: center;

					.goods-image {
						width: 160rpx;
						height: 160rpx;
						margin-right: 24rpx;
					}

					.goods-info {
						flex: 1;
						width: 100%;
						height: 160rpx;
						display: flex;
						// align-items: center;
						flex-direction: column;
						justify-content: space-between;
						font-size: 28rpx;

						.goods-name {
							overflow: hidden;
							font-weight: bold;
							text-overflow: ellipsis;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							line-height: 36rpx;
						}

						.goods-code {
							font-size: 28rpx;
						}

						.goods-price {
							font-size: 28rpx;
							font-weight: bold;
							color: #fa436a;

						}
					}

				}

				.video-item {
					width: 100%;
					height: 180rpx;
					background: #FFFFFF;
					padding: 14rpx 0;
					box-sizing: border-box;
					display: flex;
					align-items: center;


					.video-cont {
						width: 240rpx;
						height: 152rpx;
						margin-right: 26rpx;
						border-radius: 7rpx;
						overflow: hidden;
						position: relative;

						.video-img {
							width: 240rpx;
							height: 152rpx;
						}

						.video-model {
							width: 240rpx;
							height: 152rpx;
							background: rgba(0, 0, 0, 0.4);
							position: absolute;
							top: 0;
							left: 0;
							z-index: 10;
							display: flex;
							align-items: center;
							justify-content: center;

							.icon_play {
								width: 45rpx;
								height: 45rpx;
							}
						}
					}

					.video-des {
						flex: 1;
						font-size: 27rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #333333;
					}

				}

				&.padding {
					padding: 0 32rpx;
					padding-top: 14rpx;
				}

			}

			&.no-padding {
				padding: 26rpx 0 !important;
			}
		}

		.rfLoading {
			position: fixed;
			top: 0;
			left: 0;
			z-index: 100000000000000;
		}


		.play_model {
			width: 100%;
			height: 100vh;
			position: fixed;
			top: 0;
			left: 0;
			z-index: 11;
			background: rgba(0, 0, 0, 0.4);
			display: flex;
			align-items: center;
			justify-content: center;

			.play_video {
				width: 100%;
				height: 400rpx;
			}

			.play_video_maxs {
				width: 100vh;
				height: 100vw;
			}


		}

		.play_video_max {
			width: 100vh;
			height: 100vw;
			transform: rotate(90);
			position: fixed;
			top: 0;
			left: 0;
			z-index: 11;
			background: rgba(0, 0, 0, 0.4);
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
</style>