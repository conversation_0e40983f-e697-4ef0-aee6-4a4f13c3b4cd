<template>
	<view class="main">
		<view class="video-item" @click.stop="playFn(item.url.url)" v-for="(item,index) in listData" :key="index">
			<view class="video-cont">
				<!-- <image class="video-img" src="https://img.pc841.com/2018/1221/20181221041448763.jpg" mode=""></image> -->
				<video class="video-img" :show-center-play-btn='false' :src="item.url.url" initial-time='0.1' object-fit="fill" :controls='false'></video>
				<view class="video-model">
					<image class="icon_play" src="../../static/images/learn/icon_play.png" mode=""></image>
				</view>
			</view>
			<view class="video-des">
				问题：{{item.name}}
			</view>
		</view>

		<!-- 播放弹窗 -->
		<view :class="isfull ? 'play_video_max':'play_model'" v-if="isPlay" @tap.stop="isPlay=false,autoplay=false">
			<video :class="isfull ? 'play_video_maxs':'play_video'" :autoplay="autoplay" @fullscreenchange="fullscreen" @tap.stop="{}" object-fit="contain" :src="videoUrl" controls></video>
		</view>
	</view>
</template>

<script>
	import {
		learnHelp
	} from "@/api/order.js";
	export default {
		data() {
			return {
				isPlay: false, //是否展示播放弹窗
				autoplay: false, // 是否自动播放
				listData: [],
				videoUrl: '',
				isfull: false
			}
		},
		onHide() {
			this.isPlay = false
			this.autoplay = false
		},
		onShow() {
			this.learnHelpFn()
		},
		// 下拉刷新
		onPullDownRefresh() {
			uni.stopPullDownRefresh();
		},
		// 上拉加载
		onReachBottom() {},
		methods: {

			learnHelpFn() {
				this.$http.get(learnHelp).then(res => {
					this.listData = res.data
				})
			},
			// 播放
			playFn(url) {
				this.isPlay = true
				this.autoplay = true
				this.videoUrl = url
			},
			// 触发全屏播放
			fullscreen(e) {
				this.isfull = !this.isfull
			}
		}
	}
</script>

<style lang="scss" scoped>
	.main {
		width: 100%;
		height: 100%;
		min-height: 100vh;
		padding: 0 24rpx;
		padding-bottom: 27rpx;
		background-color: #F2F2F2;

		.video-item {
			width: 100%;
			height: 205rpx;
			margin-top: 20rpx;
			background: #FFFFFF;
			border-radius: 20rpx;
			padding: 26rpx;
			box-sizing: border-box;
			display: flex;
			align-items: center;


			.video-cont {
				width: 240rpx;
				height: 152rpx;
				margin-right: 26rpx;
				border-radius: 7rpx;
				overflow: hidden;
				position: relative;

				.video-img {
					width: 240rpx;
					height: 152rpx;
				}

				.video-model {
					width: 240rpx;
					height: 152rpx;
					background: rgba(0, 0, 0, 0);
					position: absolute;
					top: 0;
					left: 0;
					z-index: 10;
					display: flex;
					align-items: center;
					justify-content: center;

					.icon_play {
						width: 45rpx;
						height: 45rpx;
					}
				}
			}

			.video-des {
				flex: 1;
				font-size: 27rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #333333;
			}

		}

		.play_model {
			width: 100%;
			height: 100vh;
			position: fixed;
			top: 0;
			left: 0;
			z-index: 11;
			background: rgba(0, 0, 0, 0.4);
			display: flex;
			align-items: center;
			justify-content: center;

			.play_video {
				width: 100%;
				height: 400rpx;
			}

			.play_video_maxs {
				width: 100vh;
				height: 100vw;
			}
		}

		.play_video_max {
			width: 100vh;
			height: 100vw;
			transform: rotate(90);
			position: fixed;
			top: 0;
			left: 0;
			z-index: 11;
			background: rgba(0, 0, 0, 0.4);
			display: flex;
			align-items: center;
			justify-content: center;
		}

	}
</style>