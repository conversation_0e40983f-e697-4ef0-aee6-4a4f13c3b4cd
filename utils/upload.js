/*
 * @Author: yang<PERSON><PERSON>
 * @Date: 2023-10-30 17:28:54
 * @LastEditors: yangzhong
 * @LastEditTime: 2023-12-05 14:14:56
 * @Description:
 */
// import { get } from '@/utils/request'
import {
	http
} from '@/utils/request/service'
import cos from '@/utils/cos'
import {
	uuid
} from 'vue-uuid'

let Bucket, Region, Prefix

export const getCredentials = () => http.get('/api/cos/credentials')

export const getBucket = () => http.get('/api/cos/bucket')

export const uploadFile = async file => {
	if (!Bucket || !Region || !Prefix) {
		const res = await getBucket()
		Bucket = res.data.bucket
		Region = res.data.region
		Prefix = res.data.prefix
	}
	// FilePath: file.path,
	return new Promise((resolve, reject) => {
		const key = uuid.v1()
		cos.uploadFile({
				Bucket,
				Region,
				Key: Prefix + key,
				FilePath: file.url,
				SliceSize: 1024 * 1024 * 10,
				onProgress: progressData => {
					console.log(progressData)
				},
				onFileFinish: (err, data, options) => {
					/* 非必须 */
					console.log(options.Key + '上传' + (err ? '失败' : '完成'))
				}
			},
			(err, data) => {
				if (err) reject(err)
				console.log(data)
				resolve({
					name: file.name,
					key: Prefix + key,
					url: 'https://' + data.Location
				})
			}
		)
	})
}