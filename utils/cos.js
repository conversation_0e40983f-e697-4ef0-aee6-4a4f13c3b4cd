const COS = require("cos-wx-sdk-v5");
import { getCredentials } from "./upload";

const cos = new COS({
  getAuthorization: async (options, callback) => {
    try {
      const credentials = await getCredentials().then((res) => res.data);
      callback({
        TmpSecretId: credentials.tmpSecretId,
        TmpSecretKey: credentials.tmpSecretKey,
        SecurityToken: credentials.token,
        ExpiredTime: credentials.expiredTime,
      });
    } catch (error) {
      console.log(error);
    }
  },
});

export default cos;
