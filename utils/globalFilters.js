/*
 * @Description: 
 * @Autor: shh
 * @Date: 2022-07-28 11:23:25
 * @LastEditors: shh
 * @LastEditTime: 2023-05-17 15:55:49
 */
import { parseTime, getIdname } from '@/utils'
import { dictName, areaDict } from "@/config/dict";

export default {
  // filters

  parseTime(value) {
    return parseTime(value, '{y}-{m}-{d} {h}:{i}:{s}')
  },
  parseTime2(value) {
    return parseTime(value, '{y}-{m}')
  },
  dictName(value, type) {
    let res = dictData.filter(items => items['value'] === type)
    let res1 = res[0].options.filter(items => items['value'] === parseInt(value))
    return res1[0]?.label
  },
  dictLabel(value, type) {
    let res = dictData.filter(items => items['value'] === type)
    let res1 = res[0].options.filter(items => items['value'] === value)
    return res1[0]?.label
  },
  filterName(value, arr) {
    if (value) {
      let res = arr.filter(items => items['value'] === parseInt(value))
      return res[0]?.label
    } else {
      return null
    }
  },
  filterLabel(value, arr) {
    if (value) {
      let res = arr.filter(items => items['value'] === value)
      return res[0]?.label
    } else {
      return null
    }

  },
  formatDate(date) {
    const d = new Date(date)

    const y = d.getFullYear() // 年份

    const m = (d.getMonth() + 1).toString().padStart(2, '0') // 月份

    const r = d.getDate().toString().padStart(2, '0') // 日子

    const hh = d.getHours().toString().padStart(2, '0') // 小时

    const mm = d.getMinutes().toString().padStart(2, '0') // 分钟

    const ss = d.getSeconds().toString().padStart(2, '0') // 秒

    return `${y}-${m}-${r} ${hh}:${mm}:${ss}`// es6 字符串模板

  },
  getIdname,

}
