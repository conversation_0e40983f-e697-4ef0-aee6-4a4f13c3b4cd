/*
 * @Description:
 * @Autor: shh
 * @Date: 2022-07-04 13:41:16
 * @LastEditors: shan<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-10 15:23:02
 */
import * as gm from "sm-crypto";
const SM2 = gm.sm2;
import {
	areaDict
} from "@/config/dict";
/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
	if (arguments.length === 0 || !time) {
		return null;
	}
	const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
	let date;
	if (typeof time === "object") {
		date = time;
	} else {
		if (typeof time === "string") {
			if (/^[0-9]+$/.test(time)) {
				// support "1548221490638"
				time = parseInt(time);
			} else {
				// support safari
				// https://stackoverflow.com/questions/4310953/invalid-date-in-safari
				time = time.replace(new RegExp(/-/gm), "/");
			}
		}

		if (typeof time === "number" && time.toString().length === 10) {
			time = time * 1000;
		}
		date = new Date(time);
	}
	const formatObj = {
		y: date.getFullYear(),
		m: date.getMonth() + 1,
		d: date.getDate(),
		h: date.getHours(),
		i: date.getMinutes(),
		s: date.getSeconds(),
		a: date.getDay(),
	};
	const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
		const value = formatObj[key];
		// Note: getDay() returns 0 on Sunday
		if (key === "a") {
			return ["日", "一", "二", "三", "四", "五", "六"][value];
		}
		return value.toString().padStart(2, "0");
	});
	return time_str;
}

export function filterName(value, arr) {
	if (value) {
		let res = arr.filter((items) => items["value"] === value);
		return res[0]?.label;
	} else {
		return null;
	}
}

/**
 * 1、根据节点id,获取其所有父节点
 * @param {*} list 完整的树结构数组
 * @param {*} id 当前点击的id
 * @param {*} name 需要对比的id 的属性节点
 * @param {*} child 子节点名称
 * @returns
 */
export function getAllParentArr(list, id, name, child) {
	for (let i in list) {
		if (list[i][name] == id) {
			return [list[i][name]];
		}
		if (list[i][child]) {
			let node = getAllParentArr(list[i][child], id, name, child);
			if (!!node) {
				return node.concat(list[i][name]);
			}
		}
	}
}
export function getPid(id) {
	for (let i in areaDict) {
		if (areaDict[i]["value"] == id) {
			return [areaDict[i]["value"]];
		}
		if (areaDict[i]["children"]) {
			let node = getAllParentArr(
				areaDict[i]["children"],
				id,
				"value",
				"children"
			);
			if (!!node) {
				return node.concat(areaDict[i]["value"]).reverse();
			}
		}
	}
}

/**
 * 2、通过某个节点的id，找出他所有的子节点
 * @param {*} list 当前点击节点的数据(不是总的树结构)
 * @param {*} arr 返回的数组  默认为 []
 * @returns
 */
export function getChildIds(list = [], arr = []) {
	for (let item of list) {
		arr.push(item.id);
		if (item.children && item.children.length) {
			this.getChildIds(item.children, arr);
		}
	}
	return arr;
}

/**
 * 3、通过某个节点的id，获取其父级名称（或其他）的完整路径信息
 * @param {*} tree 树结构数组
 * @param {*} value 当前节点的id
 * @returns
 */

export function getItemByIdInTree(tree, value, path = "") {
	for (var i = 0; i < tree.length; i++) {
		let tempPath = path;
		tempPath = `${tempPath ? tempPath + "/" : tempPath}${tree[i].name}`; // 避免出现在最前面的/
		if (tree[i].id == value) {
			return tempPath;
		} else if (tree[i].children) {
			let reuslt = getItemByIdInTree(tree[i].children, value, tempPath);
			if (reuslt) {
				return reuslt;
			}
		}
	}
}

/**
 * 4、通过某个节点的id，获取该节点的完整信息
 * @param {*} tree 树结构数组
 * @param {*} value 当前节点的id
 * @returns
 */
export function getItemInfoByIdInTree(tree, value) {
	for (var i = 0; i < tree.length; i++) {
		if (tree[i].value == value) {
			return tree[i];
		} else if (tree[i].children) {
			let reuslt = getItemInfoByIdInTree(tree[i].children, value);
			if (reuslt) {
				return reuslt;
			}
		}
	}
}
export function getIdname(value) {
	for (var i = 0; i < areaDict.length; i++) {
		if (areaDict[i].value == value) {
			return areaDict[i];
		} else if (areaDict[i].children) {
			let reuslt = getItemInfoByIdInTree(areaDict[i].children, value);
			if (reuslt) {
				let str = "";
				switch (value.split("-")[0]) {
					case "1":
						str = "旌阳区";
						break;
					case "2":
						str = "中江县";
						break;
					case "3":
						str = "罗江县";
						break;
					case "4":
						str = "广汉市";
						break;
					case "5":
						str = "什邡市";
						break;
					case "6":
						str = "绵竹市";
						break;
				}
				return str + "/" + reuslt.label;
			}
		}
	}
}
/**
 * 字符串转日期并比较大小
 * @param {*} 日期1
 * @param {*} 日期2
 * @returns
 */

export function compareDate(DateOne, DateTwo) {
	var OneMonth = DateOne.substring(5, DateOne.lastIndexOf("-"));
	var OneDay = DateOne.substring(DateOne.length, DateOne.lastIndexOf("-") + 1);
	var OneYear = DateOne.substring(0, DateOne.indexOf("-"));
	var TwoMonth = DateTwo.substring(5, DateTwo.lastIndexOf("-"));
	var TwoDay = DateTwo.substring(DateTwo.length, DateTwo.lastIndexOf("-") + 1);
	var TwoYear = DateTwo.substring(0, DateTwo.indexOf("-"));
	if (
		Date.parse(OneMonth + "/" + OneDay + "/" + OneYear) >
		Date.parse(TwoMonth + "/" + TwoDay + "/" + TwoYear)
	) {
		return true;
	} else {
		return false;
	}
}

export function unitPrice(price, area) {
	let p = (price * 1000) / (area * 1000);
	return p * 10000;
}
//密码令牌加密设置
export function encodeFun(data, second) {
	//偏方：在所有加密前的内容加字符“0”，用以解决无法加密空字符的问题
	data = SM2.doEncrypt(data ? "0" + data : "0", second);
	return data;
}
export function treeToArray(tree) {
	if (!tree) {
		return;
	}
	var res = [];
	for (const item of tree) {
		const {
			children,
			...i
		} = item;
		if (children && children.length) {
			res = res.concat(treeToArray(children));
		}
		if (i.value) {
			res.push(i.value);
		}
	}
	return res;
}

/**
 * @description 防抖函数，用于防止函数在短时间内被多次调用。
 * @param {Object} func 需要防抖的函数。
 * @param {Object} wait 防抖时间，单位为毫秒。
 * @param {Object} immediate 是否立即执行，默认为false。
 * @return {Object} 返回一个新的函数，该函数在短时间内只会执行一次。
 */
export function debounce(func, wait, immediate = false) {
	let timeoutId;
	return function() {
		const context = this;
		const args = arguments;
		clearTimeout(timeoutId);

		if (immediate && !timeoutId) {
			func.apply(context, args);
			timeoutId = null;
		} else {
			timeoutId = setTimeout(() => {
				func.apply(context, args);
				timeoutId = null;
			}, wait);
		}
	};
}

/**
 * @description 节流
 * @param {Object} func
 * @param {Object} delay
 */
export function throttle(func, delay) {
	let lastExecutionTime = 0;
	return function() {
		const context = this;
		const args = arguments;
		const now = Date.now();
		if (now - lastExecutionTime >= delay) {
			func.apply(context, args);
			lastExecutionTime = now;
		}
	};
}
export function filterParam(obj) {
	for (var key in obj) {
		if (obj[key] === "" || obj[key] === null) {
			delete obj[key];
		}
	}
	return obj;
}