<template>
	<transition name="fade" v-if="styleLoadingIsOpen">
		<view
			class="mask"
			v-show="isActive"
			:class="{ 'full-screen': isFullScreen }"
			:style="{ backgroundColor }"
		>
			<view
				class="spinner"
				:style="{ transform: `translate(-50%, -${translateY}%)` }"
			>
				<slot>
<!--					<loop v-if="styleLoadingType === 'loop'" :color="themeColor.color" :size="size" />-->
					<shrinkRect v-if="styleLoadingType === 'shrinkRect'" :color="themeColor.color" :size="size" />
<!--					<rotatePlane v-if="styleLoadingType === 'rotatePlane'" :color="themeColor.color" :size="size" />-->
				</slot>
				<view v-if="text.length" :style="{ color: textColor }">
					{{ text }}
				</view>
			</view>
		</view>
	</transition>
</template>

<script>
// ==========在这里选一个您要的其他去掉也行==========
// import loop from './loaders/loop.vue';
// import rotatePlane from './loaders/rotate-plane.vue';
import shrinkRect from './loaders/shrink-rect.vue';
// ==========在这里选一个您要的其他去掉也行==========

export default {
	name: 'rfLoading',
	components: {
		// loop,
		// rotatePlane,
		shrinkRect
	},
	props: {
		active: Boolean,
		translateY: {
			type: Number,
			default: 50
		},
		text: {
			type: String,
			default: ''
		},
		color: {
			type: String,
			default: '#333'
		},
		textColor: {
			type: String,
			default: '#333'
		},
		isFullScreen: {
			type: Boolean,
			default: false
		},
		backgroundColor: {
			type: String,
			default: 'rgba(255, 255, 255, .9)'
		},
		size: {
			type: Number,
			default: 40
		}
	},
	data() {
		return {
			isActive: this.active || false,
			styleLoadingIsOpen: this.$mSettingConfig.styleLoadingIsOpen,
			styleLoadingType: this.$mSettingConfig.styleLoadingType
		};
	},
	watch: {
		active(value) {
			this.isActive = value;
		}
	}
};
</script>

<style scoped>
.mask {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 3000;
	transition: opacity 0.3s linear;
}

.full-screen {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
}

.spinner {
	position: absolute;
	top: 50%;
	left: 50%;
	text-align: center;
}

.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.3s;
}
.fade-enter,
.fade-leave-to {
	opacity: 0;
}
</style>
