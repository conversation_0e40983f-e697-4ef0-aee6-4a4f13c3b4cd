<template>
	<view class="form-container">
		<view class="customer-info" v-if="isInfo">
			<view class="item">
				<view class="title">客户编号</view>
				<view class="text">{{ seqId }}</view>
			</view>
			<view class="item">
				<view class="title">客户名称</view>
				<view class="text">{{ shopName }}</view>
			</view>
		</view>
		<form class="formTable" @submit="formSubmit" @reset="formRest">
			<uni-section class="pd_body" v-for="(item, index) in formColumns" :key="index" :title="item.title" type="line">
				<view v-if="item.valueType === 'machine'" class="machin-content">
					<input class="pd_inputs" type="text" v-model="queryParmas[item.dataIndex]" @input="productInput" placeholder="请输入（如: C7780,V80,7502,C1070）" />
					<uni-icons v-if="queryParmas[item.dataIndex]" type="close" size="17" class="close_icon" color="#999" @click.stop="productClear"></uni-icons>
					<uni-icons :type="showProduct ? 'arrowup' : 'arrowdown'" class="pd_icon" color="#999" size="14"></uni-icons>
					<view class="socrll-check" v-if="showProduct">
						<view class="socrll-popper__arrow"></view>
						<scroll-view class="uni-select__selector-scroll" v-if="productList.length > 0">
							<view class="socrll-list">
								<view class="socrll-item" v-for="(machine, index) in productList" :key="index" @click="sureCheckFn(machine)">{{ machine.brand }}/{{ machine.name }}</view>
							</view>
						</scroll-view>
						<view class="no-socrll-list" v-else>暂无该机型</view>
					</view>
				</view>
				<input
					v-if="item.valueType === 'input'"
					class="pd_input"
					:type="item.inputType || 'text'"
					v-model="queryParmas[item.dataIndex]"
					:disabled="item.disabled"
					:placeholder="item.placeholder || `请输入${item.title}`"
				/>
				<textarea
					v-if="item.valueType === 'textarea'"
					class="pd_input"
					:maxlength="item.maxlength || 140"
					v-model="queryParmas[item.dataIndex]"
					:disabled="item.disabled"
					:placeholder="item.placeholder || `请输入${item.title}`"
				/>
				<uni-datetime-picker v-if="item.valueType === 'datetime'" class="pd_input" v-model="queryParmas[item.dataIndex]" :type="item.inputType" />
				<uni-data-select
					v-if="item.valueType === 'select'"
					class="pd_input"
					:localdata="item.localdata"
					:clear="item.clear || true"
					:multiple="item.multiple || false"
					v-model="queryParmas[item.dataIndex]"
					:placeholder="item.placeholder || `请选择${item.title}`"
				/>
			</uni-section>
			<view class="footer">
				<button class="confirm-btn cancel" type="default" form-type="reset">{{ resetText }}</button>
				<button class="confirm-btn save" form-type="submit">{{ sbumitText }}</button>
			</view>
		</form>
	</view>
</template>

<script>
import { brandModelList } from '@/api/index';
import { filterParam } from '@/utils/index.js';
export default {
	props: {
		formColumns: {
			type: Array,
			default: () => []
		},
		isInfo: {
			type: Boolean,
			default: false
		},
		isMachine: {
			type: Boolean,
			default: false
		},
		resetText: {
			type: String,
			default: '重置'
		},
		sbumitText: {
			type: String,
			default: '查询'
		},
		seqId: {
			type: String,
			default: ''
		},
		shopName: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			queryParmas: {},
			showProduct: '',
			productList: []
		};
	},
	watch: {
		formColumns: {
			handler(val) {
				val.forEach((item) => {
					if (item.default && item.default !== '') {
						this.queryParmas[item.dataIndex] = item.default;
					}
				});
			},
			deep: true,
			immediate: true
		}
	},
	methods: {
		productInput(val) {
			this.productList = [];
			if (!val.detail.value) {
				this.showProduct = false;
				return;
			}
			if (val.detail.value.length < 3) {
				this.showProduct = false;
				uni.showToast({
					title: '最少输入3位字符',
					icon: 'none'
				});
				return;
			}
			const machineDataIndices = this.formColumns.find((item) => item.valueType === 'machine')?.dataIndex;
			this.$http
				.get(brandModelList, {
					name: this.queryParmas[machineDataIndices],
					pageNumber: 1,
					pageSize: 10000
				})
				.then((res) => {
					this.productList = res.data.rows;
				});
			this.showProduct = true;
		},
		productClear() {
			uni.hideKeyboard();
			const machineDataIndices = this.formColumns.find((item) => item.valueType === 'machine')?.dataIndex;
			this.queryParmas[machineDataIndices] = '';
			this.queryParmas.productTreeIdList = [];
			this.showProduct = false;
		},
		// 确认机型
		sureCheckFn(item) {
			const machineDataIndices = this.formColumns.find((item) => item.valueType === 'machine')?.dataIndex;
			this.queryParmas[machineDataIndices] = item.brand + '/' + item.name;
			// this.queryParmas.product = item.brand + '/' + item.name;
			this.queryParmas.productTreeIdList = [item.id];
			this.showProduct = false;
		},
		formSubmit() {
			const params = filterParam(this.queryParmas);
			this.$emit('search', params);
		},
		formRest() {
			this.formColumns.forEach((item) => {
				this.queryParmas[item.dataIndex] = '';
			});
		}
	}
};
</script>

<style lang="scss"></style>
