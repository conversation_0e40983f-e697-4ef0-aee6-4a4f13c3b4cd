<template>
	<view class="pd_body" type="line">
		<input
			class="pd_input"
			type="text"
			:value="inputValue"
			@focus="handleFocusEvent"
			@blur="show = false"
			@input="handleInputEvent"
			disabled
			:placeholder="placeholder"
			@click="show = !show"
		/>
		<uni-icons :type="show ? 'arrowup' : 'arrowdown'" class="pd_icon" color="#999" size="14" @click="show = !show"></uni-icons>

		<uni-icons v-if="inputValue" type="close" size="17" class="close_icon" color="#999" @click.stop="clearClick"></uni-icons>
		<view class="socrll-check" v-if="show">
			<view class="socrll-popper__arrow"></view>
			<scroll-view class="uni-select__selector-scroll" scroll-y="true" v-if="options.length > 0">
				<view class="socrll-list">
					<view class="socrll-item" v-for="(item, index) in options" :key="index" @click="handleItemClick(item)">
						{{ item.label }}
					</view>
				</view>
			</scroll-view>
			<view class="no-socrll-list" v-else>暂无数据</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		inputValue: {
			type: String,
			default: ''
		},
		options: {
			type: Array,
			default: () => {
				return [
					{
						label: 'C7780',
						value: 'C7780'
					},
					{
						label: 'V80',
						value: 'V80'
					},
					{
						label: '7502',
						value: '7502'
					}
				];
			}
		},
		title: {
			type: String,
			default: '请选择'
		},
		placeholder: {
			type: String,
			default: '请选择'
		}
	},
	data() {
		return {
			show: false,
			// inputValue: "",
			timer: null
		};
	},
	methods: {
		handleInputEvent(e) {
			clearTimeout(this.timer);
			this.timer = setTimeout(() => {
				let value = e.detail.value;
				console.log(e);
				this.$emit('input', value);
			}, 500);
		},
		clearClick() {
			// HM修改 收起键盘
			uni.hideKeyboard();
			// this.inputValue = "";
			this.show = false;
			this.$emit('clear');
		},
		handleItemClick(item) {
			console.log(item);
			this.show = false;
			// this.inputValue = item.label;
			this.$emit('confirm', item);
		},
		handleFocusEvent() {
			this.show = true;
			this.$emit('focus');
		}
	}
};
</script>

<style lang="scss" scoped>
.pd_body {
	width: 100%;
	position: relative;
	.pd_icon {
		position: absolute;
		right: 10rpx;
		top: 10rpx;
	}

	.close_icon {
		position: absolute;
		right: 40rpx;
		top: 6rpx;
		z-index: 2;
	}
	.pd_input {
		font-size: 14px !important;
		border: 1px solid #e5e5e5 !important;
		box-sizing: border-box !important;
		border-radius: 4px !important;
		height: 60rpx !important;
		padding: 0 80rpx 0 10rpx !important;
	}

	.socrll-check {
		// width: 100%;
		min-width: 400rpx;
		position: absolute;
		top: 80rpx;
		left: 0;
		z-index: 3;
		padding: 0 22rpx;

		/deep/.uni-select__selector-scroll {
			// width: 100%;
			min-height: 110rpx;
			max-height: 400rpx;
			padding: 4px 22rpx !important;
			box-sizing: border-box;
			background-color: #ffffff;
			border: 1px solid #ebeef5;
			border-radius: 6px;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

			.socrll-list {
				// height: 100%;
				// overflow: hidden;
				overflow-y: scroll;
			}

			.socrll-item {
				display: flex;
				cursor: pointer;
				line-height: 35px;
				font-size: 14px;
				text-align: center;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
			}
		}

		.no-socrll-list {
			height: 45px;
			padding: 4px 22rpx !important;
			box-sizing: border-box;
			background-color: #ffffff;
			border: 1px solid #ebeef5;
			border-radius: 6px;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
			overflow: hidden;
			overflow-y: scroll;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			color: #999;
		}

		.socrll-popper__arrow {
			filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
			position: absolute;
			top: -4px;
			left: 15%;
			margin-right: 3px;
			border-top-width: 0;
			border: 1px solid #ebeef5;
			border-bottom-color: #ffffff;
			border-right-color: #ffffff;
			width: 18rpx;
			height: 18rpx;
			background-color: #ffffff;
			transform: rotate(45deg);
		}
	}
}
</style>
