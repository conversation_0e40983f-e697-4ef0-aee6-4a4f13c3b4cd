<!-- 上传多张图片 调用完成主流程接口后携带bid触发循环上传图片接口-->
<template name="sunui-upimg">
	<view class="sunui-uploader-bd">
		<view class="sunui-uploader-files">
			<block v-for="(item, index) in upload_before_list" :key="index">
				<view class="sunui-uploader-file" :class="[
          item.upload_percent < 100 ? 'sunui-uploader-file-status' : '',
        ]" :style="'width:' + upload_img_wh + 'rpx; height:' + upload_img_wh + 'rpx;'
  ">
					<image class="sunui-uploader-img" :style="'width:' + upload_img_wh + 'rpx; height:' + upload_img_wh + 'rpx;'
            " :src="item.path" mode="aspectFill" @tap="preview(index)" v-if="type === 'image'" />
					<video :id="`myVideo_${index}`" :src="item.path" :data-index="index" @play="videoPlay" @error="videoError"
						controls :enable-play-gesture="true" objectFit="contain" :style="'width:' + upload_img_wh + 'rpx; height:' + upload_img_wh + 'rpx;'
              " v-if="type === 'video'"></video>
					<view class="sunui-img-removeicon right" @tap.stop="remove(index, item)" v-show="upimg_move">x</view>
					<!-- <view
            class="sunui-loader-filecontent"
            v-if="item.upload_percent < 100"
            >{{ item.upload_percent }}%</view
          > -->
				</view>
			</block>
			<view v-show="upload_before_list.length < upload_count" hover-class="sunui-uploader-hover"
				class="sunui-uploader-inputbox" @tap="choose" :style="'width:' + upload_img_wh + 'rpx; height:' + upload_img_wh + 'rpx;'
          ">
				<view :style="'line-height:' + upload_img_wh + 'rpx;'"><text class="iconfont icon-mn_shangchuantupian"
						style="color: #666"></text></view>
			</view>
		</view>
	</view>
</template>

<script>
	import indexConfig from "@/config/index.config";

	export default {
		data() {
			return {
				success: 0,
				formData: {},
				// 选择图片tempFile
				upload_cache: [],
				// 预览数组->支持从服务器拉取
				upload_cache_list: [],
				// 上传数组
				upload_before_list: [],
				// 图片大小
				upload_img_size: [],
				// 超出限制数组
				upload_exceeded_list: [],

				// 最大上传容量(M)
				upload_max: 5,
				// 图片/选择宽高
				upload_img_wh: 162,
				//  type: "image", //上传视频为video
				header: {},
				videoContext: [],
			};
		},
		name: "sunui-upimg",
		props: {
			type: {
				type: String,
				default: "image",
			}, //上传视频为video
			// 上传数量
			upload_count: {
				type: Number,
				default: 10,
			},
			upload_auto: {
				type: Boolean,
				default: false,
			},
			upimg_move: {
				type: Boolean,
				default: true,
			},
			url: {
				type: String,
				default: "",
			},
			filename: {
				type: String,
				default: "imageFile",
			},
		},
		onReady(res) {
			this.$nextTick(() => {
				for (let i = 0; i < 10; i++) {
					this.videoContext[i] = uni.createVideoContext(`myVideo_${i}`);
				}
			});
		},
		created() {},
		mounted() {},
		watch: {
			url(val) {
				this.upload_before_list.map((item) => {
					this.upload_cache_list.push(item.path);
				});
				this.emit();
			},
		},
		methods: {
			videoPlay(e) {
				let index = e.currentTarget.dataset.index;
				this.videoContext[index].requestFullScreen();

				for (let i = 0; i < this.upload_before_list.length; i++) {
					if (i != index) this.videoContext[i].pause();
				}
			},
			videoError(e) {
				uni.showModal({
					content: "网络错误，请稍后重试！",
					showCancel: false,
				});
			},
			uploadFile(paths) {

				this.uploader({
					url: this.url,
					path: paths,
					name: "file",
					header: this.header,
					formData: this.formData,
				});
				// this.emit("change", paths);
				// const promises = paths.map((path) => {
				//   return this.promisify(this.uploader)({
				//     url: this.url,
				//     path: path,
				//     name: "file",
				//     header: this.header,
				//     formData: this.formData,
				//   });
				// });
				// uni.showLoading({
				//   title: `正在上传...`,
				// });
				// Promise.all(promises)
				//   .then((data) => {
				//     // uni.hideLoading();
				//     this.upload_cache_list.push(...data);
				//     this.emit();
				//   })
				//   .catch((res) => {
				//     // uni.hideLoading();
				//   });
			},
			choose() {
				switch (this.type) {
					case "video":
						uni.chooseVideo({
							count: this.upload_count - this.upload_before_list.length,
							compressed: true,
							sourceType: ["album", "camera"],
							success: async (res) => {
								res.path = res.tempFilePath;
								res.upload_percent = 0;

								if (Math.ceil(res.size / 1024) < 20 * 1024) {
									await this.upload_img_size.push(Math.ceil(res.size / 1024));
									await this.upload_before_list.push(res);
								} else {
									this.upload_exceeded_list.push(1);

									uni.showModal({
										title: "提示",
										content: `视频超出限制20MB,已忽略`,
										showCancel: false,
										confirmText: "确认",
										success(res) {},
									});
								}
								if (this.upload_count == 1) {
									this.upload_cache = [];
								}
								this.upload_cache.push(res.tempFilePath);
								this.upload(this.upload_auto);
							},
							fail: (err) => {
								console.log(err);
							},
						});
						break;
					case "image":
						uni.chooseImage({
							count: this.upload_count - this.upload_before_list.length,
							sizeType: ["compressed", "original"],
							sourceType: ["album", "camera"],
							success: async (res) => {
								console.log(res);
								for (let i = 0, len = res.tempFiles.length; i < len; i++) {
									res.tempFiles[i].upload_percent = 100; //先默认是100，之前是0

									if (
										Math.ceil(res.tempFiles[i].size / 1024) <
										this.upload_max * 1024
									) {
										await this.upload_img_size.push(
											Math.ceil(res.tempFiles[i].size / 1024)
										);
										await this.upload_before_list.push(res.tempFiles[i]);
										// TODO v3.1增加图片格式限制
									} else {
										res.tempFilePaths.splice(i, 1);
										this.upload_exceeded_list.push(i === 0 ? 1 : i + 1);
										uni.showModal({
											title: "提示",
											content: `第${[...new Set(this.upload_exceeded_list)].join(
                      ","
                    )}张图片超出限制${this.upload_max}MB,已过滤`,
											showCancel: false,
											confirmText: "确认",
											success(res) {
												if (res.confirm) {
													this.upload_img_size.splice(i--, 1);
													this.upload_exceeded_list.splice(i--, 1);
												}
											},
										});
									}
								}
								// this.upload_cache = await res.tempFilePaths;
								if (this.upload_count == 1) {
									this.upload_cache = [];
									this.upload_cache = await res.tempFilePaths;
								} else {
									this.upload_cache = this.upload_cache.concat(res.tempFilePaths);
								}

								this.upload(this.upload_auto);
								console.log(this.upload_cache.length);
							},
							fail: (err) => {
								console.log(err);
							},
						});
						break;
					default:
						break;
				}
			},

			async upload(upload_auto) {
				upload_auto
					?
					await this.uploadFile(this.upload_cache) :
					console.warn(`传输参数:this.$refs.xx.upload(true)才可上传,默认false`);
			},
			preview(idx) {
				uni.previewImage({
					current: idx,
					urls: this.upload_cache_list,
				});
			},
			remove(idx, item) {
				this.upload_before_list.splice(idx, 1);
				this.upload_cache_list.splice(idx, 1);
				this.emit();
				if (this.upload_cache[idx].id) {
					this.$emit('delImg', this.upload_cache[idx])
				}
				this.upload_cache.splice(idx, 1);

			},
			emit() {
				this.$emit("change", this.upload_cache_list);
				uni.hideLoading();
			},
			promisify(api) {
				return (options, ...params) => {
					return new Promise((resolve, reject) => {
						api(
							Object.assign({}, options, {
								success: resolve,
								fail: reject,
							}),
							...params
						);
					});
				};
			},
			uploader(options) {
				uni.showLoading();

				var url = options.url,
					token = uni.getStorageSync("accessToken"),
					path = options.path,
					formData = options.formData;

				console.log(formData);

				let imgs = []
				this.upload_cache.map((el) => {
					if (!el.url) {
						imgs.push(el)
					}
				})
				if (imgs.length == 0) {
					this.$emit("ok");
					uni.hideLoading();
					return
				}
				this.upload_cache.forEach((ele) => {
					if (!ele.url) {
						uni.uploadFile({
							url: indexConfig.baseUrl + this.url, //仅为示例，非真实的接口地址
							filePath: ele,
							header: {
								'X-Auth-Token': token,
							},
							fileType: "image",
							name: this.filename,
							formData: formData,
							success: (res) => {
								if (JSON.parse(res.data).success) {
									this.success++;
									if (this.success == imgs.length) {
										console.log("okokokokkokokok");
										this.$emit("ok");
										uni.hideLoading();
									}
								}
							},
							fail: (err) => {
								console.log(err);
							},
						});

					}
				});

				// const uploadTask = uni.uploadFile({
				//   url: indexConfig.baseUrl + url,
				//   filePath: path,
				//   name: "images",
				//   header: {
				//     X-Auth-Token: token,
				//   },
				//   formData: formData,
				//   success(res) {
				//     var data = res.data;
				//     try {
				//       //Tip : 切记->主要修改这里图片的返回值为真实返回路径!!! 详情见示例
				//       data = JSON.parse(res.data).data;
				//     } catch (e) {
				//       throw e;
				//     }
				//     if (res.statusCode === 200) {
				//       if (success) {
				//         success(data);
				//       }
				//     } else {
				//       fail(data);

				//       if (res.statusCode === 401) {
				//         this.$tui.toast("未登录或登录状态过期", 2000);
				//         let _this = this;

				//         setTimeout(() => {
				//           _this.$utils.goAuth();
				//         }, 2000);
				//       } else if (data.statusCode === 403) {
				//         this.$tui.toast(item.message, 2000);
				//       }
				//     }
				//   },
				//   fail(res) {
				//     fail(res);
				//   },
				// });
				// let _this = this;
				// uploadTask.onProgressUpdate(async (res) => {
				//   console.log("上传图片返回的结果：" + JSON.stringify(res));
				//   let a = this.upload_before_list;
				//   this.upload_before_list[
				//     this.upload_before_list.length - 1
				//   ].upload_percent = await res.progress;
				//   let b = this.upload_before_list;
				//   _this.$forceUpdate();
				// });
			},
		},
	};
</script>

<style lang="scss">
	@font-face {
		font-family: "iconfont";
		src: url("//at.alicdn.com/iconfont.eot?t=1574391686418");
		/* IE9 */
		src: url("//at.alicdn.com/iconfont.eot?t=1574391686418#iefix") format("embedded-opentype"),
			/* IE6-IE8 */
			url("data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAAMkAAsAAAAAB2QAAALYAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCCcAqCYIJEATYCJAMICwYABCAFhG0HPRt3BhEVlCNkH4dxmzUXNsJHc1SNfR9KTkCtiXv/l+QDBQSFRBJdKoEsg60HUgCsOpWVnWxNx3BvVITqkj3fepbtzM/OfDo4D86iFEIiJAeX02+Bh/O84TLmsrEnYBxQoHtgm6xACoxTkN0zFsgEdQynCShpq7cwbsK0eTKROSkgbNu8cbUspRFrkoNMkC9ZGYWjcrJkX/IIR/zPhz/6hIxELmWmzdowfp1RvxdbYWm1VrUMCO54JvDrSNEbkTCv1DJDGvp6S5VUX9SRdSUHfi+u1cBZ7R+PQMgzEyugNcU5J67DO9VfJiCigD042iuNQqXSunGRfvrWV6/mvX49/+3bhW/eLHr4puOFtxMfvO5w9tX8yv7rIbf3Rrl84Mbe66XSzWet46nn/etMuALua5LqNZUqpKdfDKjsv2qef+yambJsTWM2zDtKIQ0pS7msvSTUpn1tNyts2xZmWUyw3LI4bPisSZNyOUc2y4/scfZs3QZ1UcgqUWtkVednsvnVs7NOHzmqglXIBnqU7+/M9Hp3y3L2RLWYA9uhlat61/LGGwVqt9Nvafv/8R2fmg/pu7LesH9ZOYL3/6e3P6Z2O0rbIztra+Dtc1u2RY1vapOocEtDiT0Kd1VUUkIN42joS19Fk1s1BVmKy0OioA2kMp1REdcbcsr6QV5mJJT0MnF9mbQRchZiET29CAT1fSBR1y1I1fdFRdwPcpr6Q179cIaSBaHRjmVdgxFjCSvGFuonmGYcpK1nESRfUC1dRUm+T3ggeeOEOIiywRwHpDHm+FUlzBIkjT1k5DzsuhEmGi02HGjmKQ1DWfaioBn7gzAWQRWGWqD2BIzGaCDRm4nc+y+QsuhUqKaqyviAiGcGB7FA1AKVS4ZWVddyibdSEoxJQCKjHsjIMNTpjMBUPsxCDRbQPTyTVGh1k20lwfyy/un2QYmpTII1I9Vo+1B4XQ2q0QvwvExGfTgA") format("woff2"),
			url("//at.alicdn.com/iconfont.woff?t=1574391686418") format("woff"),
			url("//at.alicdn.com/iconfont.ttf?t=1574391686418") format("truetype"),
			/* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
			url("//at.alicdn.com/iconfont.svg?t=1574391686418#iconfont") format("svg");
		/* iOS 4.1- */
	}

	.iconfont {
		font-family: "iconfont" !important;
		font-size: 16px;
		font-style: normal;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}

	.icon-mn_shangchuantupian:before {
		content: "\e559";
	}

	.icon-mn_shangchuantupian {
		font-size: 3em;
	}

	.sunui-uploader-img {
		display: block;
	}

	.sunui-uploader-input {
		position: absolute;
		z-index: 1;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		opacity: 0;
	}

	.sunui-uploader-inputbox {
		position: relative;
		margin-bottom: 16rpx;
		box-sizing: border-box;
		background-color: #ededed;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		justify-content: center;
	}

	.sunui-img-removeicon {
		position: absolute;
		color: #fff;
		width: 40upx;
		height: 40upx;
		line-height: 40upx;
		z-index: 2;
		text-align: center;
		background-color: #e54d42;

		&.right {
			top: 0;
			right: 0;
		}
	}

	.sunui-uploader-file {
		position: relative;
		margin-right: 16rpx;
		margin-bottom: 16rpx;
	}

	// .sunui-uploader-file-status:before {
	//   content: " ";
	//   position: absolute;
	//   top: 0;
	//   right: 0;
	//   bottom: 0;
	//   left: 0;
	//   background-color: rgba(0, 0, 0, 0.5);
	// }

	.sunui-loader-filecontent {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		color: #fff;
		z-index: 9;
	}

	.sunui-uploader-bd {
		padding: 0;
		margin: 0;
	}

	.sunui-uploader-files {
		display: flex;
		flex-wrap: wrap;
	}

	.sunui-uploader-inputbox {
		&>view {
			text-align: center;
		}
	}

	// .sunui-uploader-file-status:after {
	//   content: " ";
	//   position: absolute;
	//   top: 0;
	//   right: 0;
	//   bottom: 0;
	//   left: 0;
	//   background-color: rgba(0, 0, 0, 0.5);
	// }

	.sunui-uploader-hover {
		box-shadow: 0 0 0 #e5e5e5;
		background: #e5e5e5;
	}
</style>