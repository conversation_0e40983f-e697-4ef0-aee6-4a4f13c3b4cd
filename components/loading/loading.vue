<template>
	<view class="loading-wrap" v-if="status">
		<image
			class="load-icon"
			src="data:image/png;base64,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"
		></image>
		<text class="load-text">{{ text }}</text>
	</view>
</template>

<script>
export default {
	name: 'loading',
	props: {
		status: {
			type: Boolean,
			default: true
		},
		text: {
			type: String,
			default: '加载中…'
		}
	}
};
</script>

<style>
.loading-wrap {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	height: 90rpx;
	padding-top: 10rpx;
}
.load-icon {
	display: block;
	width: 45rpx;
	height: 45rpx;
	margin-right: 12rpx;
	-webkit-animation: load 1s steps(12) infinite;
	animation: load 1s steps(12) infinite;
}
.load-text {
	font-size: 28rpx;
	color: #848c98;
}

@-webkit-keyframes load {
	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	to {
		-webkit-transform: rotate(1turn);
		transform: rotate(1turn);
	}
}

@keyframes load {
	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	to {
		-webkit-transform: rotate(1turn);
		transform: rotate(1turn);
	}
}
</style>
