<template>
	<view class="upload">
		<uni-file-picker
			:value="fileLists"
			:file-mediatype="fileMediatype"
			mode="grid"
			:limit="limit"
			:title="`最多选择${limit}个文件`"
			:file-extname="fileExtname"
			:image-styles="imageStyles"
			@select="select"
			@delete="deleteFile"
			:auto-upload="false"
			:sourceType="sourceType"
		>
			<!-- <slot></slot> -->
		</uni-file-picker>
		<!-- <view class="btn" @click="handleSubmit">提交</view> -->
	</view>
</template>

<script>
import { uploadFile } from '@/utils/upload';
export default {
	name: 'imgUpload',
	props: {
		fileMediatype: {
			type: String,
			default: 'image'
		},
		limit: {
			type: Number,
			default: 3
		},
		fileExtname: {
			type: String,
			default: 'png,jpg'
		},
		sourceType: {
			type: Array,
			default: () => ['album', 'camera']
		},
		fileLists: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			imgList: [],
			imageStyles: {
				width: 100,
				height: 100,
				border: {
					color: '#f2f2f2',
					width: 2,
					style: 'solid',
					radius: '2px'
				}
			}
		};
	},
	mounted() {
		this.imgList = this.fileLists;
	},
	methods: {
		// 获取上传状态
		select(e) {
			console.log('选择文件：', e);

			// 解决file对象取值问题
			// h5上传-需要文件file对象
			// const tempFilePaths = e.tempFiles[0].file;
			// 微信小程序上传-需要微信临时提供临时地址

			e.tempFiles.map(async (item) => {
				// this.fileLists.push(item.file);
				let res = await uploadFile(item.file);
				console.log(res);
				if (res.url) {
					this.imgList.push({
						url: res.url,
						key: res.key,
						extname: 'png'
					});
					this.$emit('submit', this.imgList);
				}
			});
		},
		deleteFile(e) {
			console.log('删除文件', e);
			let key = e.tempFile.key;
			this.imgList = this.imgList.filter((item) => item.key !== key);
			this.$emit('submit', this.imgList);
		},

		// 提交
		handleSubmit() {
			console.log('提交');
		}
	}
};
</script>

<style lang="scss" scoped>
.upload {
	width: 100%;

	.btn {
		width: 100%;
		background: red;
		color: #fff;
		padding: 10px 0;
		text-align: center;
		margin-top: 10px;
	}
}
</style>
