<template>
  <view
    class="cu-modal bottom-modal"
    :class="{ show: isShow }"
    @tap.prevent="hide"
  >
    <view class="cu-dialog" @tap.stop="isShow = true">
      <view class="goods-data">
        <view class="thumb">
          <image
            @tap="
              (isShowImage = true),
                (bigImage = currentSku
                  ? currentSku.skuPicUrl[0].url
                  : tempSku.picsUrl.url)
            "
            :src="
              currentSku ? currentSku.skuPicUrl[0].url : tempSku.picsUrl.url
            "
            mode=""
          ></image>
        </view>
        <view class="item">
          <view class="title">
            <text></text>
          </view>
          <view class="price">
            <text class="min">￥</text>
            <text class="max">{{
              currentSku ? currentSku.saleUnitPrice : tempSku.saleUnitPrice
            }}</text>
            <text class="min" v-if="!currentSku">起</text>
          </view>
          <view class="inventory">
            <text
              >库存：{{
                currentSku
                  ? currentSku.availableNum
                  : tempSku.availableNum.join("-")
              }}</text
            >
          </view>
        </view>
      </view>
      <view class="attr-size">
        <view class="attr-list" v-for="(value, key) in allSaleAttr" :key="key">
          <view class="title">
            <text>{{ key }}</text>
          </view>
          <view class="size-list">
            <div
              class="list"
              v-for="(val, idx) in value"
              :class="{
                action: chooseAttr[key] === val,
                'attr-disable':
                  canUseAttr[key] && canUseAttr[key].indexOf(val) === -1,
              }"
              @tap="onAttrSize(key, val)"
              :key="idx"
            >
              <text>{{ val }}</text>
            </div>
          </view>
        </view>
        <view class="attr-number" @click="onStop">
          <view class="tit">数量</view>
          <view class="number">
            <text class="add" @click="goodsNumChange(Number(buyNum) - 1)">
              <text
                class="iconfont icon-subtract"
                :class="{ 'icon-disable': currentSku && buyNum <= 1 }"
              ></text>
            </text>
            <input
              v-model="buyNum"
              type="number"
              maxlength="8"
              @change="(e) => goodsNumChange(e.detail.value)"
            />
            <text class="add" @click="goodsNumChange(buyNum + 1)">
              <text
                class="iconfont icon-add"
                :class="{
                  'icon-disable':
                    currentSku && buyNum >= currentSku.availableNum,
                }"
              ></text>
            </text>
          </view>
        </view>
      </view>
      <view class="attr-btn" v-if="currentSku && currentSku.availableNum === 0">
        <view class="confirm disable">暂无库存</view>
      </view>
      <view class="attr-btn" v-else>
        <view class="add-cart" v-if="BuyType === 1" @click="onConfirm(2)"
          >加入购物车</view
        >
        <view class="add-buy" v-if="BuyType === 1" @click="onConfirm(3)"
          >立即购买</view
        >
        <view
          class="confirm"
          v-if="BuyType === 2 || BuyType === 3"
          @click="onConfirm(BuyType)"
          >确定</view
        >
      </view>
    </view>
    <!-- 规格图放大 -->
    <view class="bigImage" @tap.stop="isShowImage = false" v-if="isShowImage">
      <movable-area scale-area>
        <movable-view
          direction="all"
          @scale="onScale"
          scale="true"
          scale-min="1"
          scale-max="4"
          :scale-value="scale"
        >
          <image :src="bigImage" mode="aspectFill"></image>
        </movable-view>
      </movable-area>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isShow: false,
      bigImage: "",
      isShowImage: false,
      AttrIndex: 0,
      SizeIndex: 0,
      AttrSizeList: [
        {
          index: 0,
          attr: "颜色",
          SizeList: [
            {
              index: 0,
              size: "白色",
            },
            {
              index: 1,
              size: "黑色",
            },
            {
              index: 2,
              size: "粉丝",
            },
            {
              index: 3,
              size: "灰色",
            },
          ],
        },
        {
          index: 0,
          attr: "尺码",
          SizeList: [
            {
              index: 0,
              size: "M尺码",
            },
            {
              index: 1,
              size: "L尺码",
            },
            {
              index: 2,
              size: "XL尺码",
            },
            {
              index: 3,
              size: "XXL尺码",
            },
          ],
        },
      ],
      chooseAttr: {},
      buyNum: 1,
      // 购买类型
      BuyType: 0,
      currentSku: {},
      handleCanChooseSku: {},
      canUseAttr: {},
      canUseMap: {},
    };
  },
  props: {
    allSaleAttr: {
      type: Object,
      default: () => ({}),
    },
    sku: {
      type: Array,
      default: () => [],
    },
    tempSku: {
      type: Object,
      default: () => ({}),
    },
    attrMap: {
      type: Object,
      default: () => ({}),
    },
    skuMap: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    /**
     * 显示
     * @param {Number} type 1 点击选择 2 加入购物 3 立即购买
     */
    async show(type) {
      let verify = await this.$http.post(this.$mHelper.verifyAccessToken);
      if (verify.data === 2) {
        this.$mHelper.toast("网络异常，请稍后再试...");
        this.$mHelper.relogin();
        return false;
      }
      if (!uni.getStorageSync("customerId")) {
        this.$mRouter.push({
          route: "/pages/user/login",
        });
        return;
      }
      const userInfo = uni.getStorageSync("userInfo");
      if (userInfo && !userInfo.fullAddress) {
        uni.showToast({
          title: "请先填写收货地址",
          icon: "none",
          duration: 2000,
        });
        return;
      }
      this.BuyType = type;
      this.isShow = true;
      if (JSON.stringify(this.chooseAttr) === "{}") {
        this.currentSku = this.sku[0];
        this.currentSku.saleAttrVals.map((item) => {
          this.chooseAttr[item.name] = item.val;
        });
        this.chooseAttr = {
          ...this.chooseAttr,
        };
        this.checkAttr();
        this.handleChoose();
      }
    },
    hide() {
      this.isShow = false;
      if (!this.currentSku) {
        this.currentSku = this.sku[0];
        this.currentSku.saleAttrVals.map((item) => {
          this.chooseAttr[item.name] = item.val;
        });
        this.chooseAttr = {
          ...this.chooseAttr,
        };
        this.checkAttr();
        this.handleChoose();
      }
    },
    onStop() {},
    /**
     * 属性选择点击
     */
    onAttrSize(key, val) {
      if (!this.canUseAttr[key] || !this.canUseAttr[key].includes(val)) return;
      this.chooseAttr[key] = this.chooseAttr[key] === val ? "" : val;
      Object.keys(this.chooseAttr).forEach((key) => {
        if (this.chooseAttr[key] === "") delete this.chooseAttr[key];
      });
      this.chooseAttr = {
        ...this.chooseAttr,
      };
      const temp = this.handleFindSku(this.chooseAttr);
      this.currentSku = temp
        ? {
            ...temp,
          }
        : null;
      if (this.currentSku && this.currentSku.availableNum === 0) {
        this.buyNum = 0;
      } else {
        this.buyNum = 1;
      }
      this.handleChoose();
      if (Object.keys(this.chooseAttr).length === 0) {
        this.canUseAttr = {
          ...this.allSaleAttr,
        };
      } else {
        this.checkAttr();
      }
    },
    /**
     * 确认点击
     */
    async onConfirm(type) {
      let verify = await this.$http.post(this.$mHelper.verifyAccessToken);
      if (verify.data === 2) {
        this.$mHelper.toast("网络异常，请稍后再试...");
        this.$mHelper.relogin();
        return false;
      }
      if (!this.currentSku) {
        uni.showToast({
          title: "请选择商品",
          icon: "error",
        });
        return;
      }
      switch (type) {
        case 1:
          break;
        case 2:
          this.$emit("addCart");
          break;
        case 3:
          this.$emit("buyNow");
          break;

        default:
          break;
      }
    },
    // 通过属性找sku
    handleFindSku(attr) {
      return this.sku.find((item) => {
        let flag = true;
        item.saleAttrVals.map((val) => {
          if (val.val !== attr[val.name]) {
            flag = false;
          }
        });
        return flag;
      });
    },
    handleChoose() {
      this.$emit("choose", {
        sku: this.currentSku,
        attr: Object.values(this.chooseAttr).join("/"),
        buyNum: this.buyNum,
      });
    },
    goodsNumChange(vals) {
      const screeningStr = /[^\d]/g;
      if (!screeningStr.test(Number(vals))) {
        let val = Number(vals);
        if (!this.currentSku) return;
        if (val <= 0) val = 1;
        if (val > this.currentSku.availableNum)
          val = this.currentSku.availableNum;
        this.buyNum = val;
        this.handleChoose();
      }
    },
    checkAttr() {
      const selectKeys = Object.keys(this.chooseAttr);
      const result = {};
      // 单属性查询
      selectKeys.forEach((key) => {
        this.skuMap.forEach((item) => {
          if (item % this.attrMap[key + "@" + this.chooseAttr[key]] === 0) {
            result[key] ? result[key].push(item) : (result[key] = [item]);
          }
        });
      });
      const res = {};
      Object.keys(result).map((key) => {
        res[key] = this.parseSkuPrime(result[key]);
        res[key][key] = [
          ...new Set([...res[key][key], ...this.allSaleAttr[key]]),
        ];
      });
      if (selectKeys.length <= 1) {
        this.canUseAttr = this.filterAttr(res);
        return;
      }
      // 当选中多条属性时
      const composeAttr = this.getPowerSet(selectKeys)
        .filter((item) => item.length > 1)
        .map((item) => {
          let targetKey = 1;
          let keys = "";
          item.forEach((key) => {
            targetKey *= this.attrMap[key + "@" + this.chooseAttr[key]];
            keys += keys ? "@" + key : key;
          });
          return {
            key: keys,
            value: targetKey,
          };
        });
      const composeResult = {};
      this.skuMap.forEach((item) => {
        composeAttr.forEach((k) => {
          if (item % k.value === 0) {
            composeResult[k.key]
              ? composeResult[k.key].push(item)
              : (composeResult[k.key] = [item]);
          }
        });
      });
      Object.keys(composeResult).map((key) => {
        res[key] = this.parseSkuPrime(composeResult[key]);
      });
      this.canUseAttr = this.filterAttr(res);
    },
    parseSkuPrime(primeList) {
      const result = [];
      primeList.forEach((item) => {
        for (let index = 0; index < this.sku.length; index++) {
          const target = this.sku[index].saleAttrVals;
          let num = 1;
          target.forEach((item) => {
            num *= this.attrMap[item.name + "@" + item.val];
          });
          if (num === item) result.push(target);
        }
      });
      const res = {};
      result.forEach((item) => {
        item.forEach((i) => {
          res[i.name]
            ? !res[i.name].includes(i.val) && res[i.name].push(i.val)
            : (res[i.name] = [i.val]);
        });
      });
      return res;
    },
    getPowerSet(set) {
      var copy = function (powerSet) {
        const newPowerSet = [];
        for (const set of powerSet) {
          newPowerSet.push([...set]);
        }

        return newPowerSet;
      };
      // 幂集对象，
      // 初始化时，默认有1个空集（空集是任何集合的子集、任何非空集合的真子集）
      let powerSet = [[]];

      for (const el of set) {
        // 复制已有的集合
        const newPowerSet = copy(powerSet);
        // 并将当前元素添加到被复制的各个集合当中
        for (const item of newPowerSet) {
          item.push(el);
        }
        // 更新幂集（原幂集元素+新幂集元素）
        powerSet = [...powerSet, ...newPowerSet];
      }

      return powerSet;
    },
    filterAttr(canUseAttr) {
      // 只过滤无关key的信息，这是重点
      const keys = Object.keys(canUseAttr);
      let result = {
        ...this.allSaleAttr,
      };
      if (Object.keys(this.chooseAttr).length <= 1) {
        result = canUseAttr[Object.keys(this.chooseAttr)[0]];
      } else {
        keys.forEach((key) => {
          const target = canUseAttr[key];
          for (const childKey in target) {
            if (key.indexOf(childKey) !== -1) continue;
            result[childKey] = this.intersection(
              result[childKey],
              target[childKey]
            );
          }
        });
      }
      return result;
    },
    intersection(arr1, arr2) {
      return arr1.filter((item) => arr2.includes(item));
    },
  },
};
</script>

<style scoped lang="scss">
.page-total {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  // background: rgba(0,0,0,0.3);
  overflow-x: hidden;
  overflow-y: auto;
}

/* 属性规格 */
.cu-dialog {
  width: 100%;
  height: 70%;
  border-radius: 30rpx 30rpx 0 0 !important;
  overflow: hidden;
  background-color: #ffffff;

  // 商品数据
  .goods-data {
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 4%;
    height: 200rpx;

    .thumb {
      display: flex;
      width: 30%;
      height: 160rpx;

      image {
        width: 160rpx;
        height: 160rpx;
        border-radius: 10rpx;
      }
    }

    .item {
      width: 70%;
      height: 160rpx;

      .title {
        display: flex;
        align-items: center;
        width: 100%;
        height: 60rpx;

        text {
          font-size: 26rpx;
          color: #222222;
        }
      }

      .price {
        display: flex;
        align-items: center;
        width: 100%;
        height: 60rpx;

        text {
          color: #fe3b0f;
          font-weight: bold;
        }

        .min {
          font-size: 26rpx;
        }

        .max {
          font-size: 34rpx;
        }
      }

      .inventory {
        display: flex;
        align-items: center;
        width: 100%;
        height: 40rpx;

        text {
          font-size: 24rpx;
          color: #c0c0c0;
        }
      }
    }
  }

  // 属性规格
  .attr-size {
    padding: 0 4%;
    height: 80%;
    padding-bottom: 100rpx;
    overflow-y: auto;
    overflow-x: hidden;

    .attr-list {
      width: 100%;

      .title {
        display: flex;
        align-items: center;
        width: 100%;
        height: 80rpx;

        text {
          font-size: 26rpx;
          color: #222222;
        }
      }

      .size-list {
        display: flex;
        flex-wrap: wrap;
        width: 100%;

        .list {
          min-width: 120rpx;
          display: flex;
          justify-content: center;
          padding: 10rpx 30rpx;
          background-color: #eeeeee;
          border-radius: 100rpx;
          margin-right: 20rpx;
          box-sizing: border-box;
          border: 2rpx solid transparent;

          text {
            font-size: 24rpx;
            color: #222222;
          }
        }

        .action {
          background-color: rgba(233, 59, 61, 0.3);
          border: 2rpx solid #fe3b0f;
          box-sizing: border-box;

          text {
            color: #fe3b0f;
          }
        }
      }
    }

    .attr-number {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 100rpx;

      .tit {
        font-size: 26rpx;
        color: #222222;
      }

      .number {
        display: flex;
        align-items: center;

        .add {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 60rpx;
          height: 40rpx;
        }

        text {
          font-size: 26rpx;
          color: #222222;
        }

        input {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 80rpx;
          height: 40rpx;
          font-size: 24rpx;
          color: #222222;
          background-color: #eeeeee;
          margin: 0 10rpx;
          border-radius: 6rpx;
        }
      }
    }
  }

  .attr-btn {
    position: absolute;
    left: 0;
    bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100rpx;
    background-color: #ffffff;
    padding: 0 4%;

    .add-cart {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48%;
      height: 70rpx;
      background-color: #fe3b0f;
      color: #ffffff;
      font-size: 28rpx;
      border-radius: 70rpx;
    }

    .add-buy {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48%;
      height: 70rpx;
      background-color: #f0ad4e;
      color: #ffffff;
      font-size: 28rpx;
      border-radius: 70rpx;
    }

    .confirm {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 70rpx;
      background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
      color: #ffffff;
      font-size: 28rpx;
      border-radius: 70rpx;
    }

    .disable {
      opacity: 0.7;
    }
  }
}

.icon-subtract {
  position: relative;
  margin: 0 20rpx;

  &::after {
    content: "";
    display: block;
    width: 25rpx;
    height: 5rpx;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba($color: #000, $alpha: 0.8);
  }
}

.icon-add {
  position: relative;
  margin: 0 20rpx;

  &::after {
    content: "";
    display: block;
    width: 25rpx;
    height: 5rpx;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba($color: #000, $alpha: 0.8);
  }

  &::before {
    content: "";
    display: block;
    width: 5rpx;
    height: 25rpx;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba($color: #000, $alpha: 0.8);
  }
}

.icon-disable {
  &::after,
  &::before {
    background-color: #ccc;
  }
}

.attr-disable {
  background-color: rgba($color: #ccc, $alpha: 0.1) !important;

  text {
    color: #ccc !important;
  }
}

.bigImage {
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
}

movable-view {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

movable-area {
  height: 100%;
  width: 100%;
  position: fixed;
  overflow: hidden;

  .screen-swiper {
    width: 100%;
  }
}

movable-view image {
  width: 100%;
}

.swiper-items {
  // display: flex;
  // align-items: center;
  // justify-content: center;
}
</style>
