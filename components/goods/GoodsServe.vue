<template>
	<view class="page-total" v-show="isPage" @click="hide">
		<view class="dialog" :class="{ 'show': isShow }">
			<view class="serve-list">
				<view class="list">
					<view class="title">
						<text>及时配送</text>
					</view>
					<view class="content">
						<text>下单后最快将于30分钟内收到商品，如遇不可抗因素可能会导致延迟。</text>
					</view>
				</view>
				<view class="list">
					<view class="title">
						<text>瞬间退款保证</text>
					</view>
					<view class="content">
						<text>有任何不满意，可在App享受7天内瞬间退款服务门店购物亦可在手机App操作售后。</text>
					</view>
				</view>
				<view class="list">
					<view class="title">
						<text>真正的安全放心</text>
					</view>
					<view class="content">
						<text>八大环节重重检验筛选，为安全把关严格执行“四度一味一安全”食品分级标准</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			isShow: false,
			isPage: false,
		};
	},
	methods: {
		show() {
			this.isPage = true;
			setTimeout(() => {
				this.isShow = true;
			}, 200)
		},
		hide() {
			this.isShow = false;
			setTimeout(() => {
				this.isPage = false;
			}, 200)
		}
	}
}
</script>

<style scoped lang="scss">
.page-total {
	position: fixed;
	left: 0;
	top: 0;
	z-index: 1000;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.3);
	overflow-x: hidden;
	overflow-y: auto;
}

.dialog {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 600rpx;
	background-color: #FFFFFF;
	border-radius: 20rpx 20rpx 0 0;
	margin-bottom: -1000rpx;
	transition: all 0.3s ease-in-out;

	.serve-list {
		padding: 0 4%;

		.list {
			width: 100%;

			.title {
				display: flex;
				align-items: center;
				width: 100%;
				height: 80rpx;

				text {
					font-size: 26rpx;
					color: #212121;
					font-weight: bold;
				}
			}

			.title:before {
				display: inline-block;
				content: "";
				width: 14rpx;
				height: 14rpx;
				background-color: #212121;
				border-radius: 100%;
				margin-right: 20rpx;
			}

			.content {
				display: flex;
				padding: 10rpx 0;

				text {
					font-size: 24rpx;
					color: #555555;
				}
			}
		}
	}
}

.show {
	margin-bottom: 0;
}</style>
