<template>
  <view>
    <view
      v-if="isStatusBar"
      class="status-bar"
      :style="{ height: statusBarHeight + 'rpx' }"
    >
      <!-- 状态栏 -->
    </view>
    <view
      v-if="isFixed && isOccupy"
      class="top-header"
      :style="{ height: topHeight + 'rpx' }"
    >
      <!-- 头部占位 -->
    </view>
    <view
      v-else-if="isStatusBar"
      class="top-header"
      :style="{ height: statusBarHeight + 'rpx' }"
    >
      <!-- 状态栏占位 -->
    </view>
    <!-- 头部	 -->
    <view
      class="nav-bar"
      :class="{ activeIsFixed: isFixed }"
      :style="getNavBarStyle"
    >
      <view class="back" v-if="isBack" @click="back"> back </view>
      <view class="left" v-else-if="!onlyCenter" @click="leftClick">
        <slot name="left"></slot>
      </view>
      <view
        class="center"
        :class="isOnlyCenter"
        :style="{ height: navbarHeight + 'rpx' }"
      >
        <slot></slot>
      </view>
      <view class="right" @click="rightClick" v-if="!onlyCenter">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * NavBar 自定义导航栏
 * @description 导航栏组件，主要用于头部导航
 * @property {Number, String} color 图标和文字颜色
 * @property {String} isBack 是否显示返回按钮
 * @property {Boolean} isFixed  是否固定顶部
 * @property {Boolean} onlyCenter  是否只显示中间部分
 * @property {String} bgColor  背景颜色
 * @property {Number} opacity 背景透明度
 * @property {Boolean} isOccupy 固定时默认占位
 * @property {Boolean} isStatusBar 默认包含系统状态栏
 * @property {Boolean} isSplitLine 导航栏分割线
 * @property {String} splitLineColor 导航栏分割线颜色
 * @property {Number} splitLineHeight 导航栏分割线高度
 * @property {String} styleSelf 自定义样式
 * @event {Function} back 返回
 * @event {Function} leftClick 左侧点击事件
 * @event {Function} rightClick 右侧点击事件
 */
export default {
  name: "Header",
  data() {
    return {
      //状态栏高度
      statusBarHeight: 0,
      //状态栏加导航栏高度
      topHeight: 0,
    };
  },
  props: {
    // 导航栏高度
    navbarHeight: {
      type: [Number, String],
      default: 100,
    },
    // 显示返回按钮
    isBack: {
      type: [Boolean, String],
      default: false,
    },
    // 是否固定定位
    isFixed: {
      type: [Boolean, String],
      default: false,
    },
    // 是否只显示中间部分
    onlyCenter: {
      type: [Boolean, String],
      default: false,
    },
    // 背景颜色
    bgColor: {
      type: String,
      default: "#FFFFFF",
    },
    // 背景透明度
    opacity: {
      type: [Number, String],
      default: 1,
    },
    // 固定时默认占位
    isOccupy: {
      type: [Boolean, String],
      default: true,
    },
    // 默认包含系统状态栏
    isStatusBar: {
      type: [Boolean, String],
      default: true,
    },
    // 导航栏分割线
    isSplitLine: {
      type: [Boolean, String],
      default: true,
    },
    // 导航栏分割线颜色
    splitLineColor: {
      type: String,
      default: "#CCCCCC",
    },
    // 导航栏分割线高度
    splitLineHeight: {
      type: [Number, String],
      default: 1,
    },
    // 字体颜色
    color: {
      type: String,
      default: "",
    },
    // 自定义样式
    styleSelf: {
      type: String,
      default: () => {
        return "";
      },
    },
  },
  mounted() {
    this.getSystemInfo();
  },
  computed: {
    // 中间部分样式
    isOnlyCenter() {
      return this.onlyCenter ? "onlyCenter" : "";
    },
    // 导航栏样式
    getNavBarStyle() {
      let splitLine = this.isSplitLine
        ? `border-bottom: ${Number(this.splitLineHeight)}rpx solid ${
            this.splitLineColor
          };`
        : "";
      let top = this.isFixed ? `top:${Number(this.statusBarHeight)}rpx;` : "";
      let bgColor =
        Number(this.opacity) === 0 ? "" : `background-color:${this.bgColor};`;

      return `height:${Number(this.navbarHeight)}rpx;
				line-height:${Number(this.navbarHeight)}rpx;
				background:rgba(255,255,255,${Number(this.opacity)});
				color:${this.color};
				${bgColor}${top}${splitLine}${this.styleSelf}`;
    },
  },
  methods: {
    // 返回
    back() {
      uni.switchTab({
        url: "/pages/home/<USER>",
      });
    },
    rightClick() {
      this.$emit("rightClick");
    },
    leftClick() {
      this.$emit("leftClick");
    },
    //获取状态栏高度
    getSystemInfo() {
      uni.getSystemInfo({
        success: (res) => {
          //px转rpx 750用于计算比例
          this.statusBarHeight = res.statusBarHeight / (uni.upx2px(16) / 16);
          if (this.isStatusBar === false) {
            this.statusBarHeight = 0;
          }
          this.topHeight = this.statusBarHeight + this.navbarHeight;
        },
      });
    },
    // 高度样式
    getHeight(num) {
      return `height:${Number(num)}rpx;`;
    },
  },
};
</script>

<style scoped>
.status-bar {
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 9;
  background-color: #ffffff;
}

.nav-bar {
  display: flex;
  font-size: 50rpx;
}

.back,
.left,
.right {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.top-header {
  position: relative;
  top: 0;
  width: 100%;
}

.activeIsFixed {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 99;
}

.activeBgColor {
  border: solid 2rpx transparent;
  background-image: linear-gradient(#ffffff, #ffffff),
    linear-gradient(to right, #ff007f 0%, #00a0e9 100%);
  border-radius: 50upx;
  background-clip: content-box, border-box;
  background-origin: border-box;
}

.onlyCenter {
  margin: 0 20rpx;
}
</style>
