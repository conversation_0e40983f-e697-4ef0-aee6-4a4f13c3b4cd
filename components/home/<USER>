<template>
  <view style="overflow: hidden">
    <!-- 宫格类样式 -->
    <view class="recommend_goods" v-if="modes == false">
      <view v-for="(item, index) in newList" :key="index" class="goods">
        <view class="top" @tap="jumpDetails(item)">
          <image
            class="cover"
            lazy-load="true"
            :src="item.picsUrl[0].url"
            mode="scaleToFill"
          ></image>
        </view>
        <view class="bottom">
          <view class="goods_name" @tap="jumpDetails(item)">
            {{ item.name }}
          </view>
          <view class="price">
            <text class="text1">￥ {{ item.miniPrice || 0 }}</text>
          </view>
        </view>
      </view>
      <view class="place"> </view>
    </view>
    <!-- 列表类样式 -->
    <view class="list_mode" v-if="modes == true">
      <view class="goods_list" v-if="newList.length !== 0">
        <view
          v-for="(item, index) in newList"
          :key="index"
          class="goods_item"
          @tap="jumpDetails(item)"
        >
          <image :src="item.picsUrl[0].url" lazy-load="true"></image>
          <!-- 商品标签 -->
          <image class="tags" :src="tagImg[item.type - 1]"></image>
          <view class="goods_right">
            <view class="goods_name">{{ item.name }}</view>
            <view
              class="syJixin"
              v-if="item.adapterDevice && item.adapterDevice.length > 0"
            >
              适用：
              <view class="syJixin-list">
                <view
                  v-for="(items, indexs) in item.adapterDevice"
                  :key="indexs"
                  class="jxin"
                >
                  {{ items }}
                </view>
              </view>
            </view>
            <view class="numbers">
              <text>京东配送</text>
              <text>同城4小时达</text>
            </view>
            <view class="price">
                <view class="money" v-if="item.lowestSku == item.highestSku"> <text class="symbols">￥</text>{{ item.lowestSku }}</view>
                <view class="money" v-else> <text class="symbols">￥</text>{{ item.lowestSku }}~<text class="symbols">￥</text>{{ item.highestSku }}</view>
              
            </view>
          </view>
        </view>
      </view>
    </view>
    <nodata
      :colors="colors"
      title="暂无分类商品"
      v-if="newList.length == 0"
    ></nodata>
    <view class="loading" v-if="!isEnloading && newList.length">{{
      isloading ? "加载中..." : newList.length > 10 ? "上拉加载更多" : ""
    }}</view>
    <view class="loading" v-if="newList.length !== 0 && isEnloading"
      >—— 到底啦 ——</view
    >
  </view>
</template>

<script>
// import {setCart, getCart} from '@/utils/auth.js'
export default {
  components: {},
  data() {
    return {
      showModal: false,
      nowList: {},
      newList: [],
      tagImg: ["/static/images/home/<USER>"],
    };
  },
  props: {
    colors: {
      type: String,
    },
    dataList: {
      type: Array,
    },
    modes: {
      //控制显示宫格类样式或者列表类样式
      type: Boolean,
      default: true,
    },
    isloading: {
      type: Boolean,
      default: false,
    },
    isEnloading: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    this.setDataList(this.dataList);
  },
  watch: {
    dataList(value) {
      this.setDataList(value);
    },
  },
  methods: {
    setDataList(newVal) {
      // const newList = [].concat(
      //   ...Array.from(
      //     newVal.reduce(
      //       (total, cur, index) => {
      //         //瀑布流处理
      //         total[index % 2].push(cur);
      //         return total;
      //       },
      //       {
      //         0: [],
      //         1: [],
      //         length: 2,
      //       }
      //     )
      //   )
      // );
      this.newList = newVal;
    },
    onhide() {
      this.showModal = false;
    },
    addCart(item) {
      //加入购物车弹出对话框
      console.log("点击了", item);
      this.showModal = true;
      this.nowList = item;
    },
    jumpDetails(e) {
      this.$emit("goInfoGoods", e);
    },
  },
};
</script>
<style scoped lang="scss">
.recommend_goods {
  padding: 20upx;
  // column-count: 2;
  // /*分为两列  用于瀑布流*/
  // column-gap: 20upx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .goods {
    width: 49%;
  }
}

.loading {
  height: 80upx;
  line-height: 80upx;
  text-align: center;
  color: #ccc;
  font-size: 24upx;
  width: 100%;
  margin-bottom: 20upx;
}

.goods {
  height: 100%;
  overflow: auto;
  margin-bottom: 20upx;
  break-inside: avoid;
  /*用于瀑布流*/
  border-radius: 10upx;
  box-sizing: content-box;

  &:first-child {
    margin-top: 0;
  }
}

.goods .top {
  height: 45vw;
  overflow: hidden;
  position: relative;
  background-color: #ffffff;
}

.top .cover {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.top .tags {
  width: 65upx;
  height: 65upx;
  position: absolute;
  top: 0;
  left: 0;
}

.bottom {
  padding: 15upx;
  background-color: #ffffff;
  overflow: hidden;
}

.goods_name {
  /* height: 66upx; */
  font-size: 24upx;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  font-weight: bold;
}

.bottom .price {
  // margin-top: 15upx;
}

.bottom .price .text1 {
  font-size: 32upx;
  font-weight: bold;
  color: #fa436a;
}

.bottom .price .text2 {
  font-size: 22upx;
  color: #a0a0a0;
  text-decoration: line-through;
  padding-left: 15upx;
}

.goumai {
  margin-top: 10upx;
}

.g_left {
  font-size: 24upx;
  float: left;
  align-items: center;
}

.g_left text {
  display: inline-block;
  height: 35upx;
  line-height: 35upx;
  padding: 0 10upx;
  background-color: #faeff7;
  border-radius: 10upx;
  margin-right: 20upx;
  color: #fa436a;
}

.g_right {
  float: right;
  font-size: 28upx;
  color: #fa436a;
}

.mask {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  z-index: 900;
  opacity: 0.7;
}

.sku {
  width: 100vw;
  min-height: 30vh;
  position: fixed;
  bottom: -100%;
  z-index: 910;
  left: 0;
  background-color: #ffffff;
  padding: 20upx 4%;
  border-top-left-radius: 10upx;
  border-top-right-radius: 10upx;
  border-bottom: 1upx solid #eee;
  transition: all 0.3s;
}

.shows {
  /* #ifdef MP */
  bottom: 0 !important;
  /* #endif */
  /* #ifdef H5 */
  bottom: 100upx !important;
  /* #endif */
  transition: all 0.3s;
}

.sku_top {
  overflow: hidden;
}

.sku_top image {
  height: 170upx;
  width: 170upx;
  float: left;
  margin-right: 15upx;
  border-radius: 8upx;
}

.sku_top .sku_title {
  font-size: 24upx;
  line-height: 35upx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  font-weight: bold;
}

.sku_top .moneys {
  font-size: 28upx;
  line-height: 40upx;
  overflow: hidden;
  margin-top: 20upx;
}

.sku_top .kucun {
  font-size: 24upx;
  color: #999;
  overflow: hidden;
}

.sku_list {
  margin-top: 20upx;
  overflow: hidden;
  margin-bottom: 40upx;
}

.sku_name {
  font-size: 24upx;
  color: #666;
  overflow: hidden;
}

.sku_tag {
  overflow: hidden;
  margin-top: 20upx;
}

.sku_tag .tag_s {
  height: 50upx;
  line-height: 50upx;
  padding: 0 20upx;
  text-align: center;
  font-size: 22upx;
  color: #333;
  background-color: #f5f5f5;
  border: 1upx solid rgba(0, 0, 0, 0.05);
  float: left;
  border-radius: 30upx;
  margin-right: 20upx;
  transition: all 0.2s;
  margin-bottom: 20upx;
}

.number {
  margin-top: 10upx;
  border-top: 1upx solid #ccc;
  width: 100%;
  height: 80upx;
  line-height: 80upx;
  padding-top: 10upx;
}

.number .n_left {
  float: left;
  font-size: 28upx;
  color: #333;
}

.number .n_right {
  float: right;
  height: 60upx;
  width: 200upx;
  background-color: #f5f5f5;
  margin-top: 10upx;
  border-radius: 5upx;
}

.n_right .jian,
.jia {
  width: 60upx;
  height: 60upx;
  text-align: center;
  line-height: 60upx;
  font-size: 42upx;
}

.jian {
  float: left;
}

.jia {
  float: right;
}

.jian:active {
  background-color: #eee;
}

.jia:active {
  background-color: #eee;
}

.n_right input {
  width: 76upx;
  float: left;
  text-align: center;
  margin-top: 6upx;
}

.btn_box {
  margin-top: 40upx;
}

.btn_box .addcart_btn,
.submit {
  width: 40vw;
  height: 56upx;
  line-height: 56upx;
  border-radius: 42upx;
  font-size: 24upx;
  text-align: center;
  color: #ffffff;
  float: left;
  margin-left: 30upx;
  margin-bottom: 30upx;
}

.btn_box view:active {
  opacity: 0.8;
}

/* 列表类样式 */
.list_mode {
  padding: 20upx 22rpx 0upx;
  z-index: 10;
  // background-color: #ffffff;
}

.goods_list {
  overflow: hidden;
}

.goods_list .goods_item {
  align-items: center;
  // border-bottom: 1upx solid #eee;
  background-color: #ffffff;
  // padding-bottom: 10upx;
  margin-bottom: 15upx;
  overflow: hidden;
  position: relative;
  border-radius: 10rpx;
  padding: 10rpx;
  box-sizing: border-box;

  &:last-of-type {
    border-bottom: none;
  }
}

.goods_item image {
  width: 200upx;
  height: 200upx;
  float: left;
  border-radius: 10upx;
  margin-right: 5upx;
}

.goods_item .tags {
  width: 60upx;
  height: 60upx;
  position: absolute;
  top: 0;
  left: 0;
}

.goods_right {
  /* float: left; */
  height: 200upx;
  padding: 0 10upx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods_right .goods_name {
  font-size: 28upx;
  overflow: hidden;
  font-weight: bold;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  color: #333;
  min-height: 50rpx;
}

.goods_right .syJixin {
  flex: 1;
  font-size: 24upx;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  color: #000;
  display: flex;
  align-items: center;

  .syJixin-list {
    height: 30rpx;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    overflow: hidden;
    flex-wrap: wrap;
    text-overflow: ellipsis;
    flex: 1;

    .jxin {
      padding: 0 10upx;
      background-color: #e8e8e8;
      border-radius: 10upx;
      color: #000;
      font-size: 20rpx;
      margin-right: 10rpx;
      display: flex;
      align-items: center;
      justify-content: center;
	  margin-bottom: 16rpx;
    }
  }
}

.goods_right .numbers {
  font-size: 20upx;
  line-height: 30upx;
  overflow: hidden;
  display: flex;
  align-content: center;
}

.goods_right .numbers text {
  display: inline-block;
  height: 35upx;
  line-height: 35upx;
  padding: 0 10upx;
  background-color: #faeff7;
  border-radius: 10upx;
  margin-right: 20upx;
  color: #fa436a;
}

.goods_right .price {
  line-height: 40upx;
  font-size: 24upx;
  overflow: hidden;
  // margin-top: 20upx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price .money {
  margin-right: 20upx;
  font-size: 32upx;
  font-weight: bold;
  color: #fa436a;
  display: flex;
  align-items: center;
  
  .symbols{
	  font-size: 24rpx !important;
  }
  
}

.hx_money {
  text-decoration: line-through;
  color: #999;
  font-size: 22upx;
}

.pay-number {
  color: #999;
}

.gouwuche {
  font-size: 32upx;
  float: right;
  margin-right: 20upx;
  color: #fa436a;
}

.nodata {
  color: #999;
  text-align: center;
  font-size: 24upx;
  margin-top: 20upx;
  height: 80upx;
  line-height: 80upx;
}
</style>
