<!--
 * @Description: 
 * @Autor: shh
 * @Date: 2023-03-13 09:33:58
 * @LastEditors: shh
 * @LastEditTime: 2023-06-25 12:24:52
-->
<template>
  <view>
    <picker
      mode="multiSelector"
      @columnchange="bindMultiPickerColumnChange"
      :value="multiIndex"
      range-key="name"
      :range="multiArray"
      placeholder="所在楼层"
      style="text-align: right"
    >
      <uni-icons color="#aaa" type="arrowright" size="15" class="cell-more" />
    </picker>
  </view>
</template>

<script>
/* eslint-disable */
import uniIcons from "../uni-icons/uni-icons.vue";
export default {
  components: {
    uniIcons,
  },
  props: {
    multiArray: {
      type: Array,
      default() {
        return [];
      },
    },
    defaultLevel: {
      type: Number,
      default: 3,
    },
  },
  data() {
    return {
      multiIndex: [0, 0, 0],
      multiStr: "",
    };
  },
  mounted() {},
  methods: {
    async bindMultiPickerColumnChange(e) {
      let obj = {
        data: this.multiArray[e.detail.column][e.detail.value],
        column: e.detail.column,
      };

      this.$emit("getRegions", obj);
    },
  },
};
</script>
