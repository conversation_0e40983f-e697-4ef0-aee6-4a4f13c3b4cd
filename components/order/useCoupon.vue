<template>
	<view class="page-total">
		<view class="cu-modal bottom-modal" :class="{ show: isShow }" @click="hide">
			<view class="cu-dialog">
				<view class="coupon-title">
					<view class="title">优惠券</view>
					<view class="explain">使用说明</view>
				</view>
				<view class="coupon-tab">
					<view class="tab" :class="{ 'action': TabShow === 0 }" @click.stop="onTab(0)">
						<text>可用优惠券（1）</text>
						<text class="line"></text>
					</view>
					<view class="tab" :class="{ 'action': TabShow === 1 }" @click.stop="onTab(1)">
						<text>不可用优惠券（1）</text>
						<text class="line"></text>
					</view>
				</view>
				<!-- 优惠券数据 -->
				<view class="coupon-data">
					<view class="coupon-list">
						<view class="list" :class="{ 'forbidden': TabShow === 1 }" v-for="(item, index) in 6" :key="index">
							<view class="coupon-price">
								<view class="discounts">
									<text class="min">￥</text>
									<text class="max">200</text>
								</view>
								<view class="full-reduction">
									<text>满600元减200元</text>
								</view>
								<view class="jag"></view>
							</view>
							<view class="coupon-info">
								<view class="check" v-show="TabShow === 0">
									<view class="iconfont icon-check"></view>
								</view>
								<view class="info-title">
									<view class="tag">
										<text>限品类券</text>
									</view>
									<view class="title">
										<text>仅可购买酒水部分商品</text>
									</view>
								</view>
								<view class="date-get">
									<view class="date">
										<text>2020.3.09-2020.03.15</text>
									</view>
									<!-- <view class="get">
										<text>点击领取</text>
									</view> -->
								</view>
							</view>
						</view>
					</view>
				</view>
				<!--确认 -->
				<view class="cpupon-confirm">
					<view class="confirm" @click.stop="onConfirm">确定</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			isShow: false,
			TabShow: 0
		};
	},
	methods: {
		show() {
			this.isShow = true;
		},
		hide() {
			this.isShow = false;
		},
		/**
		 * tab 点击
		 */
		onTab(index) {
			this.TabShow = index;
		},
		/**
		 * 确认点击
		 */
		onConfirm() {
			this.hide();
		}
	}
}
</script>

<style scoped lang="scss">
.cu-dialog {
	width: 100%;
	height: 70%;
	border-radius: 20rpx 20rpx 0 0 !important;
	background-color: #f6f6f6 !important;

	.coupon-title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 4%;
		height: 100rpx;
		background-color: #FFFFFF;

		.title {
			font-size: 32rpx;
			color: #222222;
		}

		.explain {
			font-size: 26rpx;
			color: #959595;
		}
	}

	.coupon-tab {
		display: flex;
		align-items: center;
		width: 100%;
		height: 80rpx;
		background-color: #FFFFFF;

		.tab {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 50%;
			height: 100%;

			text {
				font-size: 26rpx;
				color: #555555;
			}

			.line {
				display: none;
				position: absolute;
				left: 50%;
				bottom: 10rpx;
				width: 60rpx;
				height: 7rpx;
				background: linear-gradient(to right, #fa436a, #f6f6f6);
				transform: translate(-50%, 0);
			}
		}

		.action {
			text {
				color: #222222;
			}

			.line {
				display: block;
			}
		}
	}

	// 优惠券数据
	.coupon-data {
		padding: 20rpx 4% 100rpx;
		height: 80%;
		overflow: auto;

		.coupon-list {
			width: 100%;

			.list {
				position: relative;
				display: flex;
				align-items: center;
				width: 100%;
				height: 200rpx;
				box-shadow: 0 0 14rpx rgba(0, 0, 0, 0.1);
				border-radius: 6rpx;
				margin-bottom: 20rpx;
				background-color: #FFFFFF;
				overflow: hidden;

				.coupon-price {
					position: relative;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					width: 30%;
					height: 100%;
					background-color: #fa436a;

					.discounts {
						display: flex;
						justify-content: center;
						align-items: flex-end;
						width: 100%;

						.min {
							color: #FFFFFF;
							font-size: 32rpx;
							font-weight: bold;
						}

						.max {
							font-size: 48rpx;
							color: #FFFFFF;
							font-weight: bold;
						}
					}

					.full-reduction {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 100%;
						height: 60rpx;

						text {
							font-size: 24rpx;
							color: #FFFFFF;
						}
					}

					.jag {
						position: absolute;
						top: 0;
						left: -6rpx;
						width: 6px;
						height: 100%;
						background-size: 10px 6px;
						background-color: #fff;
						background-position: 100% 35%;
						background-image: linear-gradient(-45deg, #fa436a 25%, transparent 25%, transparent),
							linear-gradient(-135deg, #fa436a 25%, transparent 25%, transparent),
							linear-gradient(-45deg, transparent 75%, #fa436a 75%),
							linear-gradient(-135deg, transparent 75%, #fa436a 75%);
					}
				}

				.coupon-info {
					position: relative;
					width: 70%;
					height: 90%;
					padding: 0 4%;

					.check {
						position: absolute;
						right: 20rpx;
						top: 50%;
						width: 50rpx;
						height: 50rpx;
						transform: translate(0, -50%);

						.iconfont {
							font-size: 36rpx;
							color: #666666;
						}
					}

					.info-title {
						display: flex;
						width: 100%;
						height: 100rpx;

						.tag {
							margin-right: 10rpx;

							text {
								padding: 4rpx 16rpx;
								color: #FFFFFF;
								background-color: #fa436a;
								font-size: 24rpx;
								border-radius: 40rpx;
							}
						}

						.title {
							text {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}

					.date-get {
						display: flex;
						align-items: center;
						justify-content: space-between;
						width: 100%;
						height: 80rpx;

						.date {
							display: flex;
							align-items: center;

							text {
								font-size: 24rpx;
								color: #555555;
							}
						}

						.get {
							text {
								padding: 10rpx 20rpx;
								background-color: #fa436a;
								color: #FFFFFF;
								font-size: 24rpx;
								border-radius: 100rpx;
							}

							.use {
								background-color: transparent;
								border: 1px solid #fa436a;
								color: #fa436a;
							}
						}
					}
				}

				.use-status {
					position: absolute;
					right: -20rpx;
					top: -25rpx;
					z-index: 10;
					width: 100rpx;
					height: 100rpx;
					border: 2rpx solid #C0C0C0;
					border-radius: 100%;

					text {
						display: inline-block;
						color: #C0C0C0;
						font-size: 24rpx;
						transform: rotate(45deg);
						margin-top: 40rpx;
					}
				}
			}

			// 不可用样式
			.forbidden {
				.coupon-price {
					background-color: #CCCCCC;

					.jag {
						background-image: linear-gradient(-45deg, #CCCCCC 25%, transparent 25%, transparent),
							linear-gradient(-135deg, #CCCCCC 25%, transparent 25%, transparent),
							linear-gradient(-45deg, transparent 75%, #CCCCCC 75%),
							linear-gradient(-135deg, transparent 75%, #CCCCCC 75%);
					}
				}

				.coupon-info {
					.info-title {
						.title {
							text {
								color: #CCCCCC;
							}
						}

						.tag {
							text {
								background-color: #CCCCCC;
							}
						}
					}

					.date-get {
						.date {
							text {
								color: #CCCCCC;
							}
						}
					}
				}
			}
		}
	}

	// 确认
	.cpupon-confirm {
		position: absolute;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		background-color: #FFFFFF;

		.confirm {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 90%;
			height: 70%;
			font-size: 26rpx;
			color: #FFFFFF;
			background-color: #fa436a;
			border-radius: 100rpx;
		}
	}
}
</style>
