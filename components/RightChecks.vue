<template>
  <view>
    <view>
      <view class="first-menu" @tap="togglePage()">
        <text v-if="defaultSelected" class="cell-tip">
          已选{{ activeMenuArr.length }}个</text>
        <text v-else class="cell-tip-grey">{{ filterData.label }}</text>
        <uni-icons color="#aaa" type="arrowright" size="15" class="cell-more" />
      </view>
    </view>
    <view v-if="showPage" class="HMfilterDropdown" :class="{ setDropdownBottom: maskVisibility }"
      :style="{ top: menuTop + 'rpx' }">

      <view class="mask" :class="{ show: isShowMask, hide: maskVisibility !== true }" @tap="togglePage()"></view>
      <block>
        <view class="sub-menu-class show">
          <view style="width: 100%; display: block">
            <hx-navbar :config="config" @clickBtn="onClickBtn" />
            <!-- :style="{ height: statusBarHeight + 'px' }" -->
            <view class="block-content">
              <block>
                <scroll-view class="sub-menu-list alone">
                  <block v-for="(sub, index) in filterData.children" :key="index">
                    <view class="sub-menu" :id="'first_id' + index"
                      :class="{ on: (activeMenuArr.toString()).includes(sub.value) }" @tap="selectHierarchyMenu(sub)">
                      <view class="menu-name">
                        <text>{{ sub.label }}</text>
                        <text class="iconfont selected"></text>
                      </view>
                    </view>
                  </block>
                  <view class="btn-box">
                    <view class="reset" @tap="resetFilterData()">重置</view>
                    <view class="submit" @tap="setFilterData()">确定</view>
                  </view>
                </scroll-view>
              </block>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>
</template>
<script>
import uniIcons from "./uni-icons/uni-icons.vue";
export default {
  name: "uniHeaderSelecter",
  components: {
    uniIcons,
  },
  data() {
    return {
      config: {
        title: "选择" + this.title,
        // 取消返回
        back: false,
        leftButton: [
          {
            key: "btn3",
            icon: "&#xe679;",
            position: "left",
          },
        ],
      },
      subData: [], //菜单数据
      menu: [], //顶部横条数据
      showPage: false, //菜单页面显示/隐藏动画控制
      pageState: [], //页面的状态
      activeMenuArr: [], //UI状态
      shadowActiveMenuArr: [], //记录选中
      defaultActive: [],
      triangleDeg: [], //小三角形的翻转动画控制
      isShowMask: false, //遮罩层显示/隐藏动画控制
      maskVisibility: false, //遮罩层显示/隐藏状态
      //滚动区域定位
      firstScrollInto: 0,
      secondScrollInto: 0,
      componentTop: 0, //组件top
      isReadNewSelect: false,
      isIOS: true,
      titleHeight: 0,
      statusBarHeight2: 0,
    };
  },
  props: {
    menuTop: {
      value: Number,
      default: false,
    },
    filterData: {
      value: Object,
      default: () => {
        return {};
      },
    },
    defaultSelected: {
      value: String | Number,
    },
    updateMenuName: {
      value: Boolean,
      default: true,
    },
    isSimple: {
      value: Boolean,
      default: false,
    },
    dataFormat: {
      value: String,
      default: "Array",
    },
    title: {
      value: String,
      default: "",
    },
  },
  watch: {
    defaultSelected: {
      handler(newVal) {
        if (!newVal) {
          this.activeMenuArr = []
        } else {
          this.activeMenuArr = newVal.split(",");
        }

        console.log(this.activeMenuArr)
      },
      deep: true,
      immediate: true,
    },

  },
  computed: {
    //获取系统状态栏高度
    statusBarHeight() {
      var that = this;
      return (
        uni.getSystemInfoSync().screenHeight -
        uni.getSystemInfoSync().statusBarHeight -
        44
      );
      // return 60;
    },
    navbarHeight() {
      return uni.getSystemInfoSync().statusBarHeight + 10;
    },
    screenWidth() {
      return uni.getSystemInfoSync().screenHeight;
    },
  },
  methods: {
    onClickBtn() {
      this.togglePage();
    },

    //选中
    selectHierarchyMenu(data) {
      let index = this.activeMenuArr.indexOf(data.value);
      index < 0
        ? this.activeMenuArr.push(data.value)
        : this.activeMenuArr.splice(index, 1);
      // if (this.activeMenuArr.includes(data.value)) {
      //   this.activeMenuArr.splice(
      //     this.activeMenuArr.indexOf(Number(data.value)),
      //     1
      //   );
      // } else {
      //   this.activeMenuArr.push(data.value);
      // }
    },

    //菜单开关
    togglePage() {
      this.showPage = !this.showPage;
      if (this.showPage) {
        this.hideMask();
        this.showPage = -1;
      } else {
        this.showMask();
      }
    },
    //hide遮罩层
    hideMask() {
      this.isShowMask = false;
      setTimeout(() => {
        this.maskVisibility = false;
      }, 200);
    },
    //show遮罩层
    showMask() {
      this.maskVisibility = true;
      this.$nextTick(() => {
        setTimeout(() => {
          this.isShowMask = true;
        }, 0);
      });
    },
    //写入结果，筛选
    setFilterData() {
      console.log(this.activeMenuArr);
      this.togglePage(this.showPage);
      this.$emit("confirm", this.activeMenuArr?.join(", "));
    },
    //重置结果和ui，筛选
    resetFilterData() {
      this.activeMenuArr = [];
      this.$forceUpdate();
    },
  },
};
</script>

<style lang="scss">
.cell-more {
  align-self: baseline;
  font-size: $font-lg;
  color: $font-color-light;
  margin-left: 10upx;
}

.cell-tip {
  font-size: $font-base;
  line-height: $font-base;
  color: $font-color-dark;
}

.cell-tip-grey {
  font-size: $font-base;
  line-height: $font-base;
  color: #aaa;
}

.HMfilterDropdown {
  flex-shrink: 0;
  width: 100%;
  position: fixed;
  // position: sticky;
  z-index: 998;
  flex-wrap: nowrap;
  display: flex;
  flex-direction: row;
  top: var(--window-top);
  left: 0;
  // top:100px;
  overflow-y: hidden;

  &.setDropdownBottom {
    // height: 400px;
    bottom: 0;
  }

  view {
    display: flex;
    flex-wrap: nowrap;
  }
}

.region {
  flex: 1;
  height: 44px;
}

.first-menu {
  text-align: right;
  width: 100%;
}

.sub-menu-class {
  width: 100%;
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  transform: translate3d(100%, 0, 0);
  max-height: 100%;
  background-color: #ffffff;
  z-index: 1100;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  flex-direction: row;
  transition: transform 0.15s linear;

  &.hide {
    display: none;
  }

  &.show {
    transform: translateY(0px);
  }
}

.block-content {
  display: flex;
  height: calc(100% - 44px);
}

.sub-menu-list {
  width: 100%;
  height: 100%;
  flex-direction: column;
  overflow-y: scroll;

  .sub-menu {
    min-height: 44px;
    font-size: 13px;
    flex-direction: column;
    padding-right: 15px;
    width: 45%;
    float: left;

    >.menu-name {
      height: 44px;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      >.iconfont {
        display: none;
        font-size: 18px;
        color: #f37b1d;
      }
    }
  }

  &.first {
    flex-shrink: 0;
    width: 236rpx;
    background-color: #f0f0f0;

    .sub-menu {
      padding-left: 15px;

      &.on {
        background-color: #fff;
      }
    }
  }

  &.alone {
    max-height: 100%;
    min-height: 170px;
    height: auto;

    .sub-menu {
      min-height: calc(44px - 1rpx);
      margin-left: 15px;
      border-bottom: solid 1rpx #e5e5e5;

      &.on {
        color: #f37b1d;

        >.menu-name {
          >.iconfont {
            display: block;
          }
        }
      }
    }
  }

  &.not-first {
    .sub-menu {
      min-height: calc(44px - 1rpx);
      margin-left: 15px;
      border-bottom: solid 1rpx #e5e5e5;

      >.menu-name {
        height: calc(44px - 1rpx);

        >.iconfont {
          display: none;
          font-size: 18px;
          color: #f37b1d;
        }
      }

      &.on {
        color: #f37b1d;

        >.menu-name {
          >.iconfont {
            display: block;
          }
        }
      }

      .more-sub-menu {
        flex-direction: row;
        flex-wrap: wrap;
        padding-bottom: 9px;

        >text {
          height: 30px;
          border-radius: 3px;
          background-color: #f5f5f5;
          color: #9b9b9b;
          margin-bottom: 6px;
          margin-right: 6px;
          text-align: center;
          line-height: 30px;
          border: solid #f5f5f5 1rpx;
          flex: 0 0 calc(33.33% - 6px);
          overflow: hidden;
          font-size: 12px;

          &:nth-child(3n) {
            margin-right: 0;
          }

          &.on {
            border-color: #f6c8ac;
            color: #f37b1d;
          }

          .iconfont {
            color: #9b9b9b;
          }
        }
      }
    }
  }
}

.filter {
  width: 100%;
  height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  .menu-box {
    width: 698rpx;
    height: calc(400px - 75px);
    flex-shrink: 1;

    .box {
      width: 100%;
      margin-top: 16px;
      flex-direction: column;

      .title {
        width: 100%;
        font-size: 13px;
        color: #888;
      }

      .labels {
        flex-direction: row;
        flex-wrap: wrap;

        .on {
          border-color: #f37b1d;
          background-color: #f37b1d;
          color: #fff;
        }

        >view {
          width: 148rpx;
          height: 30px;
          border: solid 1rpx #adadad;
          border-radius: 2px;
          margin-right: 15px;
          margin-top: 8px;
          font-size: 12px;
          flex-direction: row;
          justify-content: center;
          align-items: center;

          &:nth-child(4n) {
            margin-right: 0;
          }
        }
      }
    }
  }
}

.btn-box {
  flex-shrink: 0;

  width: 100%;
  padding: 0 60px;
  height: 75px;
  flex-direction: row !important;
  align-items: center;
  justify-content: space-between;

  >view {
    width: 240rpx;
    height: 40px;
    border-radius: 40px;
    border: solid 1rpx #f37b1d;
    align-items: center;
    justify-content: center;
  }

  .reset {
    color: #f37b1d;
  }

  .submit {
    color: #fff;
    background-color: #f37b1d;
  }
}

.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  transition: background-color 0.15s linear;

  &.show {
    background-color: rgba(0, 0, 0, 0.5);
  }

  &.hide {
    display: none;
  }
}

/* 字体图标 */
@font-face {
  font-family: "HM-FD-font";
  src: url("data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAALAAAsAAAAABpQAAAJzAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDBgp4gQIBNgIkAwwLCAAEIAWEbQc5G8sFERWMIbIfCbbzqA4hp7InSBibVsYGb4J42o82b3e/nJlHMw/NHbGOlwKJRCRpwzPtpAECCOZubdqxjYpQLMlVg+70/08edrgQOtx2ukpVyApZn+dyehPoQObHo3O85rYx9vOjXoBxQIHugW2yIkqIW2QXcScu4jwE8CSWbKSmrqUHFwOaJoCsLM5P4haSGIxRcRHshrUGucLCVcfqI3AZfV/+USguKCwNmtsxVztDxU/n55C+3W0Z4QQpEOTNFqCBbMCAjDUWB9CIwWk87aa70cYgqLkyd3dEmm+18R8eKATEBrV7A5CulBT8dKiWOYZk412XNcDdKSEKSGODnyKIDl+dmVt9/Dx4pu/xyeutkMlHISGPTsPCnoTNP9nOT6wTtDdlO6dPr47efvj942lkYuQzrhMKEjq9N6y98P3340gmlJ/RStUD6F31CAEEPtUW94/7rf+7XgaAz57X0ZHXAGsFFwVgw38yALuMb0IBbVyNamFYEw4oKMDTj3AHRQP5Pt4dci9VwSVkRNQh5r7CLskZadhsWHhRDBsXczk8ZYk3ewnCxmQeQKa3BOHvA8XXO2j+vqRhf7CE+sPmn4anvoL29JLa4qqaUQkmoK+QG2osCckq7txi2leK86aIPyJ3eQZ8xytXYmyQ51jQndJAxIJlqiGSLsOqImiZCjTiZCJt6Lq26U2OoXqwUo0hRaAE0K5AziANy/uLVeXzWyjVqyjcoeupjxDr5MMDn8MDkLG9Aenu5ZrOSSoghAUsRmogkkahSoWAtnlUARnCkY3It0Iu7mWhdmd9Z/19BwBP6GidEi0G56opckXTGZVSPxgAAAA=");
}

.iconfont {
  font-family: "HM-FD-font" !important;
  font-size: 13px;
  font-style: normal;
  color: #757575;

  &.triangle {
    &:before {
      content: "\e65a";
    }
  }

  &.selected {
    &:before {
      content: "\e607";
    }
  }
}
</style>
