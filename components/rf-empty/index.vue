<!--
 * @Description: 
 * @Autor: shh
 * @Date: 2023-03-13 09:33:58
 * @LastEditors: yangzhong
 * @LastEditTime: 2023-11-29 11:07:23
-->
<template>
  <view class="empty">
    <view class="empty-content" :class="{ emptyOnly: !isRecommendShow }">
      <text class="iconfont iconempty"></text>
      <text class="empty-content-info">{{ info }}</text>
    </view>
  </view>
</template>

<script>
export default {
  components: {},
  props: {
    src: {
      type: String,
      default: "empty",
    },
    isRecommendShow: {
      type: Boolean,
      default: true,
    },
    info: {
      type: String,
      default: "",
    },
    bottom: {
      type: Number,
      default: 0,
    },
    list: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {};
  },
  computed: {},
};
</script>

<style lang="scss">
.empty {
  // background-color: $color-white;

  .empty-content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 20upx 0 80upx;

    .empty-content-info {
      font-size: $font-base - 2upx;
      color: #ddd
    }

    .iconfont {
      font-size: 140rpx;
      color: #ddd
    }

    &-image {
      width: 200upx;
      height: 200upx;
    }
  }

  .emptyOnly {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
  }
}
</style>
