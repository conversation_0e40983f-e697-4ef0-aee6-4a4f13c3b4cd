<template>
	<view class="upload-container">
		<view class="upload-title" v-if="showTitle">
			<view class="text">最多选择{{ limit }}个文件</view>
			<view class="number">{{ fileLists.length }}/{{ limit }}</view>
		</view>
		<u-upload
			:width="`${width}px`"
			:height="`${height}px`"
			:fileList="fileLists"
			:capture="capture"
			:accept="accept"
			:maxCount="limit"
			:disabled="disabled"
			:deletable="deletable"
			name="1"
			:multiple="multiple"
			:previewFullImage="previewFullImage"
			:camera="camera"
			@afterRead="afterRead"
			@delete="deletePic"
		></u-upload>
	</view>
</template>

<script>
import { uploadFile } from '@/utils/upload';
export default {
	props: {
		fileList: {
			type: Array,
			default: () => []
		},
		capture: {
			type: Array,
			default: () => ['album', 'camera']
		},
		limit: {
			type: Number,
			default: 8
		},
		disabled: {
			type: Boolean,
			default: false
		},
		deletable: {
			type: Boolean,
			default: true
		},
		multiple: {
			type: Boolean,
			default: true
		},
		previewFullImage: {
			type: Boolean,
			default: true
		},
		camera: {
			type: String,
			default: 'back'
		},
		width: {
			type: String || Number,
			default: '100'
		},
		height: {
			type: String || Number,
			default: '100'
		},
		showTitle: {
			type: Boolean,
			default: true
		},
		accept: {
			type: String,
			default: 'image'
		}
	},
	data() {
		return {
			fileLists: []
		};
	},
	mounted() {
		this.fileLists = this.fileList;
	},
	watch: {
		fileList: {
			handler(newVal) {
				this.fileLists = newVal;
			},
			deep: true,
			immediate: true
		}
	},
	methods: {
		// 删除图片
		deletePic(e) {
			this.fileLists.splice(e.index, 1);
			this.$emit('submit', this.fileLists);
		},
		// 新增图片
		// async afterRead(e) {
		// 	e.file.map(async (item) => {
		// 		let res = await uploadFile(item);
		// 		if (res.url) {
		// 			this.fileLists.push({
		// 				url: res.url,
		// 				name: res.name,
		// 				key: res.key
		// 			});
		// 			this.$emit('submit', this.fileLists);
		// 		}
		// 	});
		// }
		// 显示Loading
		async afterRead(e) {
			let lists = [...e.file];
			let fileListLen = this.fileLists.length;

			// Push new files with 'uploading' status
			lists.forEach((item) => {
				this.fileLists.push({
					...item,
					status: 'uploading',
					message: '上传中'
				});
			});

			for (let i = 0; i < lists.length; i++) {
				const result = await uploadFile(lists[i]);
				console.log(result, 'result');
				let itemIndex = fileListLen + i;
				this.fileLists.splice(
					itemIndex,
					1,
					Object.assign(this.fileLists[itemIndex], {
						status: 'success',
						message: '',
						url: result
					})
				);
			}
			// 为了实现图片预览
			this.fileLists = this.fileLists.map((item) => (item.status ? item.url : item));
			this.$emit('submit', this.fileLists);
		}
	}
};
</script>

<style lang="scss" scoped>
.upload-container {
	width: 100%;
	.upload-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-top: 10rpx;
		padding-bottom: 20rpx;
		font-size: 28rpx;
		.number,
		.text {
			transform: scale(0.85);
			transform-origin: left top;
		}
	}
	// ::v-deep .u-upload {
	// 	.u-upload__wrap {
	// 		.u-upload__wrap__preview {
	// 			.u-upload__deletable {
	// 				position: absolute;
	// 				height: 48rpx !important;
	// 				width: 48rpx !important;
	// 				top: 8rpx !important;
	// 				right: 5rpx !important;
	// 				border-radius: 50% !important;
	// 				.u-upload__deletable__icon {
	// 					transform: scale(3);
	// 					top: 20rpx !important;
	// 					right: 19rpx !important;
	// 				}
	// 			}
	// 		}
	// 		.u-upload__button {
	// 			.u-icon {
	// 				text {
	// 					font-size: 80rpx !important;
	// 				}
	// 			}
	// 		}
	// 	}
	// }
}
</style>
