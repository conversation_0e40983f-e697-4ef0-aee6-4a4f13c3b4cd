/*
 * @Description:
 * @Autor: shh
 * @Date: 2023-03-13 09:33:58
 * @LastEditors: shh
 * @LastEditTime: 2023-11-27 17:02:05
 */
import indexConfig from "./index.config.js";
const PATH = ""; //indexConfig.assetsPath;
/*
 * 图片静态资源表，所有图片资源路径在这统一管理，不应该写死在页面中，该数据挂载到Vue原型中。
 * 页面使用：this.$mAssetsPath.grid_1
 * CSS背景：应尽量使用:style="" 行内样式设置背景图
 * PATH说明：本地路径或者服务器路径
 *
 * 举例：<image :src="grid_1">  需要在data中映射 grid_1: this.$mAssetsPath.grid_1
 *
 * 特别注意：经测试小程序中不支持 <image :src="$mAssetsPath.grid_1"> 该用法
 */

export default {
	// 默认头像
	headImg: PATH + "/other/missing-face.png",
	// 出错填充图片
	errorImage: PATH + "/errorImage.jpg",
	// 品牌logo
	logo: PATH + "/other/logo.png",
	// 商城新闻
	newsBg: PATH + "/other/news.png",
	// 用户背景
	userBg: "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/temp/f446cd10-da34-11ef-9daf-7b79697d20f5",
	// vip背景
	vipCardBg: "https://yzimg.jikebang.com/gczh/imgs/vip-card.png",
	// vip价格
	vipPrice: "https://yzimg.jikebang.com/gczh/imgs/vip-price.png",
	// 弧形背景
	arc: PATH + "/other/arc.png",
	// 500
	noNetWorkImg: "https://yzimg.jikebang.com/gczh/imgs/noNetWork.png",
	// 404
	notFoundImg: "https://yzimg.jikebang.com/gczh/imgs/notFound.png",
	// 升级图标
	upgradeTop: "https://yzimg.jikebang.com/gczh/imgs/upgrade-top.png",
	// 返回顶部
	backTop: PATH + "/other/top.png",
	// 分享引导背景
	shareBg: "https://yzimg.jikebang.com/gczh/imgs/share-bg.png",
	// 分销tag
	distribution: PATH + "/distribution.png",
	// 包邮tag
	pinkage: PATH + "/other/pinkage.png",

	// 预售tag
	presale: PATH + "/other/presale.png",

	// 开放站点
	openSiteBg: "https://yzimg.jikebang.com/gczh/imgs/open-site-bg.png",

	// 虚拟tag
	virtual: PATH + "/other/virtual.png",

	// 登录背景
	loginBg: "https://yzimg.jikebang.com/gczh/imgs/login-bg.png",

	// 登录插画
	loginPic: "https://yzimg.jikebang.com/gczh/imgs/login-pic.png",

	// 砍价标签
	wholesaleTag: PATH + "/other/wholesale-tag.png",

	// 拼团标签
	groupTag: PATH + "/other/group-tag.png",

	// 砍价标签
	bargainTag: PATH + "/other/bargain-tag.png",

	// 砍价标签
	discountTag: PATH + "/other/discount-tag.png",

	//授权登录
	wechat: PATH + "/other/wechat.png",

	// 授权登录
	apple: PATH + "/other/apple.png",

	// 授权登录
	money: "https://yzimg.jikebang.com/gczh/imgs/money.png",

	// 授权登录
	moneyBg: "https://yzimg.jikebang.com/gczh/imgs/money-bg.png",
};