view,
scroll-view,
swiper,
swiper-item,
cover-view,
cover-image,
icon,
text,
rich-text,
progress,
button,
checkbox,
form,
input,
label,
radio,
slider,
switch,
textarea,
navigator,
audio,
camera,
image,
video {
	box-sizing: border-box;
}

/* 骨架屏替代方案 */
.rf-skeleton {
	background: $color-white;
	padding: 20upx 0;
	border-radius: 8upx;
	box-shadow: 0upx 5upx 25upx rgba(0, 0, 0, 0.05);
}

/* 图片载入替代方案 */
.image-wrapper {
	font-size: 0;
	background: #f3f3f3;
	border-radius: 4px;

	image {
		width: 100%;
		height: 100%;
		transition: 0.6s;
		opacity: 0;

		&.loaded {
			opacity: 1;
		}
	}
}

.common-hover {
	background: #f5f5f5;
}

/*边框*/
.b-b:after,
.b-t:after {
	position: absolute;
	z-index: 3;
	left: 0;
	right: 0;
	height: 0;
	content: '';
	transform: scaleY(0.5);
	border-bottom: 1px solid $border-color-base;
}

.b-b:after {
	bottom: 0;
}

.b-t:after {
	top: 0;
}

/* button样式改写 */
uni-button,
button {
	height: 80upx;
	line-height: 80upx;
	font-size: $font-lg + 2upx;
	font-weight: normal;
	background: transparent;

	&.no-border:before,
	&.no-border:after {
		border: 0;
	}
}

uni-button[type='default'],
button[type='default'] {
	color: $font-color-dark;
}

/* input 样式 */
.input-placeholder {
	color: #999999;
}

.placeholder {
	color: #999999;
}

/*通用 */
view {
	font-size: $font-base;
	line-height: 1.5;
}

progress,
checkbox-group {
	width: 100%;
}

form {
	width: 100%;
}

.rf-button {
	color: #fff;
	border-radius: 30upx;
	font-size: $font-lg;
}

uni-button {
	background: none;
}

.one-omit {
	white-space: nowrap;
	/*规定段落中的文本不进行换行*/
	overflow: hidden;
	/*内容会被修剪，并且其余内容是不可见的。*/
	text-overflow: ellipsis;
	/*显示省略号来代表被修剪的文本*/
}

.two-omit {
	display: -webkit-box;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
}

.u-upload {
	.u-upload__wrap {
		.u-upload__wrap__preview {
			.u-upload__deletable {
				position: absolute;
				height: 48rpx;
				width: 48rpx;
				top: 8rpx;
				right: 5rpx;
				border-radius: 50%;
				display: flex !important;
				justify-content: center;
				align-items: center;
				line-height: 0 !important;
				.u-upload__deletable__icon {
					transform: scale(3);
					position: static !important;
					line-height: 0 !important;
					.u-icon {
						text {
							font-size: 10rpx !important;
							line-height: 0 !important;
						}
					}
				}
			}
		}
		.u-upload__button {
			.u-icon {
				text {
					font-size: 80rpx !important;
				}
			}
		}
	}
}

.form-container {
	width: 100%;
	// position: fixed;
	// top: 0;
	// left: 0;
	// right: 0;
	background: #fff;
	z-index: 9;
	padding: 20rpx 22rpx !important;
	border-bottom: 5rpx solid #f5f5f5;
	.customer-info {
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		.item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			min-height: 80upx;
			border-radius: 4px;
			.title {
				min-height: 50upx;
				line-height: 56upx;
				margin-right: 30rpx;
				max-width: 190rpx;
				font-size: 28rpx;
				font-weight: bold;
				color: #333;
				min-width: 130rpx;
			}
			.text {
				width: 100%;
				text-align: left;
			}
		}
	}

	.pd_body {
		width: 100%;
		// 机型
		/deep/.uni-section-content {
			width: 70%;
			display: flex;
			padding-left: 0 !important;
			padding-right: 8rpx !important;
			border-radius: 4rpx;
			.pd_input {
				width: 100%;
				font-size: 14px !important;
				border: 1px solid #e5e5e5 !important;
				box-sizing: border-box !important;
				border-radius: 4px !important;
				height: 80rpx !important;
				padding: 0 22rpx !important;
			}
			.machin-content {
				width: 100%;
				display: flex;
				align-items: center;
				padding: 0 22rpx !important;
				border: 1px solid #e5e5e5 !important;
				box-sizing: border-box;
				position: relative;

				.pd_inputs {
					border: none !important;
					flex: 1 !important;
				}

				.close_icon {
					z-index: 10000;
					width: 60rpx;
					height: 80rpx;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.socrll-check {
					width: 100%;
					position: absolute;
					top: 90rpx;
					left: 0;
					z-index: 3;

					.uni-select__selector-scroll {
						width: 100%;
						height: 210px;
						padding: 4px 22rpx !important;
						box-sizing: border-box;
						background-color: #ffffff;
						border: 1px solid #ebeef5;
						border-radius: 6px;
						box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

						.socrll-list {
							height: 210px;
							overflow: hidden;
							overflow-y: scroll;
						}

						.socrll-item {
							display: flex;
							cursor: pointer;
							line-height: 35px;
							font-size: 14px;
							text-align: center;
						}
					}

					.no-socrll-list {
						height: 45px;
						padding: 4px 22rpx !important;
						box-sizing: border-box;
						background-color: #ffffff;
						border: 1px solid #ebeef5;
						border-radius: 6px;
						box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
						overflow: hidden;
						overflow-y: scroll;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 28rpx;
						color: #999;
					}

					.socrll-popper__arrow {
						filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
						position: absolute;
						top: -4px;
						left: 23%;
						margin-right: 3px;
						border-top-width: 0;
						border: 1px solid #ebeef5;
						border-bottom-color: #ffffff;
						border-right-color: #ffffff;
						width: 18rpx;
						height: 18rpx;
						background-color: #ffffff;
						transform: rotate(45deg);
					}
				}
			}
		}
	}

	.footer {
		width: 50%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 20rpx;
		margin: auto;

		.confirm-btn {
			width: 50%;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border: none !important;
			color: #fff;
			border-radius: 37px;
			font-size: 28rpx;
			margin: 0;
		}

		.save {
			// margin-left: 70rpx;
			background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
		}

		.cancel {
			background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
		}
	}
}

.customer-main {
	position: relative;
	width: 100vw;
	overflow: hidden;
	background: #f5f6f8;

	.wrapper {
		position: relative;
		background: #f5f6f8;
		padding-bottom: 40upx;

		.main_info {
			padding: 22rpx;
			padding-bottom: 120rpx;

			.input-content {
				background: #fff;
				margin-top: 22rpx;
				border-radius: 12rpx;
				padding: 15rpx 30upx;

				.input-item {
					display: flex;
					align-items: center;
					justify-content: space-between;
					min-height: 120upx;
					border-radius: 4px;
					border-top: 2rpx solid #f5f6f8;

					&:last-child {
						margin-bottom: 0;
					}

					&:first-child {
						border-top: 0;
					}
					.title {
						min-height: 50upx;
						line-height: 56upx;
						margin-right: 30rpx;
						max-width: 190rpx;
						font-size: 28rpx;
						font-weight: bold;
						color: #333;
						min-width: 130rpx;
					}
					.require {
						position: relative;
						&::before {
							content: '*';
							display: inline-block;
							width: 20rpx;
							height: 20rpx;
							font-size: 28rpx;
							color: #ff541e;
							position: absolute;
							top: 45%;
							transform: translateY(-50%);
							left: -20rpx;
						}
					}
					.active-roles,
					.active-address {
						display: flex;
						align-items: center;
						justify-content: space-between;
						position: relative;
						/deep/.selected-list {
							margin-left: 16rpx;
						}
						.u-icon {
							position: absolute;
							right: 0;
						}
					}
					.textarea-content {
						width: 400rpx;
						font-size: $font-base + 2upx;
						color: #666666;
						display: flex;
						align-items: center;
						justify-content: flex-end;
						input {
							width: 100%;
						}
						.text {
							width: 100%;
							text-align: left;
						}
						/deep/.data-select {
							width: 100%;
							.uni-select {
								border: none;
								padding: 0;
								padding-right: 5rpx;
								border-bottom: solid 1px #e5e5e5;
							}
							.uni-select__input-text {
								margin-right: 20upx;
								font-size: 28rpx;
								color: #666666;
							}
						}
						/deep/.uni-date {
							width: 100% !important;
							flex: none;
							.uni-date-editor--x {
								border: none;
								border-bottom: solid 1px #e5e5e5;
								.uni-date__x-input {
									font-size: 28rpx;
								}
							}
							.uni-calendar__content {
								z-index: 10000;
							}
						}
						.uni-select {
							border: none;
							border-bottom: solid 1px #e5e5e5;
							padding-left: 0;
							.uni-select__input-text {
								font-size: 28rpx;
							}
						}
						.select-input-content {
							width: 100%;
							display: flex;
							align-items: center;
							padding-right: 22rpx !important;
							border-bottom: 1px solid #e5e5e5 !important;
							box-sizing: border-box;
							position: relative;

							.pd_input {
								flex: 1;
								font-size: 14px !important;
								box-sizing: border-box !important;
								border-radius: 4px !important;
								height: 80rpx !important;
								// padding: 0 22rpx !important;
							}

							.close_icon {
								z-index: 10000;
								width: 60rpx;
								height: 80rpx;
								display: flex;
								align-items: center;
								justify-content: center;
							}

							.socrll-check {
								width: 100%;
								position: absolute;
								top: 100rpx;
								left: 0;
								z-index: 3;
								// padding: 0 22rpx;

								.uni-select__selector-scroll {
									width: 100%;
									height: 210px;
									padding: 4px 22rpx !important;
									box-sizing: border-box;
									background-color: #ffffff;
									border: 1px solid #ebeef5;
									border-radius: 6px;
									box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

									.socrll-list {
										height: 210px;
										overflow: hidden;
										overflow-y: scroll;
									}

									.socrll-item {
										display: flex;
										cursor: pointer;
										line-height: 35px;
										font-size: 14px;
										text-align: center;
									}
								}

								.no-socrll-list {
									height: 45px;
									padding: 4px 22rpx !important;
									box-sizing: border-box;
									background-color: #ffffff;
									border: 1px solid #ebeef5;
									border-radius: 6px;
									box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
									overflow: hidden;
									overflow-y: scroll;
									display: flex;
									align-items: center;
									justify-content: center;
									font-size: 28rpx;
									color: #999;
								}

								.socrll-popper__arrow {
									filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
									position: absolute;
									top: -4px;
									left: 15%;
									margin-right: 3px;
									border-top-width: 0;
									border: 1px solid #ebeef5;
									border-bottom-color: #ffffff;
									border-right-color: #ffffff;
									width: 18rpx;
									height: 18rpx;
									background-color: #ffffff;
									transform: rotate(45deg);
								}
							}
						}
					}
				}

				.textarea-item {
					display: flex;
					flex-direction: column;
					margin: 10rpx 0;
					border-top: 2rpx solid #f5f6f8;

					.title {
						min-height: 50upx;
						line-height: 56upx;
						margin-right: 30rpx;
						max-width: 190rpx;
						font-size: 28rpx;
						font-weight: bold;
						color: #333;
						min-width: 130rpx;
					}
					.require {
						position: relative;
						&::before {
							content: '*';
							display: inline-block;
							width: 20rpx;
							height: 20rpx;
							font-size: 28rpx;
							color: #ff541e;
							position: absolute;
							top: 45%;
							transform: translateY(-50%);
							left: -20rpx;
						}
					}
					.textarea-content {
						width: 100%;
						height: 200rpx;
						position: relative;
						textarea {
							height: 100% !important;
							// border: 1px solid #ccc;
							border-radius: 10rpx;
							// padding: 10rpx;
							box-sizing: border-box;
						}
						.max-length {
							position: absolute;
							right: 10rpx;
							bottom: 10rpx;
							font-size: 24rpx;
							color: #999;
						}
					}
				}

				.upload-file {
					display: block;
					.image {
						display: flex;
						flex-direction: column;
					}
				}
				.top-box {
					width: 100%;
					// display: flex;
					overflow: auto;
					background: #fff;
					.tui-card {
						flex: 1;
						.list-header {
							width: 100%;
							margin: auto;
							display: flex;
							background: #fff6f3;
							.cell {
								line-height: 80rpx;
								flex: 1;
								text-align: center;
								color: #333;
								font-size: 27rpx;
								padding: 0 8rpx;
							}
						}

						.tui-card-header,
						.tui-card-header-left {
							width: 100%;
							font-size: 60rpx;
							line-height: 80rpx;
							text-align: center;
						}

						.tui-card-header {
							font-size: 40rpx;
							font-weight: bold;
							line-height: 60rpx;
						}

						.tui-card-header-left {
							text-align: left;
							font-size: 28rpx;
							line-height: 80rpx;
							font-weight: bold;
							display: flex;
							align-items: center;
							justify-content: space-between;
						}
					}
					.list-list {
						width: 100%;
						margin: auto;
						display: flex;
						align-items: center;
						justify-content: center;
						line-height: 80rpx;
						background: #fff;
						&:nth-child(odd) {
							background-color: rgba(255, 246, 243, 0.4);
						}
						.cell {
							line-height: 80rpx;
							// max-width: 200rpx;
							flex: 1;
							text-align: center;
							color: #666;
							font-size: 27rpx;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							padding: 0 8rpx;
						}

						.cells {
							line-height: 40rpx !important;
							display: -webkit-box;
							-webkit-line-clamp: 3; /* 显示的行数 */
							-webkit-box-orient: vertical; /* 设置垂直布局 */
							white-space: normal !important;
							overflow: hidden; /* 隐藏超出的内容 */
							text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
						}
						.text {
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}
					}
				}
			}
		}
	}
	.footer {
		width: 100%;
		padding: 18rpx 0 42rpx;
		display: flex;
		justify-content: center;
		background-color: #fff;
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 9999;

		.confirm-btn {
			width: 80%;
			margin: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			background: linear-gradient(90deg, #e5452f 0%, #ee652f 100%);
			border-radius: 50px;
			color: #fff;
		}
	}

	.button-content {
		width: 100%;
		height: 155rpx;
		background: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		padding: 0 20rpx;
		bottom: 0;
		left: 0;
		z-index: 9999;

		.button {
			width: 360rpx;
			height: 75rpx;
			font-size: 31rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
			background: linear-gradient(90deg, #e5452f 0%, #ee652f 100%);
		}

		.button-left {
			background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
			border-radius: 37rpx 0rpx 0rpx 37rpx;
		}

		.button-right {
			background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
			border-radius: 0rpx 37rpx 37rpx 0rpx;
		}
	}
	.button-contents {
		display: flex;
		justify-content: space-around;
		align-items: center;
		position: fixed;
		bottom: 0;
		width: 100%;
		height: 140rpx;
		background: #fff;
		padding: 0 22rpx;

		.btn {
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border: none !important;
			color: #fff;
			border-radius: 37px;
			font-size: 24rpx;
			margin: 0;
			padding: 20rpx 25rpx !important;
			white-space: nowrap;
			background: linear-gradient(180deg, #e5452f 0%, #ee652f 100%);
			// &:nth-child(odd) {
			// 	background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
			// }
			// &:nth-child(even) {
			// 	background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
			// }
		}
	}
	.loading {
		height: 80upx;
		line-height: 80upx;
		text-align: center;
		color: #ccc;
		font-size: 24upx;
		width: 100%;
		margin-bottom: 20upx;
	}
}
/deep/.uni-section-header {
	width: 170rpx !important;
	float: left;
	clear: both;
}
/deep/.uni-select {
	height: 80rpx !important;
	padding: 0 22rpx !important;
}

/deep/.line {
	display: none;
}

/deep/.uni-data-pickerview {
	.item {
		justify-content: center;
	}
}

/deep/.uni-section-content {
	height: 80rpx !important;
	padding: 0 22rpx !important;
	padding-right: 0rpx !important;
	margin-bottom: 22rpx;
}

/deep/.uni-section__content-title {
	font-size: 26rpx !important;
}

/deep/.uni-input {
	height: 80rpx !important;
	line-height: 40rpx !important;
	border: 1rpx solid #e5e5e5;
	padding: 15rpx;
	border-radius: 10rpx;
}

/deep/.uni-select__input-placeholder {
	font-size: 14px !important;
	color: #999 !important;
}

/deep/.input-value {
	padding: 0 14rpx 0 22rpx !important;
	height: 80rpx;
}

/deep/.selected-area {
	font-size: 14px !important;
	color: #999 !important;
}

/deep/.selected-item-active {
	border-bottom: 2px solid #e5452f;
}

/deep/.selected-item {
	margin: 0 6rpx !important;
	padding: 8rpx 0 !important;
}
/deep/.uni-stat__select {
	width: 100% !important;
	border: none !important;
	.uni-select {
		padding: 0 !important;
		border: none !important;
	}
}
/deep/.uni-date-editor--x {
	width: 100%;
}

/deep/.check {
	border-color: #e5452f !important;
	margin-left: 6px;
	margin-right: 0;
}
