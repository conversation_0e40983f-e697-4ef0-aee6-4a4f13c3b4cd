<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 腾讯地图所需权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <!-- 相机权限 -->
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- 添加精确位置权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
    <!-- 新增Android 10+后台位置权限 -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <!-- 增加网络变化监听权限 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <!-- 唤醒锁权限 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- WiFi锁权限 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    
    <!-- 屏幕状态监听权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    
    <!-- 电池优化权限 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    
    <!-- 系统级别警告窗口权限（可选，用于引导用户设置） -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <!-- Android 13+ 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" tools:targetApi="33" />
    <!-- Android 14+ 精确闹钟权限 -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" tools:targetApi="33" />

    <!-- 应用更新相关权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />

    <application
        android:name=".RepairOrderApp"
        android:allowBackup="false"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/app_logo"
        android:label="@string/app_name"
        android:roundIcon="@drawable/app_logo"
        android:supportsRtl="true"
        android:theme="@style/Theme.RepairOrderApp"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="31"
        tools:replace="android:appComponentFactory,android:allowBackup"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory">

        <!-- 腾讯地图API密钥配置 -->
        <meta-data
            android:name="TencentMapSDK"           
            android:value="QBJBZ-LRFY3-EXH36-3QBZW-KYUZK-ZCFFI" />
            <!-- android:value="S5IBZ-FX6LT-YSNXC-L7SKN-ZXJJQ-PLFZL" /> -->

        <!-- 禁用腾讯地图动态插件，避免MIUI限制可写Dex -->
        <meta-data
            android:name="TencentMapSDKPluginEnable"
            android:value="false" />

        <!-- 登录页面 -->
        <activity
            android:name=".ui.login.LoginActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize">
        </activity>

        <!-- 维修报告详情页面 -->
        <activity
            android:name=".ui.report.RepairReportDetailActivity"
            android:exported="false" />

        <!-- 主页面 -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustPan"
            android:label="@string/app_name">
        </activity>

        <!-- 签名页面 -->
        <activity
            android:name=".ui.report.SignatureActivity"
            android:exported="false" />

        <!-- 维修报告填写页面 -->
        <activity
            android:name=".ui.report.RepairReportEditActivity"
            android:exported="false" />

        <!-- 零件选择页面 -->
        <activity
            android:name=".ui.orders.PartSelectActivity"
            android:exported="false" />

        <!-- 位置选择页面 -->
        <activity
            android:name=".ui.customer.LocationPickerActivity"
            android:exported="false"
            android:theme="@style/Theme.RepairOrderApp"
            android:windowSoftInputMode="adjustNothing" />

        <!-- 知识库筛选页面 -->
        <activity
            android:name=".ui.learn.LearnFilterActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustNothing" />

        <!-- 知识库详情页面 -->
        <activity
            android:name=".ui.learn.LearnDetailActivity"
            android:exported="false" />

        <!-- 案例详情页面 -->
        <activity
            android:name=".ui.learn.CaseDetailActivity"
            android:exported="false" />

        <activity
            android:name=".ui.setting.KeepAliveGuideActivity"
            android:exported="false"
            android:label="后台运行设置" />

        <!-- 位置更新服务 -->
        <service
            android:name=".service.LocationUpdateService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location" />

        <!-- 全局异常监控服务 -->
        <service
            android:name=".service.GlobalExceptionMonitorService"
            android:enabled="true"
            android:exported="false" />

        <!-- 增加FileProvider配置 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- ZXing扫描Activity - 强制竖屏 -->
        <activity
            android:name="com.journeyapps.barcodescanner.CaptureActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/zxing_CaptureTheme"
            tools:replace="android:screenOrientation" />

        <!-- 修改启动活动为SplashActivity -->
        <activity
            android:name=".ui.launch.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.RepairOrderApp.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 开机自启位置服务 -->
        <receiver
            android:name=".service.BootCompletedReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <!-- FileProvider for APK installation -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>

    </application>

</manifest>