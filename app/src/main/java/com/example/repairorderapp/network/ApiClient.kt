package com.example.repairorderapp.network

import android.content.Context
import android.util.Log
import com.example.repairorderapp.BuildConfig
import com.example.repairorderapp.network.service.LoginService
import com.example.repairorderapp.network.service.WorkOrderService
import com.example.repairorderapp.network.service.LearnService
import com.example.repairorderapp.network.TokenInterceptor
import com.example.repairorderapp.network.GlobalRetrofitProxy
import okhttp3.*
import okhttp3.ResponseBody.Companion.toResponseBody
import okhttp3.logging.HttpLoggingInterceptor
import okio.Buffer
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.IOException
import java.security.cert.CertificateException
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager

/**
 * API客户端单例，用于管理Retrofit和OkHttp实例
 * 处理网络请求、错误重试、令牌管理等功能
 */
object ApiClient {
    private const val TAG = "ApiClient"

    // 服务器地址配置
    // const val BASE_URL = "https://whole.benyin.ltd/"// 本印生产服务器地址
    // const val BASE_URL = "http://192.168.0.112:8080/" // 本地服务器地址
    // const val BASE_URL = "http://192.168.0.121:8080/" // 测试完整版
    // const val BASE_URL = "http://192.168.0.121:8082/"    // 测试简版
    // const val BASE_URL = "https://benyin.hightop.xin/" // 测试服务器地址
    const val BASE_URL = "https://plat.sczjzy.com.cn/" // 生产服务器地址

    // 网络请求超时时间
    private const val TIMEOUT = 30L

    // 服务器地址变更检测
    private const val PREF_SERVER_URL = "server_url_pref"
    private const val KEY_LAST_SERVER_URL = "last_server_url"

    // 创建一个信任所有证书的TrustManager
    private val trustAllCerts = arrayOf<TrustManager>(object : X509TrustManager {
        @Throws(CertificateException::class)
        override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {
        }

        @Throws(CertificateException::class)
        override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {
        }

        override fun getAcceptedIssuers(): Array<X509Certificate> {
            return arrayOf()
        }
    })

    // 添加一个拦截器来确保我们可以多次读取响应体
    private class BufferingInterceptor : Interceptor {
        @Throws(IOException::class)
        override fun intercept(chain: Interceptor.Chain): Response {
            val originalResponse = chain.proceed(chain.request())

            // 读取并存储响应体
            val originalBody = originalResponse.body
            val bodyString = originalBody?.string() ?: ""

            // 创建一个新的响应体，包含相同的数据
            val responseBody = bodyString.toResponseBody(originalBody?.contentType())

            // 返回包含复制的响应体的响应
            return originalResponse.newBuilder()
                .body(responseBody)
                .build()
        }
    }

    // 添加网络错误处理拦截器
    private class NetworkErrorInterceptor : Interceptor {
        @Throws(IOException::class)
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()

            try {
                Log.d(TAG, "发送请求: ${request.url}")
                return chain.proceed(request)
            } catch (e: IOException) {
                Log.e(TAG, "网络请求错误: ${e.message}", e)
                throw e
            }
        }
    }

    // 添加重试拦截器
    private class RetryInterceptor(private val maxRetries: Int = 2) : Interceptor {
        @Throws(IOException::class)
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            var response: Response? = null
            var exception: IOException? = null

            var retryCount = 0
            while (retryCount < maxRetries) {
                try {
                    // 如果不是第一次尝试，需要克隆请求，因为请求体可能只能读取一次
                    val retryRequest = if (retryCount > 0) request.newBuilder().build() else request

                    Log.d(TAG, "尝试请求 ${retryCount + 1}/${maxRetries + 1}: ${request.url}")
                    response = chain.proceed(retryRequest)

                    // 如果请求成功，直接返回
                    if (response.isSuccessful) {
                        return response
                    }

                    // 如果是身份验证问题，不重试
                    if (response.code == 401) {
                        return response
                    }

                    // 关闭响应（否则会有资源泄露）
                    response.close()

                    retryCount++
                    Log.w(TAG, "请求失败，HTTP状态码: ${response.code}，准备重试(${retryCount}/${maxRetries})")

                    // 延迟一段时间再重试
                    Thread.sleep(1000L * retryCount)
                } catch (e: IOException) {
                    exception = e
                    Log.e(TAG, "网络异常: ${e.message}，准备重试(${retryCount + 1}/${maxRetries})", e)
                    retryCount++

                    // 延迟一段时间再重试
                    try {
                        Thread.sleep(1000L * retryCount)
                    } catch (ie: InterruptedException) {
                        // 忽略中断异常
                    }
                } catch (e: Exception) {
                    // 其他异常不重试
                    Log.e(TAG, "非IO异常，不重试: ${e.message}", e)
                    throw IOException("网络请求异常: ${e.message}", e)
                }
            }

            // 如果所有重试都失败，抛出最后捕获的异常，或者返回最后的响应
            if (response != null) {
                return response
            } else if (exception != null) {
                throw exception
            } else {
                throw IOException("请求失败，原因未知")
            }
        }
    }

    val okHttpClient by lazy {
        // 只在调试模式下启用日志
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = if (BuildConfig.DEBUG) {
                HttpLoggingInterceptor.Level.BODY
            } else {
                HttpLoggingInterceptor.Level.NONE
            }
        }

        // 创建SSL上下文
        val sslContext = SSLContext.getInstance("SSL")
        sslContext.init(null, trustAllCerts, java.security.SecureRandom())

        // 创建客户端 - 简化版本，只保留必要的拦截器
        OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .addInterceptor(NetworkErrorInterceptor())  // 网络错误处理
            .addInterceptor(GlobalExceptionInterceptor())  // 全局异常拦截器
            .addInterceptor(com.example.repairorderapp.network.RetryInterceptor())  // 智能重试逻辑
            .addInterceptor(TokenInterceptor())
            .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(TIMEOUT, TimeUnit.SECONDS)
            .sslSocketFactory(sslContext.socketFactory, trustAllCerts[0] as X509TrustManager)
            .hostnameVerifier { _, _ -> true }
            .cache(null) // 禁用 HTTP 缓存，避免服务器切换时的缓存问题
            .build()
    }

    val retrofit by lazy {
        Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    // 预定义的服务实例（自动添加全局异常处理）
    val loginService: LoginService by lazy<LoginService> {
        val originalService = retrofit.create(LoginService::class.java)
        GlobalRetrofitProxy.createProxy(originalService, LoginService::class.java)
    }

    val workOrderService: WorkOrderService by lazy<WorkOrderService> {
        val originalService = retrofit.create(WorkOrderService::class.java)
        GlobalRetrofitProxy.createProxy(originalService, WorkOrderService::class.java)
    }

    val learnService: LearnService by lazy<LearnService> {
        val originalService = retrofit.create(LearnService::class.java)
        GlobalRetrofitProxy.createProxy(originalService, LearnService::class.java)
    }

    val updateService: com.example.repairorderapp.update.api.UpdateApi by lazy<com.example.repairorderapp.update.api.UpdateApi> {
        val originalService = retrofit.create(com.example.repairorderapp.update.api.UpdateApi::class.java)
        GlobalRetrofitProxy.createProxy(originalService, com.example.repairorderapp.update.api.UpdateApi::class.java)
    }

    // 创建任意服务 - 类对象版本（自动添加全局异常处理）
    fun <T> createService(serviceClass: Class<T>): T {
        val originalService = retrofit.create(serviceClass)
        return GlobalRetrofitProxy.createProxy(originalService, serviceClass)
    }

    // 创建任意服务 - Kotlin reified 泛型版本（自动添加全局异常处理）
    inline fun <reified T> createService(): T {
        val originalService = retrofit.create(T::class.java)
        return GlobalRetrofitProxy.createProxy(originalService, T::class.java)
    }

    // 创建原始服务（不带全局异常处理，用于特殊情况）
    fun <T> createRawService(serviceClass: Class<T>): T {
        return retrofit.create(serviceClass)
    }

    // 创建原始服务 - Kotlin reified 泛型版本
    inline fun <reified T> createRawService(): T {
        return retrofit.create(T::class.java)
    }

    /**
     * 更新基础URL
     * 当需要切换环境时使用此方法
     */
    @Volatile
    private var instance: Retrofit? = null

    fun updateBaseUrl(newBaseUrl: String) {
        synchronized(this) {
            // 创建新的Retrofit实例，使用当前的OkHttpClient
            instance = Retrofit.Builder()
                .baseUrl(newBaseUrl)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
        }
    }

    /**
     * 获取带有自定义认证令牌的Retrofit实例
     */
    fun getInstanceWithAuth(authToken: String): Retrofit {
        // 创建新的OkHttpClient并添加认证头
        val client = okHttpClient.newBuilder()
            .addInterceptor { chain ->
                val original = chain.request()
                val request = original.newBuilder()
                    .header("X-Auth-Token", authToken)
                    .build()
                chain.proceed(request)
            }
            .build()

        // 返回新的Retrofit实例
        return Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(client)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    /**
     * 检查服务器地址是否变更，如果变更则清理相关缓存
     * 应在应用启动时调用
     */
    fun checkAndHandleServerChange(context: Context) {
        try {
            val prefs = context.getSharedPreferences(PREF_SERVER_URL, Context.MODE_PRIVATE)
            val lastServerUrl = prefs.getString(KEY_LAST_SERVER_URL, "")

            if (lastServerUrl != BASE_URL) {
                Log.i(TAG, "检测到服务器地址变更: $lastServerUrl -> $BASE_URL")

                // 清理所有相关缓存
                clearServerRelatedCache(context)

                // 保存新的服务器地址
                prefs.edit().putString(KEY_LAST_SERVER_URL, BASE_URL).apply()

                Log.i(TAG, "服务器地址变更处理完成")
            } else {
                Log.d(TAG, "服务器地址未变更: $BASE_URL")

                // 即使服务器地址未变更，也检查是否需要清理网络状态
                checkAndClearNetworkStateIfNeeded(context)
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查服务器地址变更失败", e)
        }
    }

    /**
     * 检查是否需要清理网络状态（覆盖安装检测）
     */
    private fun checkAndClearNetworkStateIfNeeded(context: Context) {
        try {
            val prefs = context.getSharedPreferences(PREF_SERVER_URL, Context.MODE_PRIVATE)
            val lastAppVersion = prefs.getString("last_app_version", "")
            val currentAppVersion = getCurrentAppVersion(context)

            // 检测应用版本变化或首次启动
            if (lastAppVersion != currentAppVersion) {
                Log.i(TAG, "检测到应用版本变化或首次启动: $lastAppVersion -> $currentAppVersion")

                // 清理网络相关状态，但保留用户数据
                clearNetworkStateOnly(context)

                // 保存当前版本
                prefs.edit().putString("last_app_version", currentAppVersion).apply()

                Log.i(TAG, "网络状态清理完成")
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查网络状态清理失败", e)
        }
    }

    /**
     * 获取当前应用版本
     */
    private fun getCurrentAppVersion(context: Context): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            "${packageInfo.versionName}_${packageInfo.versionCode}"
        } catch (e: Exception) {
            "unknown"
        }
    }

    /**
     * 清理服务器相关的所有缓存
     */
    private fun clearServerRelatedCache(context: Context) {
        try {
            Log.i(TAG, "开始清理服务器相关缓存...")

            // 1. 清理 Token 相关缓存
            clearTokenCache(context)

            // 2. 清理 OkHttp 连接池和 DNS 缓存
            clearOkHttpCache()

            // 3. 清理其他网络相关缓存
            clearNetworkCache(context)

            Log.i(TAG, "服务器相关缓存清理完成")
        } catch (e: Exception) {
            Log.e(TAG, "清理服务器相关缓存失败", e)
        }
    }

    /**
     * 清理 Token 缓存
     */
    private fun clearTokenCache(context: Context) {
        try {
            // 清理 token_pref
            val tokenPrefs = context.getSharedPreferences("token_pref", Context.MODE_PRIVATE)
            tokenPrefs.edit().clear().apply()

            // 清理 SharedPrefsManager 中的认证信息
            val appPrefs = context.getSharedPreferences("repair_order_app_prefs", Context.MODE_PRIVATE)
            appPrefs.edit()
                .remove("auth_token")
                .putBoolean("login_status", false)
                .apply()

            Log.d(TAG, "Token 缓存已清理")
        } catch (e: Exception) {
            Log.e(TAG, "清理 Token 缓存失败", e)
        }
    }

    /**
     * 清理 OkHttp 连接池和调度器（优化版本）
     */
    private fun clearOkHttpCache() {
        try {
            // 1. 清理连接池中的所有连接
            okHttpClient.connectionPool.evictAll()

            // 2. 取消调度器中的所有任务
            okHttpClient.dispatcher.cancelAll()

            // 3. 等待一小段时间确保清理完成
            Thread.sleep(100)

            Log.d(TAG, "OkHttp 连接池和调度器已清理")
        } catch (e: Exception) {
            Log.e(TAG, "清理 OkHttp 缓存失败", e)
        }
    }

    /**
     * 清理其他网络相关缓存
     */
    private fun clearNetworkCache(context: Context) {
        try {
            // 清理更新相关缓存
            val updatePrefs = context.getSharedPreferences("app_update_prefs", Context.MODE_PRIVATE)
            updatePrefs.edit().clear().apply()

            Log.d(TAG, "网络相关缓存已清理")
        } catch (e: Exception) {
            Log.e(TAG, "清理网络相关缓存失败", e)
        }
    }

    /**
     * 仅清理网络状态，不清理用户数据（用于覆盖安装场景）
     */
    private fun clearNetworkStateOnly(context: Context) {
        try {
            Log.i(TAG, "开始清理网络状态...")

            // 1. 清理 OkHttp 连接池和 DNS 缓存
            clearOkHttpCache()

            // 2. 清理网络相关缓存（但不清理 Token）
            val updatePrefs = context.getSharedPreferences("app_update_prefs", Context.MODE_PRIVATE)
            updatePrefs.edit().clear().apply()

            Log.i(TAG, "网络状态清理完成")
        } catch (e: Exception) {
            Log.e(TAG, "清理网络状态失败", e)
        }
    }

    /**
     * 检查网络是否就绪（简化版本）
     */
    fun isNetworkReady(context: Context): Boolean {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
            val activeNetwork = connectivityManager.activeNetwork ?: return false
            val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork) ?: return false

            // 简化检查：有网络连接且已验证即可
            networkCapabilities.hasCapability(android.net.NetworkCapabilities.NET_CAPABILITY_VALIDATED)
        } catch (e: Exception) {
            Log.e(TAG, "检查网络状态失败", e)
            // 网络检查失败时返回 true，让后续重试机制处理
            true
        }
    }
}