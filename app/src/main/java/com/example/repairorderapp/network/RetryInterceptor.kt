package com.example.repairorderapp.network

import android.util.Log
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.util.concurrent.TimeUnit

/**
 * 智能重试拦截器
 * 基于错误类型和HTTP状态码实现智能重试策略
 */
class RetryInterceptor(
    private val maxRetries: Int = 3,
    private val baseDelayMs: Long = 1000L,
    private val maxDelayMs: Long = 30000L
) : Interceptor {

    companion object {
        private const val TAG = "RetryInterceptor"
        
        // 可重试的HTTP状态码
        private val RETRYABLE_STATUS_CODES = setOf(
            408, // Request Timeout
            429, // Too Many Requests
            500, // Internal Server Error
            502, // Bad Gateway
            503, // Service Unavailable
            504  // Gateway Timeout
        )
        
        // 不可重试的HTTP状态码
        private val NON_RETRYABLE_STATUS_CODES = setOf(
            400, // Bad Request
            401, // Unauthorized
            403, // Forbidden
            404, // Not Found
            422  // Unprocessable Entity
        )
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        var response: Response? = null
        var exception: IOException? = null
        
        // 检查请求是否标记为不重试
        val noRetryHeader = request.header("X-No-Retry")
        if (noRetryHeader == "true") {
            return chain.proceed(request)
        }

        for (attempt in 0..maxRetries) {
            try {
                response?.close() // 关闭之前的响应
                response = chain.proceed(request)
                
                // 检查响应是否成功或不可重试
                if (response.isSuccessful || !shouldRetryForStatusCode(response.code)) {
                    return response
                }
                
                Log.w(TAG, "HTTP错误 ${response.code}，尝试 ${attempt + 1}/${maxRetries + 1}")
                
                if (attempt < maxRetries) {
                    val delay = calculateDelay(attempt)
                    Log.d(TAG, "等待 ${delay}ms 后重试")
                    Thread.sleep(delay)
                }
                
            } catch (e: IOException) {
                exception = e
                Log.w(TAG, "网络异常: ${e.message}，尝试 ${attempt + 1}/${maxRetries + 1}")
                
                // 检查是否为不可重试的异常
                if (!shouldRetryForException(e)) {
                    Log.e(TAG, "不可重试的异常: ${e.javaClass.simpleName}")
                    throw e
                }
                
                if (attempt < maxRetries) {
                    val delay = calculateDelay(attempt)
                    Log.d(TAG, "等待 ${delay}ms 后重试")
                    Thread.sleep(delay)
                }
            }
        }

        // 所有重试都失败了
        response?.let { 
            Log.e(TAG, "所有重试失败，最后状态码: ${it.code}")
            return it 
        }
        
        exception?.let { 
            Log.e(TAG, "所有重试失败，最后异常: ${it.message}")
            throw it 
        }
        
        throw IOException("未知错误：重试失败")
    }

    /**
     * 根据HTTP状态码判断是否应该重试
     */
    private fun shouldRetryForStatusCode(statusCode: Int): Boolean {
        return when {
            statusCode in NON_RETRYABLE_STATUS_CODES -> false
            statusCode in RETRYABLE_STATUS_CODES -> true
            statusCode >= 500 -> true // 5xx错误通常可以重试
            else -> false
        }
    }

    /**
     * 根据异常类型判断是否应该重试
     */
    private fun shouldRetryForException(exception: IOException): Boolean {
        return when (exception) {
            is SocketTimeoutException -> true  // 超时可以重试
            is UnknownHostException -> true    // DNS解析失败可以重试（处理启动时网络未就绪）
            else -> true // 其他IO异常可以重试
        }
    }

    /**
     * 计算重试延迟时间（指数退避 + 抖动）
     */
    private fun calculateDelay(attempt: Int): Long {
        // 指数退避：baseDelay * 2^attempt
        val exponentialDelay = baseDelayMs * (1L shl attempt)
        
        // 添加抖动（±25%）
        val jitter = (exponentialDelay * 0.25 * (Math.random() - 0.5)).toLong()
        val delayWithJitter = exponentialDelay + jitter
        
        // 限制最大延迟时间
        return minOf(delayWithJitter, maxDelayMs)
    }
}
