package com.example.repairorderapp.repository

import android.content.Context
import android.util.Log
import com.example.repairorderapp.model.PagedData
import com.example.repairorderapp.model.statistics.MonthTimelinessQuery
import com.example.repairorderapp.model.statistics.MonthTimelinessVO
import com.example.repairorderapp.model.statistics.TimelinessDetailVO
import com.example.repairorderapp.model.statistics.TimelinessDetailsQuery
import com.example.repairorderapp.network.ApiClient
import com.example.repairorderapp.data.api.StatisticsApi
import com.example.repairorderapp.util.ApiResult

/**
 * 时效统计数据仓库
 * 负责处理时效统计相关的数据获取和缓存
 */
class StatisticsRepository(context: Context) : BaseNetworkRepository(context) {
    
    companion object {
        private const val TAG = "StatisticsRepository"
    }
    
    private val api = ApiClient.createService<StatisticsApi>()
    
    /**
     * 获取月度统计数据
     *
     * @param query 查询参数
     * @return 包含分页数据的API结果
     */
    suspend fun getMonthStatistics(
        query: MonthTimelinessQuery
    ): ApiResult<PagedData<MonthTimelinessVO>> {
        logRequest("获取月度统计数据", "页码: ${query.pageNumber}, 页大小: ${query.pageSize}")

        return when (val result = executeWithRetry(
            request = {
                api.getMonthTimelinessList(query)
            },
            metricName = "statistics_month_list",
            checkNetworkFirst = true
        )) {
            is ApiResult.Success -> {
                val apiResponse = result.data
                val data = apiResponse.data
                if (data != null) {
                    logSuccess(
                        "获取月度统计数据",
                        data.rows?.size ?: 0,
                        "总计: ${data.total}条, 当前页: ${data.pageNumber}/${data.total / data.pageSize + 1}"
                    )
                    ApiResult.Success(data)
                } else {
                    val errorMessage = apiResponse.message ?: "数据为空"
                    logError("获取月度统计数据", errorMessage)
                    ApiResult.error(
                        exception = IllegalStateException("API response data is null"),
                        message = errorMessage
                    )
                }
            }
            is ApiResult.Error -> {
                logError("获取月度统计数据", result.message)
                result
            }
            is ApiResult.Loading -> result
        }
    }
    
    /**
     * 获取时效明细数据
     *
     * @param query 查询参数
     * @return 包含分页数据的API结果
     */
    suspend fun getTimelinessDetails(
        query: TimelinessDetailsQuery
    ): ApiResult<PagedData<TimelinessDetailVO>> {
        logRequest(
            "获取时效明细数据",
            "工程师ID: ${query.engineerId}, 月份: ${query.monthly}, 页码: ${query.pageNumber}"
        )

        return when (val result = executeWithRetry(
            request = {
                api.getTimelinessDetailsList(query)
            },
            metricName = "statistics_detail_list",
            checkNetworkFirst = true
        )) {
            is ApiResult.Success -> {
                val apiResponse = result.data
                val data = apiResponse.data
                if (data != null) {
                    logSuccess(
                        "获取时效明细数据",
                        data.rows?.size ?: 0,
                        "总计: ${data.total}条, 当前页: ${data.pageNumber}/${data.total / data.pageSize + 1}"
                    )
                    ApiResult.Success(data)
                } else {
                    val errorMessage = apiResponse.message ?: "数据为空"
                    logError("获取时效明细数据", errorMessage)
                    ApiResult.error(
                        exception = IllegalStateException("API response data is null"),
                        message = errorMessage
                    )
                }
            }
            is ApiResult.Error -> {
                logError("获取时效明细数据", result.message)
                result
            }
            is ApiResult.Loading -> result
        }
    }
    
    /**
     * 获取月度统计数据（带缓存）
     * 
     * @param query 查询参数
     * @param forceRefresh 是否强制刷新，忽略缓存
     * @return 包含分页数据的API结果
     */
    suspend fun getMonthStatisticsWithCache(
        query: MonthTimelinessQuery,
        forceRefresh: Boolean = false
    ): ApiResult<PagedData<MonthTimelinessVO>> {
        // 如果是第一页且不强制刷新，可以考虑使用缓存
        // 这里暂时直接调用网络请求，后续可以根据需要添加缓存逻辑
        return getMonthStatistics(query)
    }
    
    /**
     * 获取时效明细数据（带缓存）
     * 
     * @param query 查询参数
     * @param forceRefresh 是否强制刷新，忽略缓存
     * @return 包含分页数据的API结果
     */
    suspend fun getTimelinessDetailsWithCache(
        query: TimelinessDetailsQuery,
        forceRefresh: Boolean = false
    ): ApiResult<PagedData<TimelinessDetailVO>> {
        // 如果是第一页且不强制刷新，可以考虑使用缓存
        // 这里暂时直接调用网络请求，后续可以根据需要添加缓存逻辑
        return getTimelinessDetails(query)
    }
    
    /**
     * 清除统计数据缓存
     * 当数据可能发生变化时调用，比如用户刷新或者筛选条件改变
     */
    fun clearStatisticsCache() {
        Log.d(TAG, "清除统计数据缓存")
        // 这里可以实现具体的缓存清除逻辑
        // 比如清除SharedPreferences或Room数据库中的缓存数据
    }
    
    /**
     * 预加载下一页数据
     * 用于提升用户体验，在用户滚动到列表底部前预先加载数据
     *
     * @param query 查询参数
     */
    suspend fun preloadNextPage(query: MonthTimelinessQuery): ApiResult<PagedData<MonthTimelinessVO>> {
        val nextPageQuery = query.copy(pageNumber = query.pageNumber + 1)
        Log.d(TAG, "预加载下一页数据: 页码 ${nextPageQuery.pageNumber}")

        return when (val result = executeWithRetry(
            request = {
                api.getMonthTimelinessList(nextPageQuery)
            },
            metricName = "statistics_preload",
            checkNetworkFirst = false // 预加载时不检查网络，避免阻塞
        )) {
            is ApiResult.Success -> {
                val apiResponse = result.data
                val data = apiResponse.data
                if (data != null) {
                    ApiResult.Success(data)
                } else {
                    ApiResult.error(
                        exception = IllegalStateException("Preload data is null"),
                        message = apiResponse.message ?: "预加载数据为空"
                    )
                }
            }
            is ApiResult.Error -> result
            is ApiResult.Loading -> result
        }
    }
    
    /**
     * 批量获取多个月份的统计数据
     * 用于生成报表或趋势分析
     *
     * @param engineerIds 工程师ID列表
     * @param months 月份列表
     * @return 批量查询结果
     */
    suspend fun getBatchStatistics(
        engineerIds: List<Long>,
        months: List<String>
    ): List<ApiResult<PagedData<MonthTimelinessVO>>> {
        Log.d(TAG, "批量获取统计数据: ${engineerIds.size}个工程师, ${months.size}个月份")

        val results = mutableListOf<ApiResult<PagedData<MonthTimelinessVO>>>()

        // 为每个月份创建查询
        months.forEach { month ->
            val query = MonthTimelinessQuery(
                pageNumber = 1,             // 页码从1开始
                pageSize = 100,             // 批量查询时使用较大的页面大小
                startMonth = month,
                endMonth = month
            )

            val result = when (val apiResult = executeWithRetry(
                request = { api.getMonthTimelinessList(query) },
                metricName = "statistics_batch"
            )) {
                is ApiResult.Success -> {
                    val apiResponse = apiResult.data
                    val data = apiResponse.data
                    if (data != null) {
                        ApiResult.Success(data)
                    } else {
                        ApiResult.error(
                            exception = IllegalStateException("Batch query data is null"),
                            message = apiResponse.message ?: "批量查询数据为空"
                        )
                    }
                }
                is ApiResult.Error -> apiResult
                is ApiResult.Loading -> apiResult
            }

            results.add(result)
        }

        return results
    }
}
