package com.example.repairorderapp.repository

import android.content.Context
import com.example.repairorderapp.config.UploadConfig
import com.example.repairorderapp.data.api.LogConfigApi
import com.example.repairorderapp.data.model.*
import com.example.repairorderapp.util.ApiResult
import com.example.repairorderapp.util.onError
import com.example.repairorderapp.util.onSuccess

/**
 * 日志上传仓库
 * 负责所有日志相关的网络操作，基于BaseNetworkRepository提供统一的错误处理和重试机制
 */
class LogUploadRepository(
    context: Context,
    private val api: LogConfigApi,
    config: UploadConfig = UploadConfig.default()
) : BaseNetworkRepository(context, config) {
    
    companion object {
        private const val TAG = "LogUploadRepository"
    }
    
    /**
     * 上传日志数据
     */
    suspend fun uploadLogs(request: LogUploadRequest): ApiResult<LogUploadResponse> {
        logRequest("上传日志", "数量: ${request.logs.size}")
        
        return executeWithRetry(
            request = { api.uploadLogs("application/json", request) },
            enablePerformanceMonitoring = false  // 禁用性能监控，减少日志频率
        ).onSuccess { response: LogUploadResponse ->
            logSuccess("上传日志", request.logs.size, "响应: ${response.message}")
        }.onError { error: ApiResult.Error ->
            logError("上传日志", error.message, "请求数量: ${request.logs.size}")
        }
    }
    
    /**
     * 批量上传日志（按类型分组）
     */
    suspend fun uploadLogsByType(
        logType: String,
        logs: List<LogEntry>,
        deviceId: String,
        appVersion: String
    ): ApiResult<LogUploadResponse> {
        if (logs.isEmpty()) {
            return ApiResult.success(LogUploadResponse(code = 200, message = "无日志需要上传"))
        }
        
        val request = LogUploadRequest(
            deviceId = deviceId,
            appVersion = appVersion,
            logs = logs
        )
        
        logRequest("上传${logType}日志", "数量: ${logs.size}")
        
        return executeWithRetry(
            request = { api.uploadLogs("application/json", request) },
            enablePerformanceMonitoring = false  // 禁用性能监控，减少日志频率
        ).onSuccess { response: LogUploadResponse ->
            logSuccess("上传${logType}日志", logs.size, "响应: ${response.message}")
        }.onError { error: ApiResult.Error ->
            logError("上传${logType}日志", error.message, "数量: ${logs.size}")
        }
    }
    
    /**
     * 批量上传多种类型的日志
     */
    suspend fun uploadLogsBatch(
        logsByType: Map<String, List<LogEntry>>,
        deviceId: String,
        appVersion: String
    ): Map<String, ApiResult<LogUploadResponse>> {
        val results = mutableMapOf<String, ApiResult<LogUploadResponse>>()
        
        logRequest("批量上传日志", "类型数: ${logsByType.size}")
        
        for ((logType, logs) in logsByType) {
            if (logs.isNotEmpty()) {
                val result = uploadLogsByType(logType, logs, deviceId, appVersion)
                results[logType] = result
            }
        }
        
        val successCount = results.values.count { it.isSuccess }
        val totalCount = results.size
        
        if (successCount == totalCount) {
            logSuccess("批量上传日志", totalCount, "全部成功")
        } else {
            logError("批量上传日志", "部分失败", "$successCount/$totalCount 成功")
        }
        
        return results
    }
    
    /**
     * 上传崩溃信息
     */
    suspend fun uploadCrashInfo(crashInfoList: List<CrashInfo>): ApiResult<com.example.repairorderapp.data.model.ApiResponse<Unit>> {
        if (crashInfoList.isEmpty()) {
            return ApiResult.success(com.example.repairorderapp.data.model.ApiResponse(code = 200, message = "无崩溃信息需要上传", data = Unit))
        }
        
        logRequest("上传崩溃信息", "数量: ${crashInfoList.size}")
        
        // 如果只有一条崩溃信息，使用单条上传接口
        val result = if (crashInfoList.size == 1) {
            val request = CrashInfoUploadRequest(crashes = crashInfoList)
            val apiResult = executeWithRetry(
                request = { api.uploadCrashInfo(request) }
            )

            // 转换API响应格式
            when (apiResult) {
                is ApiResult.Success -> {
                    val apiResponse = apiResult.data
                    ApiResult.success(
                        com.example.repairorderapp.data.model.ApiResponse(
                            code = apiResponse.code,
                            message = apiResponse.message,
                            data = Unit
                        )
                    )
                }
                is ApiResult.Error -> apiResult
                is ApiResult.Loading -> apiResult
            }
        } else {
            // 多条崩溃信息，分批上传
            uploadCrashInfoBatch(crashInfoList)
        }

        return result.onSuccess { response: com.example.repairorderapp.data.model.ApiResponse<Unit> ->
            logSuccess("上传崩溃信息", crashInfoList.size, "响应: ${response.message}")
        }.onError { error: ApiResult.Error ->
            logError("上传崩溃信息", error.message, "数量: ${crashInfoList.size}")
        }
    }
    
    /**
     * 分批上传崩溃信息
     */
    private suspend fun uploadCrashInfoBatch(crashInfoList: List<CrashInfo>): ApiResult<com.example.repairorderapp.data.model.ApiResponse<Unit>> {
        val batchSize = config.crashBatchSize
        val batches = crashInfoList.chunked(batchSize)
        
        logRequest("分批上传崩溃信息", "总数: ${crashInfoList.size}, 批次: ${batches.size}")
        
        var successCount = 0
        var lastError: ApiResult.Error? = null
        
        for ((index, batch) in batches.withIndex()) {
            logRequest("上传崩溃信息批次", "第${index + 1}/${batches.size}批, 数量: ${batch.size}")
            
            for (crashInfo in batch) {
                val request = CrashInfoUploadRequest(crashes = listOf(crashInfo))
                val result = executeWithRetry(
                    request = { api.uploadCrashInfo(request) }
                )

                when (result) {
                    is ApiResult.Success<*> -> {
                        successCount++
                    }
                    is ApiResult.Error -> {
                        lastError = result
                        logError("上传单条崩溃信息", result.message, "ID: ${crashInfo.id}")
                    }
                    else -> {
                        // Loading状态不应该出现在这里
                    }
                }
            }
        }
        
        return if (successCount == crashInfoList.size) {
            ApiResult.success(com.example.repairorderapp.data.model.ApiResponse(code = 200, message = "所有崩溃信息上传成功", data = Unit))
        } else {
            lastError ?: ApiResult.error(
                exception = IllegalStateException("部分崩溃信息上传失败"),
                message = "成功: $successCount/${crashInfoList.size}"
            )
        }
    }
    
    /**
     * 上传设备信息
     */
    suspend fun uploadDeviceInfo(deviceInfo: DeviceInfo): ApiResult<com.example.repairorderapp.data.model.ApiResponse<Unit>> {
        logRequest("上传设备信息", "设备ID: ${deviceInfo.deviceId}")

        val uploadDto = deviceInfo.toUploadFormat()

        val apiResult = executeWithRetry(
            request = { api.uploadDeviceInfo(uploadDto) }
        )

        // 转换API响应格式
        val result = when (apiResult) {
            is ApiResult.Success -> {
                val apiResponse = apiResult.data
                ApiResult.success(
                    com.example.repairorderapp.data.model.ApiResponse(
                        code = apiResponse.code,
                        message = apiResponse.message,
                        data = Unit
                    )
                )
            }
            is ApiResult.Error -> apiResult
            is ApiResult.Loading -> apiResult
        }

        return result.onSuccess { response: com.example.repairorderapp.data.model.ApiResponse<Unit> ->
            logSuccess("上传设备信息", details = "设备: ${deviceInfo.getDeviceIdentifier()}")
        }.onError { error: ApiResult.Error ->
            logError("上传设备信息", error.message, "设备: ${deviceInfo.getDeviceIdentifier()}")
        }
    }
    
    /**
     * 获取日志配置
     * 🔧 修复：处理包装在ApiResponse中的响应数据
     */
    suspend fun getLogConfig(
        userId: String? = null,
        deviceId: String? = null,
        appVersion: String? = null
    ): ApiResult<LogConfigResponse> {
        logRequest("获取日志配置", "用户: $userId, 设备: $deviceId")

        val result = executeRequest {
            api.getLogConfig(userId, deviceId, appVersion)
        }

        return when (result) {
            is ApiResult.Success -> {
                val apiResponse = result.data
                if (apiResponse.isSuccess() && apiResponse.data != null) {
                    ApiResult.success(apiResponse.data!!)
                } else {
                    ApiResult.error(
                        exception = IllegalStateException("API response failed"),
                        message = apiResponse.getErrorMessage(),
                        code = apiResponse.code
                    )
                }
            }
            is ApiResult.Error -> result
            is ApiResult.Loading -> result
        }.onSuccess { config ->
            logSuccess("获取日志配置", details = "配置名: ${config.configName}")
        }.onError { error ->
            logError("获取日志配置", error.message)
        }
    }
    

    
    /**
     * 立即上传指定日志（高优先级）
     */
    suspend fun uploadLogsImmediately(
        logs: List<LogEntry>,
        deviceId: String,
        appVersion: String
    ): ApiResult<LogUploadResponse> {
        if (logs.isEmpty()) {
            return ApiResult.success(LogUploadResponse(code = 200, message = "无日志需要立即上传"))
        }
        
        logRequest("立即上传日志", "数量: ${logs.size}")
        
        val request = LogUploadRequest(
            deviceId = deviceId,
            appVersion = appVersion,
            logs = logs
        )
        
        // 立即上传使用更少的重试次数，但更快的响应
        return executeWithRetry(
            request = { api.uploadLogs("application/json", request) },
            maxRetries = 1,
            enablePerformanceMonitoring = false  // 禁用性能监控，减少日志频率
        ).onSuccess { response: LogUploadResponse ->
            logSuccess("立即上传日志", logs.size, "响应: ${response.message}")
        }.onError { error: ApiResult.Error ->
            logError("立即上传日志", error.message, "数量: ${logs.size}")
        }
    }
    
    /**
     * 获取上传统计信息
     */
    fun getUploadStats(): Map<String, Any> {
        return mapOf(
            "networkType" to getNetworkType(),
            "configValid" to config.validate(),
            "batchSize" to config.logBatchSize,
            "retryAttempts" to config.retryAttempts,
            "timeoutSeconds" to config.timeoutSeconds
        )
    }
}
