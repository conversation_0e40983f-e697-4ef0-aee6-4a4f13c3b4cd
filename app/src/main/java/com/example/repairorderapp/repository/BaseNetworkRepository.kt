package com.example.repairorderapp.repository

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.util.Log
import com.example.repairorderapp.config.UploadConfig
import com.example.repairorderapp.performance.PerformanceMonitor
import com.example.repairorderapp.util.ApiResult
import com.example.repairorderapp.util.safeApiCall
import kotlinx.coroutines.delay
import retrofit2.Response

/**
 * 基础网络仓库类
 * 提供统一的网络请求处理、重试机制和错误处理
 */
abstract class BaseNetworkRepository(
    protected val context: Context,
    protected val config: UploadConfig = UploadConfig.default()
) {
    
    companion object {
        const val TAG = "BaseNetworkRepository"
    }
    
    /**
     * 执行带重试机制的网络请求
     */
    protected suspend inline fun <T> executeWithRetry(
        crossinline request: suspend () -> Response<T>,
        maxRetries: Int = config.retryAttempts,
        checkNetworkFirst: Boolean = true,
        metricName: String = "api_request",
        enablePerformanceMonitoring: Boolean = true
    ): ApiResult<T> {
        
        // 检查网络连接
        if (checkNetworkFirst && !isNetworkAvailable()) {
            val networkType = getNetworkType()
            Log.w(TAG, "网络检查失败，当前网络类型: $networkType")
            return ApiResult.error(
                exception = IllegalStateException("No network connection"),
                message = "网络连接不可用，请检查网络设置"
            )
        }
        
        // 检查WiFi限制
        if (config.wifiOnlyUpload && !isWifiConnected()) {
            return ApiResult.error(
                exception = IllegalStateException("WiFi required"),
                message = "当前设置仅允许在WiFi环境下上传"
            )
        }
        
        var lastResult: ApiResult<T>? = null

        // 开始性能监控（如果启用）
        if (enablePerformanceMonitoring) {
            PerformanceMonitor.startTiming(metricName, mapOf(
                "maxRetries" to maxRetries,
                "checkNetworkFirst" to checkNetworkFirst
            ))
        }

        repeat(maxRetries + 1) { attemptNumber ->
            try {
                Log.d(TAG, "执行网络请求，尝试次数: ${attemptNumber + 1}/${maxRetries + 1}")

                val result = safeApiCall { request() }
                
                when (result) {
                    is ApiResult.Success -> {
                        if (attemptNumber > 0) {
                            Log.i(TAG, "网络请求在第${attemptNumber + 1}次尝试后成功")
                        }
                        // 结束性能监控 - 成功
                        if (enablePerformanceMonitoring) {
                            PerformanceMonitor.endTiming(metricName, mapOf(
                                "success" to true,
                                "attempts" to (attemptNumber + 1),
                                "responseSize" to (result.data?.toString()?.length ?: 0)
                            ))
                        }
                        return result
                    }
                    is ApiResult.Error -> {
                        lastResult = result
                        
                        // 判断是否应该重试
                        if (shouldRetry(result, attemptNumber, maxRetries)) {
                            val delayTime = config.getRetryDelay(attemptNumber)
                            Log.w(TAG, "请求失败，${delayTime}ms后进行第${attemptNumber + 2}次尝试: ${result.message}")
                            delay(delayTime)
                        } else {
                            Log.e(TAG, "请求最终失败，不再重试: ${result.message}")
                            // 结束性能监控 - 失败
                            if (enablePerformanceMonitoring) {
                                PerformanceMonitor.endTiming(metricName, mapOf(
                                    "success" to false,
                                    "attempts" to (attemptNumber + 1),
                                    "errorMessage" to result.message
                                ))
                            }
                            return result
                        }
                    }
                    is ApiResult.Loading -> {
                        // 不应该在这里出现Loading状态，跳过此次循环
                        lastResult = ApiResult.error(
                            exception = IllegalStateException("Unexpected Loading state"),
                            message = "意外的Loading状态"
                        )
                    }
                }
                
            } catch (e: Exception) {
                lastResult = ApiResult.fromException(e)
                Log.e(TAG, "网络请求异常，尝试次数: ${attemptNumber + 1}", e)
                
                if (attemptNumber < maxRetries) {
                    val delayTime = config.getRetryDelay(attemptNumber)
                    delay(delayTime)
                }
            }
        }

        // 结束性能监控 - 所有重试都失败
        if (enablePerformanceMonitoring) {
            PerformanceMonitor.endTiming(metricName, mapOf(
                "success" to false,
                "attempts" to (maxRetries + 1),
                "errorMessage" to "所有重试尝试都失败了"
            ))
        }

        return lastResult ?: ApiResult.error(
            exception = IllegalStateException("All retry attempts failed"),
            message = "所有重试尝试都失败了"
        )
    }
    
    /**
     * 执行简单的网络请求（无重试）
     */
    protected suspend inline fun <T> executeRequest(
        crossinline request: suspend () -> Response<T>
    ): ApiResult<T> {
        return safeApiCall { request() }
    }
    
    /**
     * 批量执行网络请求
     */
    protected suspend inline fun <T, R> executeBatch(
        items: List<T>,
        batchSize: Int = config.logBatchSize,
        crossinline request: suspend (List<T>) -> Response<R>
    ): List<ApiResult<R>> {
        val results = mutableListOf<ApiResult<R>>()

        items.chunked(batchSize).forEach { batch ->
            val result = executeWithRetry(
                request = { request(batch) }
            )
            results.add(result)

            // 如果批次失败，可以选择继续或停止
            if (result is ApiResult.Error) {
                Log.w(TAG, "批次上传失败: ${result.message}")
            }
        }

        return results
    }
    
    /**
     * 判断是否应该重试
     */
    protected fun shouldRetry(error: ApiResult.Error, attemptNumber: Int, maxRetries: Int): Boolean {
        // 已达到最大重试次数
        if (attemptNumber >= maxRetries) {
            return false
        }
        
        // 根据错误类型判断是否应该重试
        return when (error.code) {
            // 客户端错误，通常不应该重试
            400, 401, 403, 404 -> false
            // 服务器错误或网络错误，可以重试
            500, 502, 503, 504 -> true
            // 超时错误，可以重试
            408 -> true
            // 其他情况，根据异常类型判断
            else -> when (error.exception) {
                is java.net.SocketTimeoutException -> true
                is java.net.UnknownHostException -> true
                is java.io.IOException -> true
                else -> false
            }
        }
    }
    
    /**
     * 检查网络是否可用
     */
    protected fun isNetworkAvailable(): Boolean {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false

            // 放宽检查条件：只要有网络连接能力即可，不强制要求已验证
            val hasInternet = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            val isValidated = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)

            Log.d(TAG, "网络状态检查: hasInternet=$hasInternet, isValidated=$isValidated")

            // 优先使用已验证的网络，但如果没有验证也允许尝试
            return hasInternet
        } catch (e: Exception) {
            Log.e(TAG, "检查网络状态失败", e)
            false
        }
    }
    
    /**
     * 检查是否连接到WiFi
     */
    protected fun isWifiConnected(): Boolean {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        } catch (e: Exception) {
            Log.e(TAG, "检查WiFi状态失败", e)
            false
        }
    }
    
    /**
     * 获取网络类型描述
     */
    protected fun getNetworkType(): String {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val network = connectivityManager.activeNetwork ?: return "NONE"
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return "UNKNOWN"
            
            when {
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "WIFI"
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "CELLULAR"
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> "ETHERNET"
                else -> "OTHER"
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取网络类型失败", e)
            "UNKNOWN"
        }
    }
    
    /**
     * 记录网络请求日志
     */
    protected fun logRequest(operation: String, details: String = "") {
        Log.d(TAG, "网络操作: $operation ${if (details.isNotEmpty()) "- $details" else ""}")
    }
    
    /**
     * 记录网络请求成功日志
     */
    protected fun logSuccess(operation: String, count: Int = 0, details: String = "") {
        val countStr = if (count > 0) " (${count}条)" else ""
        Log.i(TAG, "✅ $operation 成功$countStr ${if (details.isNotEmpty()) "- $details" else ""}")
    }
    
    /**
     * 记录网络请求失败日志
     */
    protected fun logError(operation: String, error: String, details: String = "") {
        Log.e(TAG, "❌ $operation 失败: $error ${if (details.isNotEmpty()) "- $details" else ""}")
    }
}
