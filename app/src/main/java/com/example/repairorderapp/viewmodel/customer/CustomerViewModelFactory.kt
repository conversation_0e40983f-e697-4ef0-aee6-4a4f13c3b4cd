package com.example.repairorderapp.viewmodel.customer

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.example.repairorderapp.data.repository.CustomerRepository

/**
 * 客户相关ViewModel工厂类
 */
class CustomerViewModelFactory(
    private val customerRepository: CustomerRepository
) : ViewModelProvider.Factory {
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return when {
            modelClass.isAssignableFrom(CustomerListViewModel::class.java) -> {
                CustomerListViewModel(customerRepository) as T
            }
            modelClass.isAssignableFrom(CustomerDetailViewModel::class.java) -> {
                CustomerDetailViewModel(customerRepository) as T
            }
            modelClass.isAssignableFrom(CustomerInfoEditViewModel::class.java) -> {
                CustomerInfoEditViewModel(customerRepository) as T
            }
            modelClass.isAssignableFrom(CustomerDeviceViewModel::class.java) -> {
                CustomerDeviceViewModel(customerRepository) as T
            }
            modelClass.isAssignableFrom(CustomerStaffViewModel::class.java) -> {
                CustomerStaffViewModel(customerRepository) as T
            }
            modelClass.isAssignableFrom(CustomerTagViewModel::class.java) -> {
                CustomerTagViewModel(customerRepository) as T
            }
            modelClass.isAssignableFrom(CustomerVisitViewModel::class.java) -> {
                CustomerVisitViewModel(customerRepository) as T
            }
            modelClass.isAssignableFrom(CustomerContactViewModel::class.java) -> {
                CustomerContactViewModel(customerRepository) as T
            }
            modelClass.isAssignableFrom(CustomerBusinessViewModel::class.java) -> {
                CustomerBusinessViewModel(customerRepository) as T
            }
            modelClass.isAssignableFrom(DeviceMaintenanceViewModel::class.java) -> {
                DeviceMaintenanceViewModel(customerRepository) as T
            }
            modelClass.isAssignableFrom(DeviceReplaceViewModel::class.java) -> {
                DeviceReplaceViewModel(customerRepository) as T
            }
            modelClass.isAssignableFrom(AddDeviceViewModel::class.java) -> {
                AddDeviceViewModel(customerRepository) as T
            }
            else -> throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
        }
    }
} 