package com.example.repairorderapp.viewmodel.customer

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.data.repository.CustomerRepository
import com.example.repairorderapp.model.customer.DeviceMaintenanceRecord
import com.example.repairorderapp.util.Event
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.launch
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

/**
 * 设备维修记录ViewModel
 */
class DeviceMaintenanceViewModel(private val repository: CustomerRepository) : ViewModel() {

    private val _maintenanceRecords = MutableLiveData<List<DeviceMaintenanceRecord>>()
    val maintenanceRecords: LiveData<List<DeviceMaintenanceRecord>> = _maintenanceRecords

    private val _filteredRecords = MutableLiveData<List<DeviceMaintenanceRecord>>()
    val filteredRecords: LiveData<List<DeviceMaintenanceRecord>> = _filteredRecords

    private val _loading = MutableLiveData<Boolean>()
    val loading: LiveData<Boolean> = _loading

    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    private val _refreshing = MutableLiveData<Boolean>()
    val refreshing: LiveData<Boolean> = _refreshing

    private val _selectedRecord = MutableLiveData<Event<DeviceMaintenanceRecord>>()
    val selectedRecord: LiveData<Event<DeviceMaintenanceRecord>> = _selectedRecord
    
    private var allRecords: List<DeviceMaintenanceRecord> = emptyList()
    private var currentSearchQuery: String = ""

    // 滚动位置保存
    var savedScrollPosition: Int = 0
        private set
    var savedScrollOffset: Int = 0
        private set

    fun updateScrollPosition(position: Int, offset: Int) {
        savedScrollPosition = position
        savedScrollOffset = offset
    }

    /**
     * 加载设备维修记录列表
     */
    fun loadDeviceMaintenanceRecords(deviceGroupId: String, isRefresh: Boolean = false) {
        if (isRefresh) {
            _refreshing.value = true
        } else {
            _loading.value = true
        }

        repository.getHistoryWorkOrderList(deviceGroupId).enqueue(object : Callback<ApiResponse<Any>> {
            override fun onResponse(call: Call<ApiResponse<Any>>, response: Response<ApiResponse<Any>>) {
                if (isRefresh) {
                    _refreshing.value = false
                } else {
                    _loading.value = false
                }

                if (response.isSuccessful) {
                    response.body()?.let { apiResponse ->
                        if (apiResponse.code == 200) {
                            try {
                                // 使用Gson将Any类型转换为DeviceMaintenanceRecord列表
                                val gson = Gson()
                                val json = gson.toJson(apiResponse.data)
                                val type = object : TypeToken<List<DeviceMaintenanceRecord>>() {}.type
                                val records = gson.fromJson<List<DeviceMaintenanceRecord>>(json, type)
                                allRecords = records
                                _maintenanceRecords.value = records
                                
                                // 应用当前搜索条件
                                applySearchFilter(currentSearchQuery)
                            } catch (e: Exception) {
                                _error.value = "数据解析失败: ${e.message}"
                            }
                        } else {
                            _error.value = apiResponse.message ?: "请求失败"
                        }
                    } ?: run {
                        _error.value = "返回数据为空"
                    }
                } else {
                    _error.value = "请求失败: ${response.code()}"
                }
            }

            override fun onFailure(call: Call<ApiResponse<Any>>, t: Throwable) {
                if (isRefresh) {
                    _refreshing.value = false
                } else {
                    _loading.value = false
                }
                _error.value = "网络错误: ${t.message}"
            }
        })
    }

    /**
     * 选中维修记录
     */
    fun selectRecord(record: DeviceMaintenanceRecord) {
        _selectedRecord.value = Event(record)
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _error.value = ""
    }
    
    /**
     * 搜索维修记录
     */
    fun searchRecords(query: String) {
        currentSearchQuery = query
        applySearchFilter(query)
    }
    
    /**
     * 应用搜索过滤器
     */
    private fun applySearchFilter(query: String) {
        if (query.isBlank()) {
            _filteredRecords.value = allRecords
        } else {
            val filteredList = allRecords.filter { record ->
                searchInRecord(record, query.lowercase())
            }
            _filteredRecords.value = filteredList
        }
    }
    
    /**
     * 在记录中搜索关键词
     */
    private fun searchInRecord(record: DeviceMaintenanceRecord, query: String): Boolean {
        val searchFields = mutableListOf<String>()
        
        // 添加故障描述和解决方案
        record.repairReport?.let { report ->
            searchFields.add(report.excDesc ?: "")
            searchFields.add(report.resolveDesc ?: "")
        }
        
        // 在所有字段中搜索
        return searchFields.any { field ->
            field.lowercase().contains(query)
        }
    }
} 