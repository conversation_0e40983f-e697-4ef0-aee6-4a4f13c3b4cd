package com.example.repairorderapp.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.repairorderapp.model.statistics.DetailFilterState
import com.example.repairorderapp.model.statistics.DetailSortType
import com.example.repairorderapp.model.statistics.TimelinessDetailVO
import com.example.repairorderapp.model.statistics.TimelinessDetailsQuery
import com.example.repairorderapp.repository.StatisticsRepository
import com.example.repairorderapp.util.ApiResult
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 时效统计明细页面ViewModel
 * 负责管理某工程师某月的详细工单时效数据
 */
class StatisticsDetailViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        private const val TAG = "StatisticsDetailViewModel"
        private const val PAGE_SIZE = 100 // 明细页面页面大小，与统计页面保持一致
        private const val SEARCH_DELAY = 500L // 搜索防抖延迟
    }

    private val repository = StatisticsRepository(application)
    
    // UI状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _isRefreshing = MutableLiveData<Boolean>()
    val isRefreshing: LiveData<Boolean> = _isRefreshing
    
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage
    
    private val _showEmpty = MutableLiveData<Boolean>()
    val showEmpty: LiveData<Boolean> = _showEmpty
    
    // 数据
    private val _detailsList = MutableLiveData<List<TimelinessDetailVO>>()
    val detailsList: LiveData<List<TimelinessDetailVO>> = _detailsList
    
    // 工程师信息
    private val _engineerName = MutableLiveData<String>()
    val engineerName: LiveData<String> = _engineerName
    
    private val _monthly = MutableLiveData<String>()
    val monthly: LiveData<String> = _monthly
    
    // 分页状态
    private var currentPage = 1
    private var isLastPage = false
    private var isLoadingMore = false
    private var totalCount = 0
    
    // 查询参数
    private var engineerId: Long = 0
    private var monthlyValue: String = ""
    
    // 筛选和搜索状态
    var filterState = DetailFilterState()
        private set
    private var searchJob: Job? = null
    
    // 公开的分页状态
    val isLastPagePublic: Boolean get() = isLastPage
    val isLoadingMorePublic: Boolean get() = isLoadingMore
    val currentPagePublic: Int get() = currentPage
    val totalCountPublic: Int get() = totalCount
    
    init {
        Log.d(TAG, "StatisticsDetailViewModel 初始化")
    }
    
    /**
     * 初始化数据
     * @param engineerId 工程师ID
     * @param monthly 月份
     * @param engineerName 工程师名称（可选，用于显示）
     */
    fun initialize(engineerId: Long, monthly: String, engineerName: String? = null) {
        Log.d(TAG, "初始化数据: engineerId=$engineerId, monthly=$monthly, engineerName=$engineerName")
        
        this.engineerId = engineerId
        this.monthlyValue = monthly
        
        _engineerName.value = engineerName ?: "工程师"
        _monthly.value = monthly
        
        loadDetails(refresh = true)
    }
    
    /**
     * 加载明细数据
     * @param refresh 是否为刷新操作
     */
    fun loadDetails(refresh: Boolean = false) {
        Log.d(TAG, "加载明细数据: refresh=$refresh, currentPage=$currentPage")
        
        if (engineerId == 0L || monthlyValue.isEmpty()) {
            Log.w(TAG, "缺少必要参数: engineerId=$engineerId, monthly=$monthlyValue")
            _errorMessage.value = "参数错误，请重新进入页面"
            return
        }
        
        if (refresh) {
            currentPage = 1
            isLastPage = false
            isLoadingMore = false
            _isRefreshing.value = true
            _errorMessage.value = ""
        } else {
            if (isLoadingMore || isLastPage) {
                Log.d(TAG, "跳过加载: isLoadingMore=$isLoadingMore, isLastPage=$isLastPage")
                return
            }
            _isLoading.value = true
        }
        
        viewModelScope.launch {
            try {
                val query = TimelinessDetailsQuery(
                    pageNumber = currentPage,
                    pageSize = PAGE_SIZE,
                    engineerId = engineerId,
                    monthly = monthlyValue,
                    code = filterState.workOrderCode,
                    name = filterState.customerName
                )
                
                Log.d(TAG, "发起API请求: $query")
                
                when (val result = repository.getTimelinessDetails(query)) {
                    is ApiResult.Success -> {
                        val pageData = result.data
                        val newList = pageData.rows ?: emptyList()

                        Log.d(TAG, "API请求成功: 获取到${newList.size}条数据, 总计${pageData.total}条")

                        // 更新数据
                        val updatedList = if (refresh) {
                            newList
                        } else {
                            val currentList = _detailsList.value ?: emptyList()
                            currentList + newList
                        }

                        // 应用排序（仅在刷新时，避免分页加载时重复排序）
                        val finalList = if (refresh && updatedList.isNotEmpty()) {
                            applySortToList(updatedList, filterState.sortBy)
                        } else {
                            updatedList
                        }

                        _detailsList.value = finalList

                        // 更新分页状态
                        totalCount = pageData.total
                        val totalPages = if (pageData.pageSize > 0) {
                            (pageData.total + pageData.pageSize - 1) / pageData.pageSize
                        } else {
                            1
                        }
                        isLastPage = currentPage >= totalPages || newList.isEmpty()

                        if (!isLastPage) {
                            currentPage++
                        }

                        // 更新空状态
                        _showEmpty.value = refresh && newList.isEmpty()

                        Log.d(TAG, "数据更新完成: currentPage=$currentPage, isLastPage=$isLastPage, totalCount=$totalCount")
                    }

                    is ApiResult.Error -> {
                        Log.e(TAG, "API请求失败: ${result.message}", result.exception)
                        _errorMessage.value = result.message

                        if (refresh) {
                            _showEmpty.value = true
                        }
                    }

                    is ApiResult.Loading -> {
                        // Loading状态已经在上面处理了
                        Log.d(TAG, "API请求加载中...")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载明细数据异常", e)
                _errorMessage.value = e.message ?: "加载失败，请重试"
                
                if (refresh) {
                    _showEmpty.value = true
                }
            } finally {
                _isLoading.value = false
                _isRefreshing.value = false
                isLoadingMore = false
            }
        }
    }
    
    /**
     * 加载更多数据
     */
    fun loadMore() {
        if (isLoadingMore || isLastPage) {
            Log.d(TAG, "跳过加载更多: isLoadingMore=$isLoadingMore, isLastPage=$isLastPage")
            return
        }
        
        Log.d(TAG, "加载更多数据")
        isLoadingMore = true
        loadDetails(refresh = false)
    }
    
    /**
     * 搜索功能
     * @param workOrderCode 工单编号
     * @param customerName 客户名称
     */
    fun search(workOrderCode: String?, customerName: String?) {
        Log.d(TAG, "搜索: workOrderCode=$workOrderCode, customerName=$customerName")
        
        // 取消之前的搜索任务
        searchJob?.cancel()
        
        searchJob = viewModelScope.launch {
            // 防抖延迟
            delay(SEARCH_DELAY)
            
            val trimmedCode = workOrderCode?.trim()
            val trimmedName = customerName?.trim()
            
            val searchCode = if (trimmedCode.isNullOrBlank()) null else trimmedCode
            val searchName = if (trimmedName.isNullOrBlank()) null else trimmedName
            
            if (filterState.workOrderCode != searchCode || filterState.customerName != searchName) {
                filterState = filterState.copy(
                    workOrderCode = searchCode,
                    customerName = searchName
                )
                repository.clearStatisticsCache()
                loadDetails(refresh = true)
            }
        }
    }
    
    /**
     * 清除搜索条件
     */
    fun clearSearch() {
        Log.d(TAG, "清除搜索条件")
        
        searchJob?.cancel()
        
        if (filterState.workOrderCode != null || filterState.customerName != null) {
            filterState = filterState.copy(
                workOrderCode = null,
                customerName = null
            )
            repository.clearStatisticsCache()
            loadDetails(refresh = true)
        }
    }
    
    /**
     * 重试加载
     */
    fun retry() {
        Log.d(TAG, "重试加载")
        _errorMessage.value = ""
        loadDetails(refresh = true)
    }
    
    /**
     * 应用筛选条件
     * @param newFilterState 新的筛选状态
     */
    fun applyFilter(newFilterState: DetailFilterState) {
        Log.d(TAG, "应用筛选条件: $newFilterState")

        filterState = newFilterState
        repository.clearStatisticsCache() // 清除缓存
        loadDetails(refresh = true) // 排序会在loadDetails中自动应用
    }

    /**
     * 获取当前筛选状态
     */
    fun getCurrentFilterState(): DetailFilterState {
        return filterState
    }
    
    /**
     * 对列表应用排序
     * @param list 要排序的列表
     * @param sortType 排序类型
     * @return 排序后的列表
     */
    private fun applySortToList(list: List<TimelinessDetailVO>, sortType: DetailSortType?): List<TimelinessDetailVO> {
        return when (sortType) {
            null -> list // 默认排序，保持原始顺序
            DetailSortType.RECEIVE_TIME_ASC -> list.sortedBy { it.receiveTime }
            DetailSortType.RECEIVE_TIME_DESC -> list.sortedByDescending { it.receiveTime }
            DetailSortType.ON_ROAD_TIME_ASC -> list.sortedBy { it.onRoadTime }
            DetailSortType.ON_ROAD_TIME_DESC -> list.sortedByDescending { it.onRoadTime }
            DetailSortType.REPAIR_TIME_ASC -> list.sortedBy { it.repairTime }
            DetailSortType.REPAIR_TIME_DESC -> list.sortedByDescending { it.repairTime }
            DetailSortType.EVALUATE_SCORE_ASC -> list.sortedBy {
                (it.professionalEvaluate + it.serviceEvaluate) / 2.0
            }
            DetailSortType.EVALUATE_SCORE_DESC -> list.sortedByDescending {
                (it.professionalEvaluate + it.serviceEvaluate) / 2.0
            }
        }
    }

    /**
     * 排序数据
     * @param sortType 排序类型
     */
    fun sortData(sortType: DetailSortType?) {
        Log.d(TAG, "排序数据: $sortType")

        val currentList = _detailsList.value ?: return
        val sortedList = applySortToList(currentList, sortType)

        _detailsList.value = sortedList
        filterState = filterState.copy(sortBy = sortType)
    }
    
    /**
     * 获取统计摘要信息
     * @return 包含总工单数、平均时间等信息的Map
     */
    fun getStatisticsSummary(): Map<String, Any> {
        val currentList = _detailsList.value ?: emptyList()
        
        if (currentList.isEmpty()) {
            return emptyMap()
        }
        
        val totalOrders = currentList.size
        val avgReceiveTime = currentList.map { it.receiveTime }.average()
        val avgRepairTime = currentList.map { it.repairTime }.average()
        val avgConfirmTime = currentList.map { it.confirmTime }.average()
        val avgProfessionalScore = currentList.map { it.professionalEvaluate }.average()
        val avgServiceScore = currentList.map { it.serviceEvaluate }.average()
        
        return mapOf(
            "totalOrders" to totalOrders,
            "avgReceiveTime" to avgReceiveTime,
            "avgRepairTime" to avgRepairTime,
            "avgConfirmTime" to avgConfirmTime,
            "avgProfessionalScore" to avgProfessionalScore,
            "avgServiceScore" to avgServiceScore
        )
    }
    
    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "StatisticsDetailViewModel 清理")
        searchJob?.cancel()
    }
}
