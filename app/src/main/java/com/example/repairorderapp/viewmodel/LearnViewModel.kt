package com.example.repairorderapp.viewmodel

import android.app.Application
import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.repairorderapp.data.repository.LearnRepository
import com.example.repairorderapp.model.*
import com.example.repairorderapp.util.SharedPrefsManager
import kotlinx.coroutines.*

/**
 * 知识库ViewModel
 */
class LearnViewModel(
    private val learnRepository: LearnRepository,
    private val sharedPrefsManager: SharedPrefsManager
) : ViewModel() {
    
    // UI状态
    private val _uiState = MutableLiveData<LearnUiState>()
    val uiState: LiveData<LearnUiState> = _uiState
    
    // 知识库列表数据
    private val _learnItems = MutableLiveData<List<LearnItem>>()
    val learnItems: LiveData<List<LearnItem>> = _learnItems
    
    // 搜索建议
    private val _hotWords = MutableLiveData<List<LearnHotWord>>()
    val hotWords: LiveData<List<LearnHotWord>> = _hotWords
    
    // 筛选选项
    private val _filterOptions = MutableLiveData<FilterOptions>()
    val filterOptions: LiveData<FilterOptions> = _filterOptions
    
    // 是否显示搜索建议
    private val _showSuggestions = MutableLiveData<Boolean>()
    val showSuggestions: LiveData<Boolean> = _showSuggestions
    
    // 搜索参数
    private val searchParams = LearnSearchParams()
    private var isFirstLoad = true
    private var totalCount = 0
    private var searchJob: Job? = null
    
    init {
        _uiState.value = LearnUiState.Loading
        initializeData()
    }
    
    /**
     * 初始化数据
     */
    private fun initializeData() {
        viewModelScope.launch {
            // 恢复保存的搜索条件
            restoreSavedParams()
            
            // 加载初始数据
            loadLearnData(true)
            
//            // 记录页面访问
//            recordPageView()
        }
    }
    
    /**
     * 恢复保存的搜索参数
     */
    private fun restoreSavedParams() {
        // 恢复搜索关键词
        learnRepository.getSavedSearchKeywords()?.let { keywords ->
            searchParams.keyword = keywords
        }
        
        // 恢复筛选参数
        val (productList, typeList) = learnRepository.getSavedFilterParams()
        searchParams.productList = productList
        searchParams.type = typeList
    }
    
    /**
     * 搜索知识库
     */
    fun searchLearn(keyword: String = "", resetPage: Boolean = true) {
        if (resetPage) {
            searchParams.pageNumber = 1
            searchParams.keyword = keyword
            isFirstLoad = true
            
            // 保存搜索关键词
            if (keyword.isNotEmpty()) {
                learnRepository.saveSearchKeywords(keyword)
            }
        }
        
        loadLearnData(resetPage)
    }
    
    /**
     * 加载更多数据
     */
    fun loadMore() {
        if (_uiState.value is LearnUiState.LoadingMore || 
            _uiState.value is LearnUiState.Loading) {
            return
        }
        
        val currentItems = _learnItems.value ?: emptyList()
        if (currentItems.size >= totalCount) {
            return
        }
        
        searchParams.pageNumber++
        _uiState.value = LearnUiState.LoadingMore
        loadLearnData(false)
    }
    
    /**
     * 刷新数据
     */
    fun refresh() {
        searchParams.pageNumber = 1
        isFirstLoad = true
        loadLearnData(true)
    }
    
    /**
     * 加载知识库数据
     */
    private fun loadLearnData(isRefresh: Boolean) {
        viewModelScope.launch {
            try {
                if (isRefresh) {
                    _uiState.value = LearnUiState.Loading
                }
                
                val result = learnRepository.getLearnPage(searchParams)
                
                result.fold(
                    onSuccess = { pageData ->
                        totalCount = pageData.total
                        
                        val newItems = pageData.rows
                        val currentItems = if (isFirstLoad) {
                            newItems
                        } else {
                            val existing = _learnItems.value ?: emptyList()
                            existing + newItems
                        }
                        
                        _learnItems.value = currentItems
                        _uiState.value = if (currentItems.isEmpty()) {
                            LearnUiState.Empty
                        } else {
                            LearnUiState.Success
                        }
                        
                        isFirstLoad = false
                    },
                    onFailure = { exception ->
                        _uiState.value = LearnUiState.Error(exception.message ?: "加载失败")
                    }
                )
            } catch (e: Exception) {
                _uiState.value = LearnUiState.Error(e.message ?: "网络错误")
            }
        }
    }
    
    /**
     * 搜索热词建议
     */
    fun searchHotWords(keyword: String) {
        // 取消之前的搜索
        searchJob?.cancel()
        
        if (keyword.isBlank()) {
            _hotWords.value = emptyList()
            _showSuggestions.value = false
            return
        }
        
        searchJob = viewModelScope.launch {
            try {
                val result = learnRepository.getHotWords(keyword)
                result.fold(
                    onSuccess = { hotWords ->
                        _hotWords.value = hotWords
                        _showSuggestions.value = hotWords.isNotEmpty()
                    },
                    onFailure = {
                        _hotWords.value = emptyList()
                        _showSuggestions.value = false
                    }
                )
            } catch (e: Exception) {
                _hotWords.value = emptyList()
                _showSuggestions.value = false
            }
        }
    }
    
    /**
     * 隐藏搜索建议
     */
    fun hideSuggestions() {
        _showSuggestions.value = false
    }
    
    /**
     * 应用筛选条件
     */
    fun applyFilter(productList: List<String>, typeList: List<String>) {
        searchParams.productList = productList
        searchParams.type = typeList
        searchParams.pageNumber = 1
        isFirstLoad = true
        
        // 保存筛选参数
        learnRepository.saveFilterParams(productList, typeList)
        
        loadLearnData(true)
    }
    
    /**
     * 清除筛选条件
     */
    fun clearFilter() {
        searchParams.productList = emptyList()
        searchParams.type = emptyList()
        searchParams.pageNumber = 1
        isFirstLoad = true
        
        // 清除保存的筛选参数
        learnRepository.clearAllKnowledgeBaseState()
        
        loadLearnData(true)
    }
    
    /**
     * 检查是否有筛选条件
     */
    fun hasFilter(): Boolean {
        return searchParams.productList.isNotEmpty() || searchParams.type.isNotEmpty()
    }
    
    /**
     * 获取当前搜索关键词
     */
    fun getCurrentKeyword(): String {
        return searchParams.keyword
    }
    
    /**
     * 检查登录状态
     */
    fun isLoggedIn(): Boolean {
        // 使用SharedPrefsManager的getAuthToken方法
        return sharedPrefsManager.getAuthToken().isNotEmpty()
    }
    
    /**
     * 记录页面访问统计
     */
    private fun recordPageView() {
        viewModelScope.launch {
            learnRepository.recordPageView("/pages/learn/index")
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        searchJob?.cancel()
    }
}

/**
 * 知识库UI状态
 */
sealed class LearnUiState {
    object Loading : LearnUiState()
    object LoadingMore : LearnUiState()
    object Success : LearnUiState()
    object Empty : LearnUiState()
    data class Error(val message: String) : LearnUiState()
}

/**
 * 筛选选项
 */
data class FilterOptions(
    val types: List<FilterOption> = emptyList(),
    val models: List<FilterOption> = emptyList()
) 