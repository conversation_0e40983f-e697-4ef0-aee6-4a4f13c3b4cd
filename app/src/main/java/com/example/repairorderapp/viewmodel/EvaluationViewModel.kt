package com.example.repairorderapp.viewmodel

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.repairorderapp.data.api.WorkOrderApi
import com.example.repairorderapp.network.ApiClient
import com.example.repairorderapp.model.EvaluationItem
import com.example.repairorderapp.model.RatingFilter
import com.example.repairorderapp.model.WorkOrderEvaluationDetail
import com.example.repairorderapp.ui.orders.WorkOrderItem
import com.example.repairorderapp.util.await
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

@kotlinx.coroutines.ExperimentalCoroutinesApi
class EvaluationViewModel : ViewModel() {

    private val api = ApiClient.createService(WorkOrderApi::class.java)

    // UI状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _isRefreshing = MutableLiveData<Boolean>()
    val isRefreshing: LiveData<Boolean> = _isRefreshing

    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    // 数据
    private val _evaluationList = MutableLiveData<List<EvaluationItem>>()
    val evaluationList: LiveData<List<EvaluationItem>> = _evaluationList

    private val _filteredList = MutableLiveData<List<EvaluationItem>>()
    val filteredList: LiveData<List<EvaluationItem>> = _filteredList

    // 筛选和搜索状态
    private val _currentFilter = MutableLiveData<RatingFilter>(RatingFilter.ALL)
    val currentFilter: LiveData<RatingFilter> = _currentFilter

    private val _searchQuery = MutableLiveData<String>("")
    val searchQuery: LiveData<String> = _searchQuery

    // 原始数据
    private var allEvaluations: List<EvaluationItem> = emptyList()

    init {
        loadEvaluations()
    }

    /**
     * 加载评价列表
     */
    fun loadEvaluations(refresh: Boolean = false) {
        if (refresh) {
            _isRefreshing.value = true
        } else {
            _isLoading.value = true
        }

        viewModelScope.launch {
            try {
                // 第一步：获取已评价的工单列表
                val evaluatedWorkOrders = getEvaluatedWorkOrders()
                
                if (evaluatedWorkOrders.isNotEmpty()) {
                    // 第二步：并发获取每个工单的评价详情
                    val evaluationItems = getEvaluationDetails(evaluatedWorkOrders)
                    
                    // 更新数据
                    allEvaluations = evaluationItems
                    _evaluationList.value = evaluationItems
                    
                    // 应用当前筛选条件
                    applyFilters()
                } else {
                    allEvaluations = emptyList()
                    _evaluationList.value = emptyList()
                    _filteredList.value = emptyList()
                }
                
            } catch (e: Exception) {
                Log.e("EvaluationViewModel", "加载评价列表失败", e)
                _errorMessage.value = e.message ?: "加载失败，请重试"
            } finally {
                _isLoading.value = false
                _isRefreshing.value = false
            }
        }
    }

    /**
     * 获取已评价的工单列表
     */
    private suspend fun getEvaluatedWorkOrders(): List<WorkOrderItem> {
        return withContext(Dispatchers.IO) {
            try {
                val response = api.getWorkOrderList(
                    mapOf(
                        "pageSize" to "50", // 增加每页数量，减少分页请求
                        "pageNumber" to "1",
                        "isEvaluated" to "true"
                    )
                ).await()

                if (response.isSuccessful && response.body()?.code == 200) {
                    response.body()?.data?.rows ?: emptyList()
                } else {
                    Log.w("EvaluationViewModel", "获取工单列表失败: ${response.body()?.message}")
                    emptyList()
                }
            } catch (e: Exception) {
                Log.e("EvaluationViewModel", "获取工单列表异常", e)
                emptyList()
            }
        }
    }

    /**
     * 并发获取评价详情
     */
    private suspend fun getEvaluationDetails(workOrders: List<WorkOrderItem>): List<EvaluationItem> {
        val evaluationItems = mutableListOf<EvaluationItem>()
        
        // 并发请求所有工单详情
        val deferredList = workOrders.map { workOrder ->
            viewModelScope.async(Dispatchers.IO) {
                try {
                    val response = api.getWorkOrderEvaluationDetail(workOrder.id).await()
                    if (response.isSuccessful && response.body()?.code == 200) {
                        val detail = response.body()?.data
                        // 创建评价列表项
                        if (detail != null) {
                            EvaluationItem.fromWorkOrderAndDetail(workOrder, detail)
                        } else {
                            null
                        }
                    } else {
                        Log.w("EvaluationViewModel", "获取工单详情失败: ${response.body()?.message}")
                        null
                    }
                } catch (e: Exception) {
                    Log.e("EvaluationViewModel", "获取工单详情异常: ${workOrder.id}", e)
                    null
                }
            }
        }
        
        // 等待所有请求完成
        val results = deferredList.awaitAll()
        
        // 过滤掉null值
        evaluationItems.addAll(results.filterNotNull())
        
        return evaluationItems.sortedByDescending { it.evaluationTime } // 按评价时间倒序排列
    }

    /**
     * 设置评分筛选
     */
    fun setRatingFilter(filter: RatingFilter) {
        _currentFilter.value = filter
        applyFilters()
    }

    /**
     * 设置搜索关键词
     */
    fun setSearchQuery(query: String) {
        _searchQuery.value = query
        applyFilters()
    }

    /**
     * 清除搜索
     */
    fun clearSearch() {
        _searchQuery.value = ""
        applyFilters()
    }

    /**
     * 应用筛选条件
     */
    private fun applyFilters() {
        val currentFilterValue = _currentFilter.value ?: RatingFilter.ALL
        val currentSearchQuery = _searchQuery.value ?: ""
        
        var filteredList = allEvaluations
        
        // 应用评分筛选
        if (currentFilterValue != RatingFilter.ALL) {
            filteredList = filteredList.filter { evaluation ->
                currentFilterValue.matches(evaluation.averageRating)
            }
        }
        
        // 应用搜索筛选
        if (currentSearchQuery.isNotBlank()) {
            filteredList = filteredList.filter { evaluation ->
                evaluation.orderCode.contains(currentSearchQuery, ignoreCase = true) ||
                evaluation.customerName.contains(currentSearchQuery, ignoreCase = true) ||
                evaluation.machineModel.contains(currentSearchQuery, ignoreCase = true) ||
                evaluation.engineerName.contains(currentSearchQuery, ignoreCase = true)
            }
        }
        
        _filteredList.value = filteredList
    }

    /**
     * 刷新数据
     */
    fun refresh() {
        loadEvaluations(refresh = true)
    }

    /**
     * 获取当前筛选条件描述
     */
    fun getCurrentFilterDescription(): String {
        val filter = _currentFilter.value ?: RatingFilter.ALL
        return "当前：${filter.label}"
    }
} 