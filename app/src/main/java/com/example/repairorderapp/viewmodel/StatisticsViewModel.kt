package com.example.repairorderapp.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.repairorderapp.model.statistics.MonthTimelinessQuery
import com.example.repairorderapp.model.statistics.MonthTimelinessVO
import com.example.repairorderapp.model.statistics.SortType
import com.example.repairorderapp.model.statistics.StatisticsFilterState
import com.example.repairorderapp.repository.StatisticsRepository
import com.example.repairorderapp.util.ApiResult
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 时效统计主页面ViewModel
 * 负责管理月度统计数据的加载、筛选、搜索等功能
 */
class StatisticsViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        private const val TAG = "StatisticsViewModel"
        private const val PAGE_SIZE = 100 // 增加页面大小，便于前端排序
        private const val SEARCH_DELAY = 500L // 搜索防抖延迟
    }

    private val repository = StatisticsRepository(application)
    
    // UI状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _isRefreshing = MutableLiveData<Boolean>()
    val isRefreshing: LiveData<Boolean> = _isRefreshing
    
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage
    
    private val _showEmpty = MutableLiveData<Boolean>()
    val showEmpty: LiveData<Boolean> = _showEmpty
    
    // 数据
    private val _statisticsList = MutableLiveData<List<MonthTimelinessVO>>()
    val statisticsList: LiveData<List<MonthTimelinessVO>> = _statisticsList
    
    // 分页状态
    private var currentPage = 1
    private var isLastPage = false
    private var isLoadingMore = false
    private var totalCount = 0
    
    // 筛选和搜索状态
    private var filterState = StatisticsFilterState()
    private var searchJob: Job? = null
    
    // 公开的分页状态
    val isLastPagePublic: Boolean get() = isLastPage
    val isLoadingMorePublic: Boolean get() = isLoadingMore
    val currentPagePublic: Int get() = currentPage
    val totalCountPublic: Int get() = totalCount
    
    init {
        Log.d(TAG, "StatisticsViewModel 初始化")
    }
    
    /**
     * 加载统计数据
     * @param refresh 是否为刷新操作
     */
    fun loadStatistics(refresh: Boolean = false) {
        Log.d(TAG, "加载统计数据: refresh=$refresh, currentPage=$currentPage")
        
        if (refresh) {
            currentPage = 1
            isLastPage = false
            isLoadingMore = false
            _isRefreshing.value = true
            _errorMessage.value = ""
        } else {
            if (isLoadingMore || isLastPage) {
                Log.d(TAG, "跳过加载: isLoadingMore=$isLoadingMore, isLastPage=$isLastPage")
                return
            }
            _isLoading.value = true
        }
        
        viewModelScope.launch {
            try {
                val query = MonthTimelinessQuery(
                    pageNumber = currentPage,
                    pageSize = PAGE_SIZE,
                    startMonth = filterState.startMonth,
                    endMonth = filterState.endMonth,
                    name = filterState.engineerName
                )
                
                Log.d(TAG, "发起API请求: $query")
                
                when (val result = repository.getMonthStatistics(query)) {
                    is ApiResult.Success -> {
                        val pageData = result.data
                        val newList = pageData.rows ?: emptyList()

                        Log.d(TAG, "API请求成功: 获取到${newList.size}条数据, 总计${pageData.total}条")

                        // 更新数据
                        val updatedList = if (refresh) {
                            newList
                        } else {
                            val currentList = _statisticsList.value ?: emptyList()
                            currentList + newList
                        }

                        // 应用排序（仅在刷新时，避免分页加载时重复排序）
                        val finalList = if (refresh && updatedList.isNotEmpty()) {
                            applySortToList(updatedList, filterState.sortBy)
                        } else {
                            updatedList
                        }

                        _statisticsList.value = finalList

                        // 更新分页状态
                        totalCount = pageData.total
                        val totalPages = if (pageData.pageSize > 0) {
                            (pageData.total + pageData.pageSize - 1) / pageData.pageSize
                        } else {
                            1
                        }
                        isLastPage = currentPage >= totalPages || newList.isEmpty()

                        if (!isLastPage) {
                            currentPage++
                        }

                        // 更新空状态
                        _showEmpty.value = refresh && finalList.isEmpty()

                        Log.d(TAG, "数据更新完成: currentPage=$currentPage, isLastPage=$isLastPage, totalCount=$totalCount")
                    }

                    is ApiResult.Error -> {
                        Log.e(TAG, "API请求失败: ${result.message}", result.exception)
                        _errorMessage.value = result.message

                        if (refresh) {
                            _showEmpty.value = true
                        }
                    }

                    is ApiResult.Loading -> {
                        // Loading状态已经在上面处理了
                        Log.d(TAG, "API请求加载中...")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载统计数据异常", e)
                _errorMessage.value = e.message ?: "加载失败，请重试"
                
                if (refresh) {
                    _showEmpty.value = true
                }
            } finally {
                _isLoading.value = false
                _isRefreshing.value = false
                isLoadingMore = false
            }
        }
    }
    
    /**
     * 加载更多数据
     */
    fun loadMore() {
        if (isLoadingMore || isLastPage) {
            Log.d(TAG, "跳过加载更多: isLoadingMore=$isLoadingMore, isLastPage=$isLastPage")
            return
        }
        
        Log.d(TAG, "加载更多数据")
        isLoadingMore = true
        loadStatistics(refresh = false)
    }
    
    /**
     * 应用筛选条件
     * @param newFilterState 新的筛选状态
     */
    fun applyFilter(newFilterState: StatisticsFilterState) {
        Log.d(TAG, "应用筛选条件: $newFilterState")

        filterState = newFilterState
        repository.clearStatisticsCache() // 清除缓存
        loadStatistics(refresh = true) // 排序会在loadStatistics中自动应用
    }
    
    /**
     * 搜索工程师
     * @param name 工程师名称
     */
    fun searchEngineer(name: String) {
        Log.d(TAG, "搜索工程师: $name")
        
        // 取消之前的搜索任务
        searchJob?.cancel()
        
        searchJob = viewModelScope.launch {
            // 防抖延迟
            delay(SEARCH_DELAY)
            
            val trimmedName = name.trim()
            val searchName = if (trimmedName.isBlank()) null else trimmedName
            
            if (filterState.engineerName != searchName) {
                filterState = filterState.copy(engineerName = searchName)
                repository.clearStatisticsCache()
                loadStatistics(refresh = true)
            }
        }
    }
    
    /**
     * 清除搜索条件
     */
    fun clearSearch() {
        Log.d(TAG, "清除搜索条件")
        
        searchJob?.cancel()
        
        if (filterState.engineerName != null) {
            filterState = filterState.copy(engineerName = null)
            repository.clearStatisticsCache()
            loadStatistics(refresh = true)
        }
    }
    
    /**
     * 重试加载
     */
    fun retry() {
        Log.d(TAG, "重试加载")
        _errorMessage.value = ""
        loadStatistics(refresh = true)
    }
    
    /**
     * 获取当前筛选状态
     */
    fun getCurrentFilterState(): StatisticsFilterState {
        return filterState
    }
    
    /**
     * 对列表应用排序
     * @param list 要排序的列表
     * @param sortType 排序类型
     * @return 排序后的列表
     */
    private fun applySortToList(list: List<MonthTimelinessVO>, sortType: SortType?): List<MonthTimelinessVO> {
        return when (sortType) {
            null -> list // 默认排序，保持原始顺序
            SortType.MONTHLY_ASC -> list.sortedBy { it.monthly }
            SortType.MONTHLY_DESC -> list.sortedByDescending { it.monthly }
            SortType.ORDER_COUNT_ASC -> list.sortedBy { it.orderNums }
            SortType.ORDER_COUNT_DESC -> list.sortedByDescending { it.orderNums }
            SortType.AVG_REPAIR_TIME_ASC -> list.sortedBy { it.repairTimeAvg }
            SortType.AVG_REPAIR_TIME_DESC -> list.sortedByDescending { it.repairTimeAvg }
        }
    }

    /**
     * 排序数据
     * @param sortType 排序类型
     */
    fun sortData(sortType: SortType?) {
        Log.d(TAG, "排序数据: $sortType")

        val currentList = _statisticsList.value ?: return
        val sortedList = applySortToList(currentList, sortType)

        _statisticsList.value = sortedList
        filterState = filterState.copy(sortBy = sortType)
    }
    
    /**
     * 预加载下一页数据
     * 在用户滚动到列表底部前预先加载，提升用户体验
     */
    fun preloadNextPage() {
        if (isLastPage || isLoadingMore) return
        
        Log.d(TAG, "预加载下一页数据")
        
        viewModelScope.launch {
            try {
                val query = MonthTimelinessQuery(
                    pageNumber = currentPage,
                    pageSize = PAGE_SIZE,
                    startMonth = filterState.startMonth,
                    endMonth = filterState.endMonth,
                    name = filterState.engineerName
                )
                
                repository.preloadNextPage(query)
                Log.d(TAG, "预加载完成")
            } catch (e: Exception) {
                Log.w(TAG, "预加载失败", e)
                // 预加载失败不影响主流程，只记录日志
            }
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "StatisticsViewModel 清理")
        searchJob?.cancel()
    }
}
