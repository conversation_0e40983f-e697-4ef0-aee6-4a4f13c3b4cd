package com.example.repairorderapp.viewmodel.customer

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.repairorderapp.data.repository.CustomerRepository
import com.example.repairorderapp.model.customer.CustomerTag
import com.example.repairorderapp.model.customer.Tag
import com.example.repairorderapp.util.Event
import kotlinx.coroutines.launch

/**
 * 客户标签管理ViewModel
 */
class CustomerTagViewModel(
    private val customerRepository: CustomerRepository
) : ViewModel() {
    
    private val _tagInfo = MutableLiveData<CustomerTag>()
    val tagInfo: LiveData<CustomerTag> = _tagInfo
    
    private val _tags = MutableLiveData<List<Tag>>()
    val tags: LiveData<List<Tag>> = _tags
    
    private val _loading = MutableLiveData<Boolean>()
    val loading: LiveData<Boolean> = _loading
    
    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    
    private val _operationSuccess = MutableLiveData<Event<String>>()
    val operationSuccess: LiveData<Event<String>> = _operationSuccess
    
    /**
     * 获取客户标签信息
     */
    fun getCustomerTagInfo(customerId: String) {
        _loading.value = true
        
        viewModelScope.launch {
            try {
                val result = customerRepository.getCustomerTagInfo(customerId)
                
                result.collect { result ->
                    _loading.value = false
                    result.fold(
                        onSuccess = { tagInfo ->
                            _tagInfo.value = tagInfo
                            _tags.value = tagInfo.tags
                        },
                        onFailure = { e ->
                            _error.value = e.message ?: "获取客户标签信息失败"
                        }
                    )
                }
            } catch (e: Exception) {
                _loading.value = false
                _error.value = e.message ?: "获取客户标签信息失败"
            }
        }
    }
    
    /**
     * 添加标签
     */
    fun addTag(customerId: String, tag: Tag) {
        val currentTagInfo = _tagInfo.value
        val currentTags = currentTagInfo?.tags?.toMutableList() ?: mutableListOf()
        
        // 检查是否已存在相同名称的标签
        if (currentTags.any { it.name == tag.name }) {
            _error.value = "已存在相同名称的标签"
            return
        }
        
        currentTags.add(tag)
        
        val newTagInfo = currentTagInfo?.copy(tags = currentTags) 
            ?: CustomerTag(
                id = "",
                customerId = customerId,
                tags = currentTags
            )
        
        updateCustomerTag(newTagInfo)
    }
    
    /**
     * 删除标签
     */
    fun deleteTag(tag: Tag) {
        val currentTagInfo = _tagInfo.value ?: return
        val currentTags = currentTagInfo.tags.toMutableList()
        
        currentTags.removeAll { it.id == tag.id }
        
        val newTagInfo = currentTagInfo.copy(tags = currentTags)
        updateCustomerTag(newTagInfo)
    }
    
    /**
     * 更新客户标签信息
     */
    private fun updateCustomerTag(customerTag: CustomerTag) {
        _loading.value = true
        
        viewModelScope.launch {
            try {
                val result = customerRepository.updateCustomerTag(customerTag)
                
                result.collect { result ->
                    _loading.value = false
                    result.fold(
                        onSuccess = { _ ->
                            _tagInfo.value = customerTag
                            _tags.value = customerTag.tags
                            _operationSuccess.value = Event("标签更新成功")
                        },
                        onFailure = { e ->
                            _error.value = e.message ?: "更新标签失败"
                        }
                    )
                }
            } catch (e: Exception) {
                _loading.value = false
                _error.value = e.message ?: "更新标签失败"
            }
        }
    }
} 