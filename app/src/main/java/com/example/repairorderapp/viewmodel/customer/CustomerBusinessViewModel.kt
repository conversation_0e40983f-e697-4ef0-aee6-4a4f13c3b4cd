package com.example.repairorderapp.viewmodel.customer

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.repairorderapp.data.repository.CustomerRepository
import com.example.repairorderapp.model.customer.CustomerBusiness
import com.example.repairorderapp.util.Event
import kotlinx.coroutines.launch

/**
 * 客户商务信息ViewModel
 */
class CustomerBusinessViewModel(
    private val customerRepository: CustomerRepository
) : ViewModel() {
    
    private val _business = MutableLiveData<CustomerBusiness>()
    val business: LiveData<CustomerBusiness> = _business
    
    private val _loading = MutableLiveData<Boolean>()
    val loading: LiveData<Boolean> = _loading
    
    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    
    private val _operationSuccess = MutableLiveData<Event<String>>()
    val operationSuccess: LiveData<Event<String>> = _operationSuccess
    
    /**
     * 获取客户商务信息
     */
    fun getCustomerBusinessInfo(customerId: String) {
        _loading.value = true
        
        viewModelScope.launch {
            try {
                val result = customerRepository.getCustomerBusinessInfo(customerId)
                result.collect { result ->
                    _loading.value = false
                    result.fold(
                        onSuccess = { business ->
                            _business.value = business
                        },
                        onFailure = { e ->
                            _error.value = e.message ?: "获取客户商务信息失败"
                        }
                    )
                }
            } catch (e: Exception) {
                _loading.value = false
                _error.value = e.message ?: "获取客户商务信息失败"
            }
        }
    }
    
    /**
     * 更新客户商务信息
     */
    fun updateCustomerBusinessInfo(params: Map<String, Any?>) {
        _loading.value = true
        
        viewModelScope.launch {
            try {
                val result = customerRepository.updateCustomerBusinessInfo(params)
                result.collect { result ->
                    _loading.value = false
                    result.fold(
                        onSuccess = { success ->
                            if (success) {
                                _operationSuccess.value = Event("商务信息保存成功")
                            } else {
                                _error.value = "商务信息保存失败，请稍后重试"
                            }
                        },
                        onFailure = { e ->
                            _error.value = e.message ?: "更新客户商务信息失败"
                        }
                    )
                }
            } catch (e: Exception) {
                _loading.value = false
                _error.value = e.message ?: "更新客户商务信息失败"
            }
        }
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _error.value = ""
    }
} 