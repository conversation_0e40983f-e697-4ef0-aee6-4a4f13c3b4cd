package com.example.repairorderapp.viewmodel.customer

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.repairorderapp.data.repository.CustomerRepository
import com.example.repairorderapp.model.customer.Customer
import com.example.repairorderapp.model.customer.CustomerOption
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

/**
 * 客户信息编辑ViewModel
 */
class CustomerInfoEditViewModel(
    private val customerRepository: CustomerRepository
) : ViewModel() {

    private val _loading = MutableLiveData<Boolean>()
    val loading: LiveData<Boolean> = _loading

    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    private val _customer = MutableLiveData<Customer>()
    val customer: LiveData<Customer> = _customer

    private val _saveSuccess = MutableLiveData<Boolean>()
    val saveSuccess: LiveData<Boolean> = _saveSuccess

    // 字典数据
    private val _customerTypeOptions = MutableLiveData<List<CustomerOption>>()
    val customerTypeOptions: LiveData<List<CustomerOption>> = _customerTypeOptions

    private val _businessStatusOptions = MutableLiveData<List<CustomerOption>>()
    val businessStatusOptions: LiveData<List<CustomerOption>> = _businessStatusOptions

    private val _customerStatusOptions = MutableLiveData<List<CustomerOption>>()
    val customerStatusOptions: LiveData<List<CustomerOption>> = _customerStatusOptions

    private val _industryAttrOptions = MutableLiveData<List<CustomerOption>>()
    val industryAttrOptions: LiveData<List<CustomerOption>> = _industryAttrOptions

    private val _membershipLevelOptions = MutableLiveData<List<CustomerOption>>()
    val membershipLevelOptions: LiveData<List<CustomerOption>> = _membershipLevelOptions

    private val _groupOptions = MutableLiveData<List<CustomerOption>>()
    val groupOptions: LiveData<List<CustomerOption>> = _groupOptions

    /**
     * 加载基础数据（字典数据）
     */
    fun loadBaseData() {
        viewModelScope.launch {
            _loading.value = true
            _error.value = ""

            try {
                // 并行加载所有字典数据
                val customerTypeDeferred = viewModelScope.async { customerRepository.getDictTreeByCodeSync("400") }
                val businessStatusDeferred = viewModelScope.async { customerRepository.getDictTreeByCodeSync("200") }
                val customerStatusDeferred = viewModelScope.async { customerRepository.getDictTreeByCodeSync("100") }
                val industryAttrDeferred = viewModelScope.async { customerRepository.getDictTreeByCodeSync("300") }
                val membershipLevelDeferred = viewModelScope.async { customerRepository.getCustomerLevelList() }
                val groupDeferred = viewModelScope.async { customerRepository.getCustomerGroupList() }

                // 等待所有请求完成
                _customerTypeOptions.value = customerTypeDeferred.await()
                _businessStatusOptions.value = businessStatusDeferred.await()
                _customerStatusOptions.value = customerStatusDeferred.await()
                _industryAttrOptions.value = industryAttrDeferred.await()
                _membershipLevelOptions.value = membershipLevelDeferred.await()
                _groupOptions.value = groupDeferred.await()

            } catch (e: Exception) {
                _error.value = "加载基础数据失败: ${e.message}"
                android.util.Log.e("CustomerInfoEditVM", "loadBaseData error", e)
            } finally {
                _loading.value = false
            }
        }
    }

    /**
     * 加载客户数据
     */
    fun loadCustomerData(customerId: String) {
        viewModelScope.launch {
            _loading.value = true
            _error.value = ""

            try {
                val customerData = customerRepository.getCustomerDetailSync(customerId)
                _customer.value = customerData
            } catch (e: Exception) {
                _error.value = "加载客户信息失败: ${e.message}"
                android.util.Log.e("CustomerInfoEditVM", "loadCustomerData error", e)
            } finally {
                _loading.value = false
            }
        }
    }

    /**
     * 新增客户
     */
    fun addCustomer(customer: Customer) {
        viewModelScope.launch {
            _loading.value = true
            _error.value = ""

            try {
                customerRepository.addCustomerSync(customer)
                _saveSuccess.value = true
            } catch (e: Exception) {
                _error.value = "新增客户失败: ${e.message}"
                android.util.Log.e("CustomerInfoEditVM", "addCustomer error", e)
            } finally {
                _loading.value = false
            }
        }
    }

    /**
     * 更新客户信息
     */
    fun updateCustomer(customer: Customer) {
        viewModelScope.launch {
            _loading.value = true
            _error.value = ""

            try {
                customerRepository.updateCustomerSync(customer)
                _saveSuccess.value = true
            } catch (e: Exception) {
                _error.value = "更新客户信息失败: ${e.message}"
                android.util.Log.e("CustomerInfoEditVM", "updateCustomer error", e)
            } finally {
                _loading.value = false
            }
        }
    }


} 