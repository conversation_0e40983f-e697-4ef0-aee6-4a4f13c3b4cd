package com.example.repairorderapp.viewmodel.customer

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.example.repairorderapp.data.repository.CustomerRepository

/**
 * 客户设备ViewModel工厂
 */
class CustomerDeviceViewModelFactory(
    private val customerRepository: CustomerRepository
) : ViewModelProvider.Factory {
    
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(CustomerDeviceViewModel::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return CustomerDeviceViewModel(customerRepository) as T
        }
        throw IllegalArgumentException("未知的ViewModel类: ${modelClass.name}")
    }
} 