package com.example.repairorderapp.performance

import android.util.Log
import com.example.repairorderapp.data.dao.*
import com.example.repairorderapp.data.model.*
import com.example.repairorderapp.utils.EnhancedLogUtils

/**
 * 数据库性能监控包装器
 * 为数据库操作添加性能监控功能
 */
class DatabasePerformanceWrapper {
    
    companion object {
        private const val TAG = "DatabasePerformance"
        
        /**
         * 包装LogEntryDao，添加性能监控
         */
        fun wrapLogEntryDao(dao: LogEntryDao): LogEntryDao {
            return LogEntryDaoWrapper(dao)
        }
        
        /**
         * 包装DeviceInfoDao，添加性能监控
         */
        fun wrapDeviceInfoDao(dao: DeviceInfoDao): DeviceInfoDao {
            return DeviceInfoDaoWrapper(dao)
        }
        
        /**
         * 包装LocationDao，添加性能监控
         */
        fun wrapLocationDao(dao: LocationDao): LocationDao {
            return LocationDaoWrapper(dao)
        }
        
        /**
         * 包装CrashInfoDao，添加性能监控
         */
        fun wrapCrashInfoDao(dao: CrashInfoDao): CrashInfoDao {
            return CrashInfoDaoWrapper(dao)
        }
    }
    
    /**
     * LogEntryDao性能监控包装器
     */
    private class LogEntryDaoWrapper(private val delegate: LogEntryDao) : LogEntryDao {
        
        override suspend fun insertLog(logEntry: LogEntry): Long {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "insertLog",
                    "table" to "log_entries",
                    "logType" to logEntry.logType
                )
            ) {
                delegate.insertLog(logEntry)
            }
        }
        
        override suspend fun insertLogs(logEntries: List<LogEntry>): List<Long> {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "insertLogs",
                    "table" to "log_entries",
                    "batchSize" to logEntries.size
                )
            ) {
                delegate.insertLogs(logEntries)
            }
        }
        
        override suspend fun getUnuploadedLogsByType(logType: String, limit: Int): List<LogEntry> {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getUnuploadedLogsByType",
                    "table" to "log_entries",
                    "logType" to logType,
                    "limit" to limit
                )
            ) {
                delegate.getUnuploadedLogsByType(logType, limit)
            }
        }
        
        override suspend fun getUnuploadedLogs(limit: Int): List<LogEntry> {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getUnuploadedLogs",
                    "table" to "log_entries",
                    "limit" to limit
                )
            ) {
                delegate.getUnuploadedLogs(limit)
            }
        }
        
        override suspend fun markAsUploaded(logIds: List<Long>) {
            PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "markAsUploaded",
                    "table" to "log_entries",
                    "batchSize" to logIds.size
                )
            ) {
                delegate.markAsUploaded(logIds)
            }
        }
        
        override suspend fun getLogsByTypeAndLevel(logType: String, level: String, limit: Int): List<LogEntry> {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getLogsByTypeAndLevel",
                    "table" to "log_entries",
                    "logType" to logType,
                    "level" to level,
                    "limit" to limit
                )
            ) {
                delegate.getLogsByTypeAndLevel(logType, level, limit)
            }
        }
        
        override suspend fun getLogCount(): Int {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getLogCount",
                    "table" to "log_entries"
                )
            ) {
                delegate.getLogCount()
            }
        }
        
        override suspend fun getUnuploadedLogCount(): Int {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getUnuploadedLogCount",
                    "table" to "log_entries"
                )
            ) {
                delegate.getUnuploadedLogCount()
            }
        }
        
        override suspend fun getLogCountByType(logType: String): Int {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getLogCountByType",
                    "table" to "log_entries",
                    "logType" to logType
                )
            ) {
                delegate.getLogCountByType(logType)
            }
        }
        
        override suspend fun cleanupOldLogs(keepCount: Int) {
            PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "cleanupOldLogs",
                    "table" to "log_entries",
                    "keepCount" to keepCount
                )
            ) {
                delegate.cleanupOldLogs(keepCount)
            }
        }

        override suspend fun deleteLogsBeforeTime(timestamp: Long) {
            PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "deleteLogsBeforeTime",
                    "table" to "log_entries",
                    "timestamp" to timestamp
                )
            ) {
                delegate.deleteLogsBeforeTime(timestamp)
            }
        }

        // 其他方法直接委托给原始实现（为了简化，不是所有方法都添加监控）
        override suspend fun getLogsByTimeRange(startTime: Long, endTime: Long): List<LogEntry> = delegate.getLogsByTimeRange(startTime, endTime)
        override suspend fun getLogsWithEmptyFields(): List<LogEntry> = delegate.getLogsWithEmptyFields()
        
        override suspend fun deleteUploadedLogs() {
            PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "deleteUploadedLogs",
                    "table" to "log_entries"
                )
            ) {
                delegate.deleteUploadedLogs()
            }
        }
        
        override suspend fun getLatestLogs(limit: Int): List<LogEntry> {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getLatestLogs",
                    "table" to "log_entries",
                    "limit" to limit
                )
            ) {
                delegate.getLatestLogs(limit)
            }
        }
        
        override suspend fun getLogsByTag(tag: String, limit: Int): List<LogEntry> {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getLogsByTag",
                    "table" to "log_entries",
                    "tag" to tag,
                    "limit" to limit
                )
            ) {
                delegate.getLogsByTag(tag, limit)
            }
        }
        
        override suspend fun getErrorLogs(limit: Int): List<LogEntry> {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getErrorLogs",
                    "table" to "log_entries",
                    "limit" to limit
                )
            ) {
                delegate.getErrorLogs(limit)
            }
        }
        
        override suspend fun deleteAllLogs() {
            PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "deleteAllLogs",
                    "table" to "log_entries"
                )
            ) {
                delegate.deleteAllLogs()
            }
        }
        
        override suspend fun update(logEntry: LogEntry) {
            PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "update",
                    "table" to "log_entries"
                )
            ) {
                delegate.update(logEntry)
            }
        }
        
        override suspend fun updateLogs(logEntries: List<LogEntry>) {
            PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "updateLogs",
                    "table" to "log_entries",
                    "batchSize" to logEntries.size
                )
            ) {
                delegate.updateLogs(logEntries)
            }
        }
    }
    
    /**
     * DeviceInfoDao性能监控包装器
     */
    private class DeviceInfoDaoWrapper(private val delegate: DeviceInfoDao) : DeviceInfoDao {
        
        override suspend fun insertOrUpdateDevice(deviceInfo: DeviceInfo) {
            PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "insertOrUpdateDevice",
                    "table" to "device_info"
                )
            ) {
                delegate.insertOrUpdateDevice(deviceInfo)
            }
        }
        
        override suspend fun getDeviceById(deviceId: String): DeviceInfo? {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getDeviceById",
                    "table" to "device_info"
                )
            ) {
                delegate.getDeviceById(deviceId)
            }
        }
        
        override suspend fun getAllDevices(): List<DeviceInfo> {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getAllDevices",
                    "table" to "device_info"
                )
            ) {
                delegate.getAllDevices()
            }
        }
        
        override suspend fun getDevicesByBrand(brand: String): List<DeviceInfo> {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getDevicesByBrand",
                    "table" to "device_info",
                    "brand" to brand
                )
            ) {
                delegate.getDevicesByBrand(brand)
            }
        }
        
        override suspend fun getDevicesByModel(model: String): List<DeviceInfo> {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getDevicesByModel",
                    "table" to "device_info",
                    "model" to model
                )
            ) {
                delegate.getDevicesByModel(model)
            }
        }
        
        override suspend fun getDevicesByOsType(osType: String): List<DeviceInfo> {
            return delegate.getDevicesByOsType(osType)
        }
        
        override suspend fun getDevicesByOsVersion(osVersion: String): List<DeviceInfo> {
            return delegate.getDevicesByOsVersion(osVersion)
        }
        
        override suspend fun getProblemDevices(): List<DeviceInfo> {
            return delegate.getProblemDevices()
        }
        
        override suspend fun getHighEndDevices(minMemory: Long, minSdk: Int): List<DeviceInfo> {
            return delegate.getHighEndDevices(minMemory, minSdk)
        }
        
        override suspend fun getLowEndDevices(maxMemory: Long, minSdk: Int): List<DeviceInfo> {
            return delegate.getLowEndDevices(maxMemory, minSdk)
        }
        
        override suspend fun updateCollectCount(deviceId: String, updateTime: Long) {
            delegate.updateCollectCount(deviceId, updateTime)
        }
        
        override suspend fun getBrandStatistics(): List<BrandStatistic> {
            return delegate.getBrandStatistics()
        }
        
        override suspend fun getOsVersionStatistics(): List<OsVersionStatistic> {
            return delegate.getOsVersionStatistics()
        }
        
        override suspend fun getDeviceCount(): Int {
            return delegate.getDeviceCount()
        }
        
        override suspend fun deleteDevice(deviceId: String) {
            delegate.deleteDevice(deviceId)
        }
        
        override suspend fun cleanupOldDevices(cutoffTime: Long) {
            delegate.cleanupOldDevices(cutoffTime)
        }
    }

    /**
     * LocationDao性能监控包装器
     */
    private class LocationDaoWrapper(private val delegate: LocationDao) : LocationDao {

        override suspend fun insertLocation(location: LocationPoint) {
            PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "insertLocation",
                    "table" to "location_points"
                )
            ) {
                delegate.insertLocation(location)
            }
        }

        override suspend fun insertAll(locations: List<LocationPoint>) {
            PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "insertAll",
                    "table" to "location_points",
                    "batchSize" to locations.size
                )
            ) {
                delegate.insertAll(locations)
            }
        }

        override suspend fun getUnuploadedLocations(limit: Int): List<LocationPoint> {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getUnuploadedLocations",
                    "table" to "location_points",
                    "limit" to limit
                )
            ) {
                delegate.getUnuploadedLocations(limit)
            }
        }

        override suspend fun markAsUploaded(ids: List<Long>) {
            PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "markAsUploaded",
                    "table" to "location_points",
                    "batchSize" to ids.size
                )
            ) {
                delegate.markAsUploaded(ids)
            }
        }

        override suspend fun deleteUploadedBefore(timestamp: Long) {
            PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "deleteUploadedBefore",
                    "table" to "location_points",
                    "timestamp" to timestamp
                )
            ) {
                delegate.deleteUploadedBefore(timestamp)
            }
        }

        override suspend fun count(): Int {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "count",
                    "table" to "location_points"
                )
            ) {
                delegate.count()
            }
        }

        override suspend fun clearAll() {
            PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "clearAll",
                    "table" to "location_points"
                )
            ) {
                delegate.clearAll()
            }
        }

        override suspend fun keepLatestRecords(keepCount: Int) {
            PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "keepLatestRecords",
                    "table" to "location_points",
                    "keepCount" to keepCount
                )
            ) {
                delegate.keepLatestRecords(keepCount)
            }
        }
    }

    /**
     * CrashInfoDao性能监控包装器
     */
    private class CrashInfoDaoWrapper(private val delegate: CrashInfoDao) : CrashInfoDao {

        override suspend fun insertCrash(crashInfo: CrashInfo): Long {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "insertCrash",
                    "table" to "crash_info"
                )
            ) {
                delegate.insertCrash(crashInfo)
            }
        }

        override suspend fun insertCrashes(crashes: List<CrashInfo>): List<Long> {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "insertCrashes",
                    "table" to "crash_info",
                    "batchSize" to crashes.size
                )
            ) {
                delegate.insertCrashes(crashes)
            }
        }

        override suspend fun getCrashById(crashId: Long): CrashInfo? {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getCrashById",
                    "table" to "crash_info"
                )
            ) {
                delegate.getCrashById(crashId)
            }
        }

        override suspend fun getCrashesByDevice(deviceId: String, limit: Int): List<CrashInfo> {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getCrashesByDevice",
                    "table" to "crash_info",
                    "limit" to limit
                )
            ) {
                delegate.getCrashesByDevice(deviceId, limit)
            }
        }

        override suspend fun getUnuploadedCrashes(limit: Int): List<CrashInfo> {
            return PerformanceMonitor.measurePerformanceAsync(
                metricName = "database_query",
                metadata = mapOf(
                    "operation" to "getUnuploadedCrashes",
                    "table" to "crash_info",
                    "limit" to limit
                )
            ) {
                delegate.getUnuploadedCrashes(limit)
            }
        }

        // 其他方法委托给原始实现（为了简化，不是所有方法都添加监控）
        override suspend fun getCrashesByExceptionType(exceptionType: String, limit: Int): List<CrashInfo> = delegate.getCrashesByExceptionType(exceptionType, limit)
        override suspend fun getCrashesByTimeRange(startTime: Long, endTime: Long): List<CrashInfo> = delegate.getCrashesByTimeRange(startTime, endTime)
        override suspend fun getRecentCrashes(limit: Int): List<CrashInfo> = delegate.getRecentCrashes(limit)
        override suspend fun getMemoryRelatedCrashes(limit: Int): List<CrashInfo> = delegate.getMemoryRelatedCrashes(limit)
        override suspend fun getNetworkRelatedCrashes(limit: Int): List<CrashInfo> = delegate.getNetworkRelatedCrashes(limit)
        override suspend fun markAsUploaded(crashIds: List<Long>) = delegate.markAsUploaded(crashIds)
        override suspend fun getCrashStatistics(): List<CrashStatistic> = delegate.getCrashStatistics()
        override suspend fun getDeviceCrashStatistics(limit: Int): List<DeviceCrashStatistic> = delegate.getDeviceCrashStatistics(limit)
        override suspend fun getCrashTrendStatistics(startTime: Long, endTime: Long): List<CrashTrendStatistic> = delegate.getCrashTrendStatistics(startTime, endTime)
        override suspend fun getCrashCount(): Int = delegate.getCrashCount()
        override suspend fun getUnuploadedCrashCount(): Int = delegate.getUnuploadedCrashCount()
        override suspend fun getCrashCountByDevice(deviceId: String): Int = delegate.getCrashCountByDevice(deviceId)
        override suspend fun deleteCrashesBeforeTime(cutoffTime: Long) = delegate.deleteCrashesBeforeTime(cutoffTime)
        override suspend fun deleteUploadedCrashes() = delegate.deleteUploadedCrashes()
        override suspend fun cleanupOldCrashes(keepCount: Int) = delegate.cleanupOldCrashes(keepCount)
        override suspend fun deleteAllCrashes() = delegate.deleteAllCrashes()
    }
}
