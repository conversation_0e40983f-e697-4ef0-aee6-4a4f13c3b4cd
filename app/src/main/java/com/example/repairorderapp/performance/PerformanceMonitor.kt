package com.example.repairorderapp.performance

import android.content.Context
import android.os.Debug
import android.util.Log
import com.example.repairorderapp.utils.EnhancedLogUtils
import java.util.concurrent.ConcurrentHashMap
import kotlin.system.measureTimeMillis

/**
 * 性能监控工具类
 * 统一管理应用性能监控，提供便捷的性能测量和告警功能
 */
object PerformanceMonitor {
    
    private const val TAG = "PerformanceMonitor"
    
    // 性能指标存储
    private val performanceMetrics = ConcurrentHashMap<String, PerformanceMetric>()
    
    // 性能阈值配置（毫秒）
    private val performanceThresholds = mapOf(
        "app_startup" to 3000L,           // 应用启动
        "page_load" to 2000L,             // 页面加载
        "api_request" to 5000L,           // API请求
        "database_query" to 1000L,        // 数据库查询
        "location_acquisition" to 10000L, // 位置获取
        "image_load" to 3000L,            // 图片加载
        "file_operation" to 2000L,        // 文件操作
        "network_download" to 15000L      // 网络下载
    )

    // 性能监控采样配置
    private val samplingRates = mapOf(
        "app_startup" to 1.0f,           // 100% - 应用启动很重要
        "page_load" to 1.0f,             // 100% - 页面加载很重要
        "api_request" to 0.1f,           // 10% - API请求采样
        "database_query" to 0.2f,        // 20% - 数据库查询采样
        "location_acquisition" to 0.5f,  // 50% - 位置获取中等重要
        "image_load" to 0.1f,            // 10% - 图片加载采样
        "file_operation" to 0.3f,        // 30% - 文件操作采样
        "network_download" to 0.5f       // 50% - 网络下载中等重要
    )

    // 只记录超过阈值的性能监控（除了关键操作）
    private val criticalOperations = setOf(
        "app_startup", "page_load", "location_acquisition"
    )
    
    // 内存使用阈值（百分比）
    private const val MEMORY_WARNING_THRESHOLD = 75
    private const val MEMORY_CRITICAL_THRESHOLD = 90
    
    /**
     * 性能指标数据类
     */
    data class PerformanceMetric(
        val name: String,
        val startTime: Long,
        var endTime: Long = 0L,
        val metadata: MutableMap<String, Any> = mutableMapOf(),
        var memoryStart: Long = 0L,
        var memoryEnd: Long = 0L
    ) {
        val duration: Long
            get() = if (endTime > 0) endTime - startTime else 0L
            
        val memoryDelta: Long
            get() = memoryEnd - memoryStart
    }
    
    /**
     * 开始性能监控
     */
    fun startTiming(metricName: String, metadata: Map<String, Any> = emptyMap()) {
        val currentMemory = getCurrentMemoryUsage()
        
        performanceMetrics[metricName] = PerformanceMetric(
            name = metricName,
            startTime = System.currentTimeMillis(),
            metadata = metadata.toMutableMap(),
            memoryStart = currentMemory
        )
        
        // 只在VERBOSE级别记录开始监控信息
        if (Log.isLoggable(TAG, Log.VERBOSE)) {
            Log.v(TAG, "开始监控: $metricName ${metadata.takeIf { it.isNotEmpty() }?.let { "- $it" } ?: ""}")
        }
    }
    
    /**
     * 结束性能监控并记录结果
     */
    fun endTiming(metricName: String, additionalData: Map<String, Any> = emptyMap()): Long {
        val metric = performanceMetrics.remove(metricName)
        if (metric == null) {
            Log.w(TAG, "未找到性能监控指标: $metricName")
            return 0L
        }

        metric.endTime = System.currentTimeMillis()
        metric.memoryEnd = getCurrentMemoryUsage()
        metric.metadata.putAll(additionalData)

        val duration = metric.duration
        val memoryDelta = metric.memoryDelta

        // 智能过滤：决定是否记录性能日志
        if (shouldRecordPerformance(metricName, duration)) {
            // 记录性能日志
            EnhancedLogUtils.logPerformance(
                tag = TAG,
                message = "性能监控: $metricName - 耗时: ${duration}ms",
                duration = duration,
                memoryUsage = metric.memoryEnd
            )
        }

        // 检查性能阈值（总是检查，用于告警）
        checkPerformanceThreshold(metricName, duration, metric.metadata)

        // 检查内存使用
        checkMemoryUsage(metricName, metric.memoryEnd)

        // 只在VERBOSE级别记录详细信息，减少日志冗余
        if (Log.isLoggable(TAG, Log.VERBOSE)) {
            Log.v(TAG, "完成监控: $metricName - 耗时: ${duration}ms, 内存变化: ${memoryDelta}KB")
        }

        return duration
    }

    /**
     * 智能判断是否应该记录性能日志
     */
    private fun shouldRecordPerformance(metricName: String, duration: Long): Boolean {
        // 1. 关键操作总是记录
        if (metricName in criticalOperations) {
            return true
        }

        // 2. 超过阈值的总是记录
        val threshold = performanceThresholds[metricName]
        if (threshold != null && duration > threshold) {
            return true
        }

        // 3. 按采样率决定
        val samplingRate = samplingRates[metricName] ?: 0.1f
        return Math.random() < samplingRate
    }
    
    /**
     * 便捷的性能测量方法
     */
    inline fun <T> measurePerformance(
        metricName: String,
        metadata: Map<String, Any> = emptyMap(),
        block: () -> T
    ): T {
        startTiming(metricName, metadata)
        return try {
            block()
        } finally {
            endTiming(metricName)
        }
    }
    
    /**
     * 异步性能测量
     */
    suspend inline fun <T> measurePerformanceAsync(
        metricName: String,
        metadata: Map<String, Any> = emptyMap(),
        crossinline block: suspend () -> T
    ): T {
        startTiming(metricName, metadata)
        return try {
            block()
        } finally {
            endTiming(metricName)
        }
    }
    
    /**
     * 检查性能阈值
     */
    private fun checkPerformanceThreshold(
        metricName: String, 
        duration: Long, 
        metadata: Map<String, Any>
    ) {
        val threshold = performanceThresholds[metricName] ?: return
        
        if (duration > threshold) {
            val severity = when {
                duration > threshold * 2 -> "CRITICAL"
                duration > threshold * 1.5 -> "HIGH"
                else -> "MEDIUM"
            }
            
            EnhancedLogUtils.w(TAG, "性能告警: $metricName 耗时 ${duration}ms 超过阈值 ${threshold}ms (严重程度: $severity)")
            
            // 记录性能告警日志
            EnhancedLogUtils.logPerformance(
                tag = "PerformanceAlert",
                message = "性能告警: $metricName - 耗时 ${duration}ms 超过阈值 ${threshold}ms (严重程度: $severity)",
                duration = duration
            )
        }
    }
    
    /**
     * 检查内存使用情况
     */
    private fun checkMemoryUsage(metricName: String, currentMemory: Long) {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val memoryUsagePercent = (currentMemory.toDouble() / maxMemory * 100).toInt()
        
        when {
            memoryUsagePercent >= MEMORY_CRITICAL_THRESHOLD -> {
                EnhancedLogUtils.w(TAG, "内存使用严重告警: ${memoryUsagePercent}% (${currentMemory / 1024}KB / ${maxMemory / 1024}KB)")
                recordMemoryAlert(metricName, memoryUsagePercent, "CRITICAL")
            }
            memoryUsagePercent >= MEMORY_WARNING_THRESHOLD -> {
                EnhancedLogUtils.w(TAG, "内存使用告警: ${memoryUsagePercent}% (${currentMemory / 1024}KB / ${maxMemory / 1024}KB)")
                recordMemoryAlert(metricName, memoryUsagePercent, "WARNING")
            }
        }
    }
    
    /**
     * 记录内存告警
     */
    private fun recordMemoryAlert(metricName: String, memoryPercent: Int, severity: String) {
        EnhancedLogUtils.logPerformance(
            tag = "MemoryAlert",
            message = "内存使用告警: $metricName - ${memoryPercent}% (严重程度: $severity)",
            memoryUsage = getCurrentMemoryUsage()
        )
    }
    
    /**
     * 获取当前内存使用量（字节）
     */
    private fun getCurrentMemoryUsage(): Long {
        val runtime = Runtime.getRuntime()
        return runtime.totalMemory() - runtime.freeMemory()
    }
    
    /**
     * 记录内存快照
     */
    fun logMemorySnapshot(tag: String = "MemorySnapshot") {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val memoryUsagePercent = (usedMemory.toDouble() / maxMemory * 100).toInt()
        
        EnhancedLogUtils.logPerformance(
            tag = tag,
            message = "内存快照 - 使用: ${usedMemory / 1024 / 1024}MB/${maxMemory / 1024 / 1024}MB (${memoryUsagePercent}%)",
            memoryUsage = usedMemory
        )
    }
    
    /**
     * 获取性能统计信息
     */
    fun getPerformanceStats(): Map<String, Any> {
        return mapOf(
            "activeMetrics" to performanceMetrics.size,
            "thresholds" to performanceThresholds,
            "memoryUsagePercent" to run {
                val runtime = Runtime.getRuntime()
                val usedMemory = runtime.totalMemory() - runtime.freeMemory()
                val maxMemory = runtime.maxMemory()
                (usedMemory.toDouble() / maxMemory * 100).toInt()
            }
        )
    }

    /**
     * 测试性能监控和用户信息记录
     */
    fun testPerformanceWithUserInfo() {
        Log.i(TAG, "=== 测试性能监控用户信息记录 ===")

        // 直接调用EnhancedLogUtils强制记录测试日志
        EnhancedLogUtils.logPerformance(
            tag = "UserInfoTest",
            message = "测试性能监控用户信息记录 - 耗时: 100ms",
            duration = 100L,
            memoryUsage = getCurrentMemoryUsage()
        )

        Log.i(TAG, "=== 性能监控用户信息测试完成 ===")
    }
}
