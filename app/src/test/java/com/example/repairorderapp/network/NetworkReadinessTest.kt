package com.example.repairorderapp.network

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.junit.MockitoJUnitRunner
import org.junit.Assert.*

/**
 * 网络就绪状态检测测试
 */
@RunWith(MockitoJUnitRunner::class)
class NetworkReadinessTest {

    @Mock
    private lateinit var mockContext: Context

    @Mock
    private lateinit var mockConnectivityManager: ConnectivityManager

    @Mock
    private lateinit var mockNetwork: Network

    @Mock
    private lateinit var mockNetworkCapabilities: NetworkCapabilities

    @Before
    fun setup() {
        `when`(mockContext.getSystemService(Context.CONNECTIVITY_SERVICE))
            .thenReturn(mockConnectivityManager)
    }

    @Test
    fun `should return true when network is validated and has wifi transport`() {
        // Given: 网络已验证且有 WiFi 传输
        `when`(mockConnectivityManager.activeNetwork).thenReturn(mockNetwork)
        `when`(mockConnectivityManager.getNetworkCapabilities(mockNetwork))
            .thenReturn(mockNetworkCapabilities)
        `when`(mockNetworkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED))
            .thenReturn(true)
        `when`(mockNetworkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI))
            .thenReturn(true)
        `when`(mockNetworkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR))
            .thenReturn(false)

        // When: 检查网络是否就绪
        val isReady = ApiClient.isNetworkReady(mockContext)

        // Then: 应该返回 true
        assertTrue(isReady)
    }

    @Test
    fun `should return true when network is validated and has cellular transport`() {
        // Given: 网络已验证且有蜂窝传输
        `when`(mockConnectivityManager.activeNetwork).thenReturn(mockNetwork)
        `when`(mockConnectivityManager.getNetworkCapabilities(mockNetwork))
            .thenReturn(mockNetworkCapabilities)
        `when`(mockNetworkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED))
            .thenReturn(true)
        `when`(mockNetworkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI))
            .thenReturn(false)
        `when`(mockNetworkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR))
            .thenReturn(true)

        // When: 检查网络是否就绪
        val isReady = ApiClient.isNetworkReady(mockContext)

        // Then: 应该返回 true
        assertTrue(isReady)
    }

    @Test
    fun `should return false when network is not validated`() {
        // Given: 网络未验证
        `when`(mockConnectivityManager.activeNetwork).thenReturn(mockNetwork)
        `when`(mockConnectivityManager.getNetworkCapabilities(mockNetwork))
            .thenReturn(mockNetworkCapabilities)
        `when`(mockNetworkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED))
            .thenReturn(false)

        // When: 检查网络是否就绪
        val isReady = ApiClient.isNetworkReady(mockContext)

        // Then: 应该返回 false
        assertFalse(isReady)
    }

    @Test
    fun `should return false when no active network`() {
        // Given: 没有活动网络
        `when`(mockConnectivityManager.activeNetwork).thenReturn(null)

        // When: 检查网络是否就绪
        val isReady = ApiClient.isNetworkReady(mockContext)

        // Then: 应该返回 false
        assertFalse(isReady)
    }

    @Test
    fun `should return false when network capabilities is null`() {
        // Given: 网络能力为空
        `when`(mockConnectivityManager.activeNetwork).thenReturn(mockNetwork)
        `when`(mockConnectivityManager.getNetworkCapabilities(mockNetwork)).thenReturn(null)

        // When: 检查网络是否就绪
        val isReady = ApiClient.isNetworkReady(mockContext)

        // Then: 应该返回 false
        assertFalse(isReady)
    }
}
