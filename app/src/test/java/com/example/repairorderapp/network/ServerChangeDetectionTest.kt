package com.example.repairorderapp.network

import android.content.Context
import android.content.SharedPreferences
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.junit.MockitoJUnitRunner

/**
 * 服务器地址变更检测测试
 */
@RunWith(MockitoJUnitRunner::class)
class ServerChangeDetectionTest {

    @Mock
    private lateinit var mockContext: Context

    @Mock
    private lateinit var mockSharedPreferences: SharedPreferences

    @Mock
    private lateinit var mockEditor: SharedPreferences.Editor

    @Before
    fun setup() {
        `when`(mockContext.getSharedPreferences(anyString(), anyInt())).thenReturn(mockSharedPreferences)
        `when`(mockSharedPreferences.edit()).thenReturn(mockEditor)
        `when`(mockEditor.putString(anyString(), anyString())).thenReturn(mockEditor)
        `when`(mockEditor.apply()).then { }
    }

    @Test
    fun `should detect server change when URL is different`() {
        // Given: 旧的服务器地址
        `when`(mockSharedPreferences.getString("last_server_url", ""))
            .thenReturn("https://old-server.com/")

        // When: 检查服务器变更
        ApiClient.checkAndHandleServerChange(mockContext)

        // Then: 应该保存新的服务器地址
        verify(mockEditor).putString("last_server_url", ApiClient.BASE_URL)
        verify(mockEditor).apply()
    }

    @Test
    fun `should not trigger cache clear when URL is same`() {
        // Given: 相同的服务器地址
        `when`(mockSharedPreferences.getString("last_server_url", ""))
            .thenReturn(ApiClient.BASE_URL)

        // When: 检查服务器变更
        ApiClient.checkAndHandleServerChange(mockContext)

        // Then: 不应该更新 SharedPreferences
        verify(mockEditor, never()).putString(anyString(), anyString())
    }

    @Test
    fun `should handle first launch when no previous URL stored`() {
        // Given: 首次启动，没有存储的服务器地址
        `when`(mockSharedPreferences.getString("last_server_url", ""))
            .thenReturn("")

        // When: 检查服务器变更
        ApiClient.checkAndHandleServerChange(mockContext)

        // Then: 应该保存当前服务器地址
        verify(mockEditor).putString("last_server_url", ApiClient.BASE_URL)
        verify(mockEditor).apply()
    }
}
