{"name": "四川至简智印工程师端", "appid": "__UNI__FE85C0F", "description": "四川至简智印工程师平台", "versionName": "1.0.1", "versionCode": 101, "transformPx": false, "sassImplementationName": "node-sass", "app-plus": {"compatible": {"ignoreVersion": true}, "ios": {"UIUserInterfaceStyle": "Automatic"}, "usingComponents": true, "safearea": {"bottom": {"offset": "auto"}}, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"VideoPlayer": {}, "Maps": {}, "OAuth": {}}, "distribute": {"android": {"permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.WRITE_SMS\"/>"]}, "ios": {"privacyDescription": {"NSPhotoLibraryUsageDescription": "商品评价", "NSPhotoLibraryAddUsageDescription": "商品评价", "NSCameraUsageDescription": "核销虚拟商品", "NSMicrophoneUsageDescription": ""}}, "sdkConfigs": {"ad": {}, "payment": {"alipay": {"__platform__": ["ios", "android"]}, "appleiap": {}, "weixin": {"__platform__": ["ios", "android"], "appid": "wxcc7de0d6fcc1cb5d", "UniversalLinks": ""}}, "push": {}, "share": {"weixin": {"appid": "wxcc7de0d6fcc1cb5d", "UniversalLinks": ""}}, "oauth": {"weixin": {"appid": "wxcc7de0d6fcc1cb5d", "UniversalLinks": ""}}, "geolocation": {"amap": {"__platform__": ["ios", "android"], "appkey_ios": "", "appkey_android": ""}}, "maps": {"tencent": {"key": "SXEBZ-CKGCU-5HFVN-GHNV7-VTJ5J-ADBQB"}}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}, "splashscreen": {"android": {"hdpi": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/android-480-762.png", "xhdpi": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/android-720-1242.png", "xxhdpi": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/android-1080-1882.png"}, "ios": {"iphone": {"portrait-896h@3x": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/iso-full-splash.png", "landscape-896h@3x": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/iso-full-splash.png", "portrait-896h@2x": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/iso-full-splash.png", "landscape-896h@2x": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/iso-full-splash.png", "iphonex": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/iso-full-splash.png", "iphonexl": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/iso-full-splash.png", "retina55": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ios-splash.png", "retina55l": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ios-splash.png", "retina47": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ios-splash.png", "retina47l": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ios-splash.png", "retina40l": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ios-splash.png", "retina40": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ios-splash.png", "retina35": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ios-splash.png"}, "ipad": {"portrait-1366h@2x": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ipad.png", "landscape-1366h@2x": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ipad.png", "portrait-1194h@2": "/Users/<USER>/Downloads/开屏图/ipad.png", "landscape-1194h@2x": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ipad.png", "portrait-1112h@2x": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ipad.png", "landscape-1112h@2x": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ipad.png", "portrait-retina7": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ipad.png", "landscape-retina7": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ipad.png", "portrait7": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ipad.png", "landscape7": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ipad.png", "portrait-1194h@2x": "D:/git-projects/jixiebang/ruoyi-uniApp/docs/images/ipad.png"}}}}, "uniStatistics": {"enable": true}}, "uni-app": {"scripts": {"h5-weixin": {"title": "微信服务号", "BROWSER": "Chrome", "env": {"UNI_PLATFORM": "h5"}, "define": {"H5-WEIXIN": true}}}}, "quickapp": {}, "mp-weixin": {"usingComponents": true, "appid": "wxcc7de0d6fcc1cb5d", "setting": {"urlCheck": false, "minified": true, "postcss": true, "es6": true}, "permission": {"scope.userLocation": {"desc": "您的位置信息将用于更好的地址选取功能"}}, "plugins": {}, "uniStatistics": {"enable": true}, "unipush": {"enable": true}, "optimization": {"subPackages": true}, "lazyCodeLoading": "requiredComponents", "requiredPrivateInfos": ["chooseLocation", "getLocation"]}, "h5": {"router": {"mode": "history", "base": "./"}, "devServer": {"proxy": {"/8888": {"target": "https://www.baidu.com/api", "changeOrigin": true, "pathRewrite": {"^/8888": "/"}}, "/8800": {"target": "https://www.taobao.com/api", "changeOrigin": true, "pathRewrite": {"^/8800": ""}}}}, "async": {"loading": "AsyncLoading", "error": "AsyncError", "delay": 200, "timeout": 10000}, "uniStatistics": {"enable": true}, "title": "四川至简智印-工程师平台", "sdkConfigs": {"maps": {"qqmap": {"key": "SXEBZ-CKGCU-5HFVN-GHNV7-VTJ5J-ADBQB"}}}, "domain": "h5.jxbcn.com"}, "mp-qq": {"setting": {"minified": true}, "appid": "", "uniStatistics": {"enable": true}}, "uniStatistics": {"enable": true, "version": "2"}, "mp-alipay": {"uniStatistics": {"enable": true}}, "mp-baidu": {"uniStatistics": {"enable": true}}, "mp-toutiao": {"uniStatistics": {"enable": true}}}