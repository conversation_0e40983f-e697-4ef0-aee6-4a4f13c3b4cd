import {
	http
} from "@/utils/request/service";

/**
 * @description 客户信息
 */

// ======================== 客户列表 =========================

// 客户列表查询（工程师服务的客户）
export async function getCustomersListApi(params) {
	let url = `/api/customer/service-list`
	return await http.post(url, params)
}

// 获取字典数据
export async function dictTreeByCodeApi(code) {
	let url = `/api/magina/api/code/dict-item/${code}/tree`
	return await http.get(url)
}
// 获取客户等级
export async function getCustomerLevelApi() {
	let url = `/api/customer/member-level`
	return await http.get(url)
}
// 关联集团
export async function getCustomerGroupApi() {
	let url = `/api/customer-group/list`
	return await http.get(url)
}
// 新增客户
export async function addCustomerApi(params) {
	let url = `/api/customer`
	return await http.post(url, params)
}
// 获取客户明细
export async function getCustomerDetail(id) {
	let url = `/api/wechat/customer/${id}`
	return await http.get(url)
}
// 更新客户信息
export async function updateCustomerInfo(params) {
	let url = `/api/wechat/customer/update`
	return await http.put(url, params)
}
// 获取地区信息
export async function getAreaInfo(id) {
	let url = `/api/region/tree`
	return await http.get(url)
}


// ======================= 客户设备 =========================

// 获取客户设备列表
export async function getCustomerDeviceList(params) {
	let url = `/api/customer-device-group/page`
	return await http.get(url, params)
}
// 获取客户设备组列表--下拉
export async function getCustomerDeviceGroupList(customerId) {
	let url = `/api/customer-device-group/group-name-drop-down/${customerId}`
	return await http.get(url)
}
// 机型搜索
export async function getCustomerDeviceModelList(params) {
	let url = `/api/product-tree/model-page`
	return await http.get(url, params)
}

// =================== 设备组 =========================

// 添加设备组
export async function addCustomerDeviceGroup(params) {
	let url = `/api/customer-device-group`
	return await http.post(url, params)
}

// 禁用设备
export async function updateDeviceStatus(params) {
	let url = `/api/customer-device-group/updateStatus`;
	return await http.post(url, params);
}
// 获取设备详情
export async function getCustomerDeviceDetail(id) {
	let url = `/api/customer-device-group/detail/${id}`;
	return await http.get(url);
}
// 更新设备组信息
export async function updateCustomerDeviceInfo(params) {
	let url = `/api/customer-device-group`;
	return await http.put(url, params);
}

// 工程师列表
export async function roleMemberApi(id, params) {
	let url = `/api/magina/manage/user-role/member-page/${id}`;
	return await http.get(url, params);
}
// ===============  客户设备信息 ================
// 维修记录
export async function getCustomerDeviceRepairList(params) {
	let url = `/api/work-order/mechinePage`
	return await http.post(url, params)
}

// =============== 客户员工信息 ================

// 客户员工列表
export async function customerMemberApi(params) {
	let url = `/api/customer-staff/page`;
	return await http.get(url, params);
}
// 新增客户员工
export async function addCustomerMemberApi(params) {
	let url = `/api/customer-staff`;
	return await http.post(url, params);
}
// 编辑客户员工
export async function editCustomerMemberApi(params) {
	let url = `/api/customer-staff`;
	return await http.put(url, params);
}
// 删除员工
export async function deleteCustomerMemberApi(id) {
	let url = `/api/customer-staff/${id}`;
	return await http.delete(url);
}

// ============ 客户商务信息 ================

// 获取客户商务信息
export async function getCustomerBusinessInfo(id) {
	let url = `/api/customer-business/info/${id}`;
	return await http.get(url);
}
// 更新客户商务信息
export async function updateCustomerBusinessInfo(params) {
	let url = `/api/customer-business`;
	return await http.put(url, params);
}
// 根据编码查询角色信息
export async function getRoleByCode(code, data) {
	let url = `/api/customer/${code}/page`;
	return await http.get(url, data);
}

// =============== 物联网设置 =================

// 获取物联网设置
export async function getCustomerIotSetting(id) {
	let url = `/api/iotTimeConfig/${id}`;
	return await http.get(url);
}
// 更新物联网设置
export async function updateCustomerIotSetting(params) {
	let url = `/api/iot-time-config`;
	return await http.put(url, params);
}

// =============== 用户标签 =================
// 获取统计用户数据
export async function getCustomerStatistics(customerId) {
	let url = `/api/customer-tag-properties/statistics/${customerId}`
	return await http.get(url)
}
// 获取用户标签信息
export async function getUserTagInfo(customerId) {
	let url = `/api/customer-tag-properties/info/${customerId}`
	return await http.get(url)
}
// 更新用户标签
export async function updateUserTag(params) {
	let url = `/api/customer-tag-properties`
	return await http.post(url, params)
}

// =============== 拜访记录 =================
// 获取拜访记录
export async function getVisitRecordApi(params) {
	let url = `/api/customer-call-record/page`;
	return await http.post(url, params);
}
// 新增拜访记录
export async function addVisitRecordApi(params) {
	let url = `/api/customer-call-record`;
	return await http.post(url, params);
}
// 编辑拜访记录
export async function editVisitRecordApi(params) {
	let url = `/api/customer-call-record`;
	return await http.put(url, params);
}
// 删除拜访记录
export async function deleteVisitRecordApi(id) {
	let url = `/api/customer-call-record/${id}`;
	return await http.delete(url);
}
// 获取员工列表
export async function getStaffListApi(customerId) {
	let url = `/api/customer-staff/list/${customerId}`;
	return await http.get(url);
}

// ===================  购买意向  =====================
// 获取购买意向列表
export async function getCustomerIntentionApi(params) {
	let url = `/api/customer-intention/pageList`;
	return await http.post(url, params);
}
// 新增购买意向
export async function addCustomerIntentionApi(params) {
	let url = `/api/customer-intention`;
	return await http.post(url, params);
}
// ============== 合约记录  =================
// 列表
export async function getContractRecordApi(params) {
	let url = `/api/customer-contract/pageList`;
	return await http.post(url, params);
}
// 添加
export async function addContractRecordApi(params) {
	let url = `/api/customer-contract`;
	return await http.post(url, params);
}
// 获取价格列表
export async function getPriceListApi(params) {
	let url = `/api/repairMonthlyPrice/getAll`;
	return await http.get(url, params);
}

// 获取客户价值
export async function getCustomerValueApi(id) {
	let url = `/api/customer/customer-value/${id}`;
	return await http.get(url);
}

// =========================  访问记录  ========================
// 获取访问记录
export async function getCustomerVisitRecordApi(params) {
	let url = `/api/pc/customer-page-view/page`;
	return await http.post(url, params);
}
// ========================  搜索记录  ========================
// 获取搜索记录
export async function getCustomerSearchRecordApi(params) {
	let url = `/api/customer-search-out/page`;
	return await http.post(url, params);
}

// =======================  耗材仓库  ==========================
// 获取客户领取耗材列表
export async function getCustomerMaterialListApi(params) {
	let url = `/api/customerItemStore/page`;
	return await http.post(url, params);
}
// 获取客户用料记录
export async function getCustomerMaterialUseListApi(params) {
	let url = `/api/statisics/usedDetailPage`;
	return await http.post(url, params);
}
// 获取客户入库记录
export async function getCustomerMaterialInListApi(params) {
	let url = `/api/customerItemStore/logPageList`;
	return await http.post(url, params);
}

// ========================  领料审核  =========================
// 获取客户领取耗材列表
export async function getCustomerMaterialAuditListApi(params) {
	let url = `/api/operator/trade-order/page`;
	return await http.get(url, params);
}
// 获取用户退回耗材列表
export async function getCustomerMaterialReturnListApi(params) {
	let url = `/api/reverseManager/detailPage`;
	return await http.post(url, params);
}
// 查看审核订单信息
export async function getCustomerMaterialAuditDetailApi(orderNum) {
	let url = `/api/operator/trade-order/detail/${orderNum}`;
	return await http.get(url);
}
// 审核订单
export async function auditCustomerMaterialApi(params) {
	let url = `/api/operator/trade-order/audit`;
	return await http.put(url, params);
}