/*
 * @Description:
 * @Autor: shh
 * @Date: 2023-11-21 16:15:57
 * @LastEditors: yangzhong
 * @LastEditTime: 2023-11-29 22:02:08
 */

// 订单详情
export const orderdetail = '/api/mall/trade-order/detail/'
// 预支付订单
export const payPreview = '/api/mall/trade-order/payPreview/'
// 订单取消
export const orderClose = '/api/mall/trade-order/close'
//订单创建
export const orderCreate = '/api/mall/trade-order/create'
//订单预览
export const orderPreview = '/api/mall/trade-order/preview'
//订单收货
export const orderReceived = '/api/mall/trade-order/received/'
// 申请售后
export const orderReverse = '/api/reverse-order/appReverseOrder'
// 填写退货物流信息
export const fillInReturnOrder = '/api/reverse-order/fillInReturnOrder'

// 售后详情
export const getOne = '/api/reverse-order/getOne'

// 撤销申请
export const orderRevoke = '/api/reverse-order/appRevoke'

//  售后详情
export const ordergetOne = '/api/reverse-order/getOne'

// 订单数量查询
export const orderCount = '/api/mall/trade-order/sumaryCount'

// 小程序-知识库检索
export const learnPage = '/api/knowledge-base-info/search'
// 热词搜索下拉列表查询
export const learnWords = '/api/knowledge-hot-words/list'
// 小程序-知识库详情
export const learnAppletDetail = '/api/knowledge-base-info/appletDetail/{id}'
// 使用帮助列表查询
export const learnHelp = '/api/knowledge-use-help/list'
// 获取当前登录人所在店铺设备组适配机型
export const getCurrentModel = '/api/knowledge-base-info/getCurrentModel'
// 耗材仓库列表
export const customerItemStore = '/api/customerItemStore/list'
// 基础信息分页查询【工程师】
export const engineerPage = '/api/knowledge-base-info/engineerPage'