/*
 * @Description: 
 * @Autor: shh
 * @Date: 2023-03-13 09:33:58
 * @LastEditors: shh
 * @LastEditTime: 2023-05-02 10:28:54
 */
import {
	http
} from '@/utils/request/service';

// 根据字典类型查询字典数据信息
export async function getDicts(dictType) {
	let params = {};
	let url = '/system/dict/data/type/' + dictType;
	return await http.apiCall('get', url, params);
}

// 根据字典类型查询字典数据信息
export async function getRegions() {
	let nowDay = new Date().getDate();
	let regionDay = uni.getStorageSync("regionDay");
	if (nowDay === regionDay) {
		return uni.getStorageSync("regionData");
	} else {
		let res = await http.apiCall('get', '/queryRegionMenu', {});
		uni.setStorageSync("regionData", res);
		uni.setStorageSync("regionDay", nowDay);
		return res
	}
}

// 根据字典类型查询字典数据信息
export async function getSimpleRegions() {
	let nowDay = new Date().getDate();
	let regionDay = uni.getStorageSync("regionSimpleDay");
	if (nowDay === regionDay) {
		return uni.getStorageSync("simpleRegionData");
	} else {
		let res = await http.apiCall('get', '/querySimpleRegionMenu', {});
		uni.setStorageSync("simpleRegionData", res);
		uni.setStorageSync("regionSimpleDay", nowDay);
		return res
	}
}

/**
 * 出库管理
 */

// 获取出库列表
export async function getOutWarehouseList(params) {
	let url = `/api/storage-out-warehouse/pageWechart`;
	return await http.post(url, params);
}
// 获取工作人员列表
export async function getWorkerList(params) {
	let url = `/api/magina/api/user/list`;
	return await http.get(url, params);
}

// 获取商品批次号
export async function getBatchNo(params) {
	let url = `api/storage-out-warehouse/batchList`;
	return await http.get(url, params);
}
// 出库审核
export async function outWarehouseAudit(params) {
	let url = `/api/storage-out-warehouse`;
	return await http.put(url, params);
}

// ==========================  问题商品  =========================
// 新增问题商品
export async function addProblemGoods(params) {
	let url = `/api/problems/batch`;
	return await http.post(url, params);
}
// 获取问题商品添加记录
export async function getProblemGoodsList(params) {
	let url = `/api/problems/page`;
	return await http.post(url, params);
}