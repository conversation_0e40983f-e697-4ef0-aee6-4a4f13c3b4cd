/*
 * @Description:
 * @Autor: shh
 * @Date: 2023-03-13 09:33:58
 * @LastEditors: shanhaihong <EMAIL>
 * @LastEditTime: 2024-01-10 15:19:44
 */
//获取小程序openId
const getOpenId = "/api/wechat/staff/authorize";
// 小程序登录
const mpWechatLogin = "/api/wechat/staff/login";
//验证码生成
const captcha = "/api/magina/anno/captcha";
//生成加密令牌及公钥
const key = "/api/magina/anno/key";
// 退出登录
const logout = "/api/wechat/staff/logout";

// 刷新token
const refreshToken = "/tiny-shop/v1/site/refresh";

// 登录令牌有效性检测
const verifyAccessToken = "/api/wechat/customer/check";

//省市区
const regionTree = "/api/region/tree";

// 完善店铺信息
const customer = "/api/wechat/customer/fill";

// 角色
const roles = `/api/magina/api/code/dict-item/${500}/tree`;
const resources = "/api/magina/system/resources";
export {
  getOpenId,
  mpWechatLogin,
  logout,
  captcha,
  key,
  refreshToken,
  verifyAccessToken,
  regionTree,
  customer,
  roles,
  resources,
};
