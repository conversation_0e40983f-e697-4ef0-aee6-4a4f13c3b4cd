/*
 * @Description:
 * @Autor: shh
 * @Date: 2023-03-13 09:33:58
 * @LastEditors: shanhaihong <EMAIL>
 * @LastEditTime: 2024-01-17 16:30:18
 */
import indexConfig from "@/config/index.config";

export const topHeader = " ";

export const PATH = indexConfig.baseUrl;

//获得可用店铺列表
export const shopInfo = "/api/wechat/customer";

// =======展示信息========
// 轮播图
export const bannerList = "/wxlite/v1/banner/list";
// 商品列表
export const goodsListApi = "/api/wechat/shop/page";
// 商品详情
export const goodsDetail = `/api/wechat/shop/detail/`;

// ============入驻============
export const applyStore = "/wxlite/v1/store/"; //门店入驻
export const applyAgent = "/wxlite/v1/staff/"; //经纪人入驻
// =========关注============
//关注列表
export const followList = "";
//关注 / 取关
export const follow = "";
// 查看是否关注了当前
export const isfollow = "";

export const wxUser = ""; //更新个人信息

// ===========订单============
export const orderList = "/api/mall/trade-order/page";

// 分类列表
export const classifyList = "/api/commodity-classify/page";
// 分类查询tag
export const classifyTag = "/api/wechat/shop/modelConditions";

//  ===========售后============
// app端-售后分页查询
export const afterSaleApi = "/api/reverse-order/appPage";

// ===========购物车============
export const cartList = "/api/applet/cart/page";
export const addCartApi = "/api/applet/cart/add";
export const deleteCart = "/api/applet/cart/delete";
export const updateCart = "/api/applet/cart/setNum";
export const addNumberCart = "/api/applet/cart/increaseNum";
export const decreaseNumCart = "/api/applet/cart/decreaseNum";
export const selectCart = "/api/applet/cart/multi-select";

// 员工管理
export const customersStaffPage = "/api/customer-staff/page"; // 列表数据
export const customersStaff = "/api/customer-staff"; // 新增
// 设备管理
export const customerDeviceGoupPage = "/api/customer-device-group/page"; // 列表数据
export const customerDeviceGoup = "/api/customer-device-group"; // 新增
export const customerAllTree = "/api/product-tree/all-tree"; // 关联品牌树
export const customerModelPage = "/api/product-tree/model-page"; // 机型搜索
export const customerUpdateStatus = "/api/customer-device-group/updateStatus"; // 机型搜索

// 机型列表
export const brandSerialList = "/api/engineerItemStore/brand-serial-list";

export const brandModelList = "/api/product-tree/model-page"; // 机型搜索

// 分类单元
export const getFilterData = "/api/engineerItemStore/getFilterData";
// 耗材列表
export const consumablesList = "/api/wechat/shop/consumables-page";
// 耗材领用清单
export const applyInventoryList = "/api/wechat/shop/consumables-sku";
// 领用耗材
export const applyWare = "/api/applyOrder/apply";

// 耗材记录列表
export const historyList = "/api/applyOrder/listPage";
// 取消领取耗材
export const cancelApply = "/api/applyOrder/cancelDetail";

// 可退料的列表
export const listUserId = "/api/engineerItemStore/listUserId";
export const returnItemStore = "/api/applyOrder/returnItemStore";

export const returnItemStorelist = "/api/applyOrder/returnItemStore/list";
//二维码扫描结果
export const scanQr = "/api/qr-app/scanQr";
//二维码扫描选择店铺
export const selectStore = "/api/qr-app/selectStore";