{"description": "项目配置文件，详见文档：%s", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": true, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": false, "ignoreDevUnusedFiles": false, "minifyWXML": true, "condition": false}, "compileType": "miniprogram", "libVersion": "2.31.0", "appid": "wxe24de6b28f7abee4", "projectname": "appdemo", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}