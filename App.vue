<!--
 * @Description: 
 * @Autor: shh
 * @Date: 2023-03-13 09:33:58
 * @LastEditors: shanhaihong <EMAIL>
 * @LastEditTime: 2024-01-05 13:36:02
-->
<script>
/* eslint-disable */
import Vue from 'vue';
import { mapMutations } from 'vuex';
import indexConfig from '@/config/index.config';
import { getOpenId, verifyAccessToken } from '@/api/login';

export default {
	async onLaunch() {
		await this.initData();
	},
	methods: {
		...mapMutations(['setCartNum', 'setNotifyNum']),
		// 数据初始化
		async initData() {
			// 获取页面设置配置
			const token = uni.getStorageSync('accessToken');
			// 获取系统title高度
			await this.initSystemInfo();
			// if (token) {
			this.handleVerifyAccessToken();

			// }
			// setInterval(() => {
			// 	debugger
			// 	let time = uni.getStorageSync("time");
			// 	time--
			// 	uni.setStorageSync("time", time);
			// 	if (time == 0) {
			// 		this.handleVerifyAccessToken();
			// 	}
			// }, 1)
			// 170分钟
			// setInterval(() => {
			// 	this.handleVerifyAccessToken();
			// }, 170 * 60 * 1000)
			if (this.$mStore.getters.hasLogin) {
				// 初始化购物车数量
				// this.setCartNum(uni.getStorageSync("cartNum") || 0);
				// this.setNotifyNum(uni.getStorageSync("notifyNum") || 0);
			}

			this.jxb.getRealNearestRegion();
		},
		// 初始化系统信息
		initSystemInfo() {
			uni.getSystemInfo({
				success(e) {
					// #ifdef MP-WEIXIN
					Vue.prototype.StatusBar = e.statusBarHeight;
					// eslint-disable-next-line
					const custom = wx.getMenuButtonBoundingClientRect();
					Vue.prototype.Custom = custom;
					Vue.prototype.CustomBar = custom.top - e.statusBarHeight;
					// #endif
				}
			});
		},
		// 检验token是否有效
		handleVerifyAccessToken() {
			this.$http.post(this.$mHelper.verifyAccessToken).then((res) => {
				if (res.data === 2) {
					this.$mHelper.relogin();
				}
			});
		}
	}
};
</script>
<style lang="scss">
page {
	min-height: 100vh;
	background: #f5f6f8;
}

@import '/static/css/colorui/main.css';
@import './static/css/reset.scss';
@import './static/css/uni.scss';
@import './static/css/app.css';
@import './static/css/icon.css';
@import './static/css/icon1.css';
</style>
