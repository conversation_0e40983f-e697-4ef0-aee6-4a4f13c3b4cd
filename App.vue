<!--
 * @Description: 
 * @Autor: shh
 * @Date: 2023-03-13 09:33:58
 * @LastEditors: shan<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-05 13:36:02
-->
<script>
/* eslint-disable */
import Vue from 'vue';
import { mapMutations } from 'vuex';
import { getOpenId, verifyAccessToken } from '@/api/login';

export default {
	async onLaunch() {
		this.autoUpdate();
		await this.initData();
	},
	methods: {
		...mapMutations(['setCartNum', 'setNotifyNum']),
		// 数据初始化
		async initData() {
			// 获取页面设置配置
			const token = uni.getStorageSync('accessToken');
			// 获取系统title高度
			await this.initSystemInfo();
			// if (token) {
			this.handleVerifyAccessToken();
			// if (this.$mStore.getters.hasLogin) {
			// }

			this.jxb.getRealNearestRegion();
		},
		// 初始化系统信息
		initSystemInfo() {
			uni.getSystemInfo({
				success(e) {
					// #ifdef MP-WEIXIN
					Vue.prototype.StatusBar = e.statusBarHeight;
					// eslint-disable-next-line
					const custom = wx.getMenuButtonBoundingClientRect();
					Vue.prototype.Custom = custom;
					Vue.prototype.CustomBar = custom.top - e.statusBarHeight;
					// #endif
				}
			});
		},
		autoUpdate() {
			// 获取小程序更新机制兼容
			if (uni.canIUse('getUpdateManager')) {
				const updateManager = uni.getUpdateManager();
				updateManager.onCheckForUpdate((res) => {
					if (res.hasUpdate) {
						uni.showModal({
							title: '更新提示',
							content: '检测到新版本，是否下载新版本并重启小程序？',
							success: (res) => {
								if (res.confirm) {
									downLoadAndUpdate(updateManager);
								} else if (res.cancel) {
									//用户点击取消按钮的处理。
									// uni.showModal({
									// 	title: '温馨提示',
									// 	content: '本次版本更新涉及到新的功能添加，旧版本无法正常访问的哦',
									// 	showCancel: false,
									// 	confirmText: '确定更新',
									// 	success: (res) => {
									// 		if (res.confirm) {
									// 			downLoadAndUpdate(updateManager);
									// 		}
									// 	}
									// });
								}
							}
						});
					}
				});
			} else {
				uni.showModal({
					title: '提示',
					content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
				});
			}
		},
		downLoadAndUpdate(updateManager) {
			updateManager.onUpdateReady(() => {
				uni.showModal({
					title: '更新提示',
					content: '新版本已经准备好，请重新登录小程序',
					success: (res) => {
						if (res.confirm) {
							// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
							// updateManager.applyUpdate();
							this.$mStore.commit('login', {});
							uni.removeStorageSync('accessToken');
							uni.removeStorageSync('powerMenu');
							uni.removeStorageSync('customerId');
							uni.removeStorageSync('refreshToken');
							uni.removeStorageSync('userInfo');
							uni.removeStorageSync('systemMenu');
							uni.removeStorageSync('permits');
							this.$mRouter.reLaunch({
								route: '/pages/user/login'
							});
						}
					}
				});
			});

			// 更新失败回调
			updateManager.onUpdateFailed(() => {
				uni.showModal({
					title: '已经有新版本了哟~',
					content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~'
				});
			});
		},
		// 检验token是否有效
		handleVerifyAccessToken() {
			this.$http.post(this.$mHelper.verifyAccessToken).then((res) => {
				if (res.data === 2) {
					this.$mHelper.relogin();
				}
			});
		}
	}
};
</script>
<style lang="scss">
page {
	// min-height: 100vh;
	background: #f5f6f8;
}

@import '/static/css/colorui/main.css';
@import './static/css/reset.scss';
@import './static/css/uni.scss';
@import './static/css/app.css';
@import './static/css/icon.css';
@import './static/css/icon1.css';
</style>
