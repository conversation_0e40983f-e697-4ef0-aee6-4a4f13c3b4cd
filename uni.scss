@import 'uview-ui/theme.scss';
/* 页面左右间距 */
$spacing-lg: 30upx;
$spacing-base: 20upx;
$spacing-sm: 10upx;

$color-white: #fff;
$color-black: #000;

$page-row-spacing: 30upx;
$page-color-base: #f8f8f8;
$page-color-light: #f8f6fc;
$base-color: #ee822f;

/* 文字尺寸 */
$font-sm: 24upx;
$font-base: 28upx;
$font-lg: 32upx;

/*文字颜色*/
$font-color-dark: #303133;
$font-color-base: #606266;
$font-color-light: #909399;
$font-color-disabled: #c0c4cc;
$font-color-spec: #4399fc;

/* 边框颜色 */
$border-color-dark: #dcdfe6;
$border-color-base: #e4e7ed;
$border-color-light: #ebeef5;

/* 图片加载中颜色 */
$image-bg-color: #eee;

/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 主要颜色 */
$base: #ee822f; // 基础颜色
$assist-clor: #ff4e17; // 辅助颜色
$change-clor: #f5c744; // 渐变色
$floor-clor: #fafafa; // 底部颜色
$price-clor: #ff0000; // 价格颜色
$rgba-01: rgba(233, 59, 61, 0.1); // 基础色透明度0.1
$rgba-02: rgba(233, 59, 61, 0.2); // 基础色透明度0.2
$rgba-03: rgba(233, 59, 61, 0.3); // 基础色透明度0.3
$rgba-04: rgba(233, 59, 61, 0.4); // 基础色透明度0.4
$rgba-05: rgba(233, 59, 61, 0.5); // 基础色透明度0.5
$rgba-06: rgba(233, 59, 61, 0.6); // 基础色透明度0.6
$rgba-07: rgba(233, 59, 61, 0.7); // 基础色透明度0.7
$rgba-08: rgba(233, 59, 61, 0.8); // 基础色透明度0.8

/* 文字基本颜色 */
$uni-text-color: #333; //基本色
$uni-text-color-inverse: #fff; //反色
$uni-text-color-grey: #999; //辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

/* 背景颜色 */
$uni-bg-color: #ffffff;
$uni-bg-color-grey: #f8f8f8;
$uni-bg-color-hover: #f1f1f1; //点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); //遮罩颜色

/* 边框颜色 */
$uni-border-color: #c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 24upx;
$uni-font-size-base: 28upx;
$uni-font-size-lg: 32upx;

/* 图片尺寸 */
$uni-img-size-sm: 40upx;
$uni-img-size-base: 52upx;
$uni-img-size-lg: 80upx;

/* Border Radius */
$uni-border-radius-sm: 4upx;
$uni-border-radius-base: 6upx;
$uni-border-radius-lg: 12upx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10px;
$uni-spacing-row-base: 20upx;
$uni-spacing-row-lg: 30upx;

/* 垂直间距 */
$uni-spacing-col-sm: 8upx;
$uni-spacing-col-base: 16upx;
$uni-spacing-col-lg: 24upx;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-font-size-title: 40upx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle: 36upx;
$uni-color-paragraph: #3f536e; // 文章段落颜色
$uni-font-size-paragraph: 30upx;
